// plugins/server-logger.js

interface ServerLogger {
    log: (...args: any) => void;
    error: (...args: any) => void;
    warn: (...args: any) => void;
}
export default defineNuxtPlugin((nuxtApp) => {
    const serverLog: ServerLogger = {
        log: (...args: any) => {
            if (process.server) {
                console.info(`INFO: `, ...args);
            }
        },
        error: (...args: any) => {
            if (process.server) {
                console.error(`ERROR: `, ...args);
            }
        },
        warn: (...args: any) => {
            if (process.server) {
                console.warn(`WARNING: `, ...args);
            }
        },
    };

    // 将 serverLog 挂载到 Nuxt 应用的全局属性中
    nuxtApp.provide("serverLog", serverLog);
});
