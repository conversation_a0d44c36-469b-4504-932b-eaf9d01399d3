<template>
    <!-- big size -->
    <div class="header header_bg_color" :class="{ fixed: menuFixed, [headerBgColor]: headerBgColor ? true : false }">
        <div class="header_content">
            <div class="c_logo" @click="goToPage('main', 'www')"></div>
            <div class="menu">
                <div class="li" :class="{ active: route.path.indexOf('/main') > -1 }" @click="goToPage('main', 'www')">
                  {{ $t('pages.index.main') }}
                </div>
                <div class="li" :class="{ active: route.path.indexOf('/game/list') > -1 }" @click="goToPage('game/list', 'www')">
                  {{ $t('pages.index.game.list') }}
                </div>
                <div class="li" :class="{ active: route.path.indexOf('/home') > -1 }" @click="goToPage('home', 'payment')">
                  {{ $t('pages.index.pay') }}
                </div>
                <div class="li" :class="{ active: route.path.indexOf('/support') > -1 }" @click="goToPage('support', 'payment')">
                  {{ $t('pages.index.support') }}
                </div>
                <div class="li" :class="{ active: route.path.indexOf('/about/company') > -1 }" @click="goToPage('about/company', 'www')">
                  {{ $t('pages.index.about.about') }}
                </div>
                <!-- <div class="li">{{ $t('menu.dashboard.pay') }}</div> -->
                <!-- <div class="li">充值</div>
                <div class="li">充值</div> -->
            </div>
            <div class="login_box" :class="{'dn':route.name == 'lang-account-name'}">
                <div v-show="!isLogin">
                    <Login></Login>
                </div>
                <a-dropdown :trigger="['click']" v-if="isLogin">
                    <a class="ant-dropdown-link account_drop_btn" @click.prevent>
                        <span style="margin-right:10px; color: #fff;">{{ $t('pages.pay.Hello') }}{{ user }}</span>
                        <DownOutlined style="color: #fff; margin-right: 10px;" />
                    </a>
                    <template #overlay>
                        <a-menu>
                            <a-menu-item @click="goToPage('account/profile', 'account')">{{ $t('pages.pay.Account-Center') }}</a-menu-item>
                            <!-- <a-menu-item @click="goToPage('account/mylist')">{{ $t('menu.dashboard.order-list') }}</a-menu-item>
                            <a-menu-item @click="goToPage('account/security')">安全管理</a-menu-item> -->
                            <a-menu-item @click="logOut">{{ $t('menu.account.logout') }}</a-menu-item>
                        </a-menu>
                    </template>
                </a-dropdown>
            </div>
        </div>
    </div>

    <!-- small size -->
    <div class="header_m header_bg_color" :class="{ fixed: menuFixed, [headerBgColor]: headerBgColor }">
        <div class="c_logo"></div>
        <div class="c_icon_user" @click="popNavRightCtrl = !popNavRightCtrl; popNavLeftCtrl = false"></div>
    </div>
    <!-- overlay -->
    <div class="h_pop_overlay" v-if="popNavLeftCtrl || popNavRightCtrl"
        @click="popNavLeftCtrl = false; popNavRightCtrl = false"></div>
    <!-- nav -->
    <!-- 左边菜单 -->
    <div class="h_pop_nav left" :class="{ active: popNavLeftCtrl }">
        <div class="top_header">
            <img src="~/assets/images/common/play_best_blue.png" alt="PLAY BEST" />
        </div>
        <div class="item" :class="{ active: route.path.indexOf('/main') > -1 }" @click="goToPage('main', 'www')">
            {{ $t('pages.index.main') }}
        </div>
        <div class="item" :class="{ active: route.path.indexOf('/game/list') > -1 }" @click="goToPage('game/list', 'www')">
            {{ $t('pages.index.game.list') }}
        </div>
        <div class="item" :class="{ active: isMenuHome }" @click="goToPage('home', 'payment')">
            {{ $t('pages.index.pay') }}
        </div>
        <div class="item" :class="{ active: route.path.indexOf('/support') > -1 }" @click="goToPage('support', 'payment')">
            {{ $t('pages.index.support') }}
        </div>
        <div class="item" :class="{ active: route.path.indexOf('/about/company') > -1 }" @click="goToPage('about/company', 'www')">
            {{ $t('pages.index.about.about') }}
        </div>
    </div>
    <!-- 右边菜单 -->
    <div class="h_pop_nav right" :class="{ active: popNavRightCtrl }">
        <div class="menu-close active" id="menu-close" @click="popNavRightCtrl = !popNavRightCtrl">
            <div class="menu-close__burger"></div>
        </div>
        <div class="top_header">
            <img src="~/assets/images/common/play_best_blue.png" alt="PLAY BEST" />
        </div>
        <!-- login -->
        <div class="item" v-if="!isLogin && route.name != 'lang-account-name'" @click="showModal">
            <div class="c_icon c_icon_order"></div>
            <div class="txt">{{ $t('pages.pay.login') }}</div>
        </div>
        <template v-if="isLogin">
            <div class="item">
                {{ user }}
            </div>
            <div class="item" :class="{ active: isMenuAccountProfile }" @click="goToPage('account/profile', 'account')">
                <div class="c_icon c_icon_home"></div>
                <div class="txt">{{ $t('pages.pay.Account-Center') }}</div>
            </div>
            <!-- <div class="item" :class="{ active: isMenuAccountMylist }" @click="goToPage('account/mylist')">
                <div class="c_icon c_icon_order"></div>
                <div class="txt">{{ $t('menu.dashboard.order-list') }}</div>
            </div>
            <div class="item" :class="{ active: isMenuAccountSecurity }" @click="goToPage('account/security')">
                <div class="c_icon c_icon_home"></div>
                <div class="txt">安全管理</div>
            </div> -->

            <div class="item" @click="logOut">
                <div class="c_icon c_icon_logout"></div>
                <div class="txt">{{ $t('menu.account.logout') }}</div>
            </div>
        </template>
    </div>
    <!-- close -->
    <div class="menu-close" :class="{ active: popNavLeftCtrl }" id="menu-close"
        @click="popNavLeftCtrl = !popNavLeftCtrl; popNavRightCtrl = false">
        <div class="menu-close__burger"></div>
    </div>

</template>
<script lang="ts" setup>
const { locale } = useI18n();
// 判断哪些需要重定向
import { route_auth } from '~/constants/route_auth';
// 登录态管理
import { useAuthStore } from '~/stores/auth';
import { goToPageSwitch } from '~/utils/tools';
const authStore = useAuthStore()
const isLogin = computed(() => authStore.isLogin)
const user = computed(() => authStore.user);


// 设置 cookie
// const $token = useCookie("auth_token");
// const $user = useCookie("user");
//pop nav 控制器
const popNavLeftCtrl = ref(false);
const popNavRightCtrl = ref(false);
const menuFixed = ref(false)

// 登录弹窗控制器
const showModal = () => {
    authStore.setLoginModal(true);
    closeNavCtrl();
};
// 关闭左右弹窗
const closeNavCtrl = () => {
    popNavLeftCtrl.value = false;
    popNavRightCtrl.value = false;
}
// 链接跳转
const goToPage = (pathVal: string, host: string) => {
    goToPageSwitch(pathVal, host, locale.value);
    closeNavCtrl();
}

// 路由
const route = useRoute();
const router = useRouter();

// 修改同步 顶部颜色 配色
const headerBgColor = useState('headerBgColor', () => "")


// 我的订单判断
const isMenuAccountMylist = computed(() => {
    return route.path.indexOf('/account/mylist') > -1
})

// 账号中心判断
const isMenuAccountProfile = computed(() => {
    return route.path.indexOf('/account/profile') > -1
})

// 安全中心判断
const isMenuAccountSecurity = computed(() => {
    return route.path.indexOf('/account/security') > -1
})

// 当前是否是首页判断
const isMenuHome = computed(() => {
    return route.path.indexOf('/home') > -1
})

// 登出 
const logOut = () => {
    let callBack = null;
    // 路由auth 控制
    if ((route_auth as any)[route.name]) {
        callBack = () => {
            navigateTo(`/${locale.value}/account/login`);
            closeNavCtrl();
        }
    }
    authStore.logout(callBack);
    // 埋点: 移除userId
    YKTrack.setUserId('')
}

onMounted(() => {
    // 添加滚动监听
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            menuFixed.value = true
        } else {
            menuFixed.value = false
        }
    }, true);
});



</script>
<style lang="scss">
.header_bg_color {
    background: rgba(0, 0, 0, 0.3);

    &.fixed {
        background: rgba(0, 0, 0, 0.8);
    }

    &.blue {
        background: var(--base_color);
    }

    &.black {
        background: #111;
    }
}
</style>
<style lang="scss" scoped>
.c_logo {
    cursor: pointer;
}

.header {
    height: 60px;
    position: fixed;
    z-index: 990;
    width: 100%;
    left: 0;
    top: 0;
    transition: background-color 0.3s ease-in-out;
}

.account_drop_btn {
    line-height: 36px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.header_content {
    max-width: 1400px;
    width: 100%;
    padding: 0 20px;
    margin: 0 auto;
    line-height: 60px;
    display: flex;
    align-items: center;
    font-size: 16px;

    .logo {
        width: 135px;
        height: 34px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .menu {
        margin-left: 80px;
        display: flex;
        align-items: center;
        color: #fff;

        .li {
            padding: 0 10px;
            margin: 0 10px;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.active {
                position: relative;

                &::after {
                    content: "";
                    width: 100%;
                    position: absolute;
                    height: 2px;
                    background: #fff;
                    bottom: 0;
                    left: 0;
                    z-index: 9;
                }
            }
        }
    }

    .login_box {
        margin-left: auto;
        color: #fff;
        cursor: pointer;

        .loginin_bar {
            display: flex;
            align-items: center;

            img {
                width: 20px;
                height: 22px;
            }

            .txt {
                margin-left: 10px;
            }
        }
    }
}

.header_m {
    align-items: center;
    height: 50px;
    position: fixed;
    z-index: 990;
    width: 100%;
    left: 0;
    top: 0;
    display: none;
    justify-content: space-between;
    padding: 0 20px;
    transition: background-color 0.3s ease-in-out;

    &::before {
        content: "";
        width: 40px;
        height: 40px;
    }

    .logo {
        width: 100px;

        img {
            width: 100%;
        }
    }

    .loginin_bar {
        img {
            width: 20px;
            height: 22px;
        }
    }
}

.h_pop_overlay {
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 991;
    background: rgba(0, 0, 0, 0.8);
}

.h_pop_nav {
    width: 340px;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    background: #fff;
    z-index: 995;
    transition: all 0.3s ease-in-out;

    &.left {
        transform: translate(-100%, 0);

        &.active {
            transform: translate(0, 0);
        }
    }

    &.right {
        left: auto;
        right: 0;
        transform: translate(100%, 0);

        &.active {
            transform: translate(0, 0);
        }

        .menu-close {
            left: auto;
            position: absolute;
            right: 20px;
        }
    }

    .top_header {
        height: 52px;
        background: #F9F9FD;
        border-bottom: 2px solid #EAEAF8;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 100px;
        }
    }

    .item {
        display: flex;
        align-items: center;
        line-height: 50px;
        border-bottom: 2px solid #EAEAF8;
        padding-left: 32px;
        font-size: 18px;
        color: #000;
        position: relative;

        .c_icon {
            margin-right: 14px;
        }

        &.active {
            background: #f1f1f1;
        }

        &.active::after {
            content: "";
            left: 0;
            width: 2px;
            position: absolute;
            height: 100%;
            top: 0;
            background: var(--base_color);
        }
    }
}

// menu close bar
.menu-close {
    display: none;
    cursor: pointer;
    position: fixed;
    z-index: 998;
    top: 23px;
    left: 20px;
    $pop_nav_close_color: #7E7E7E;

    &.active .menu-close__burger {
        background-color: transparent;
    }

    &.active .menu-close__burger::before {
        transform: rotate(45deg);
        background-color: $pop_nav_close_color;
    }

    &.active .menu-close__burger::after {
        transform: rotate(-45deg);
        background-color: $pop_nav_close_color;
    }
}

.menu-close__burger {
    $pop_nav_close_w: 25px;
    width: $pop_nav_close_w;
    height: 3px;
    background-color: #fff;
    border-radius: 3px;
    position: relative;
    transition: all 0.3s ease-in-out;

    &::before,
    &::after {
        content: '';
        width: $pop_nav_close_w;
        height: 3px;
        background-color: #fff;
        border-radius: 3px;
        position: absolute;
        transition: all 0.3s ease-in-out;
    }

    &::before {
        transform: translateY(-9px);
    }

    &::after {
        transform: translateY(9px);
    }
}


:deep(.ant-dropdown-menu-title-content) {
    text-align: center;
    border-bottom: 1px solid #EAEAF8;

    &:last-child {
        border-bottom: none;
    }
}

@media screen and (max-width: 980px) {
    .header_content {
        .menu {
            margin-left: 20px;
        }
    }
}

@media screen and (max-width: 768px) {
    .header {
        display: none;
    }

    .header_m {
        display: flex;
    }

    .menu-close {
        display: block;
    }
}
</style>
