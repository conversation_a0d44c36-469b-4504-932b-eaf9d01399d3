## 3.6.5
1. 【新增】添加接口监控日志名单

## 3.6.4
1. 【新增】添加返回值console

## 3.6.3
1. 【修改】兼容性优化

## 3.6.2
1. 【修改】订单状态提示优化

## 3.6.1
1. 【新增】登录弹窗添加“注销拦截”

## 3.6.0
1. 【新增】登录窗口三方登录

## v3.5.2
1. 【新增】CODA 14种和adyen支付方式
2. 【修改】兼容性修改

## v3.5.1
1. 【修改】web-view 透明显示BUG

## v3.5.0
1. 【新增】客户端官包支付优化

## v3.4.4
1. 【修改】修复线上因为YK统计加载导致的BUG
2. 【修改】雷蛇图标

## v3.4.3
1. 【修改】增加一个webview logout方法

## v3.4.2
1. 【新增】大富翁日语主体相关修改
2. 【修改】兼容注销提示

## v3.4.0
1. 【新增】CODA 支付 + EVONET 支付
2. 【修改】点击统计修改

## v3.3.0
1. 【新增】内嵌账号升级

## v3.2.3
1. 【修改】robots.txt 和 sitemap.xml 的配置
2. 【修改】404 页面倒计时

## v3.2.2
1. 【新增】QIXI 支付宝HK+国内支付

## v3.2.1
1. 【修改】SEO优化 robots.txt
2. 【修改】SEO优化 sitemap.xml
3. 【修改】首页样式
4. 【修改】支付宝图标背景
5. 【修改】直接访问 Payment.playbest.net 跳转  /home
6. 【修改】直接访问 Account.playbest.net 跳转  /login

## v3.2.0
1. 【新增】主页，游戏页，关于页三件套
2. 【新增】增加www主站跳转

## v3.1.2
1. 【新增】支付宝HK支持

## v3.1.1
1. 【新增】图形验证码
2. 【优化】手机海外验证码
3. 【新增】雷蛇支付

## v3.1.0
1. 【新增】支付中心的数据上报
2. 【优化】账号视觉优化

## v3.0.0
1. 【新增】客服中心
2. 【新增】账号中心

## v2.4.0
1. 【新增】支持客户端web-view 的“支付功能”

## v2.3.3
1. 【修复】mycard BUG

## v2.3.2
1. 【新增】添加adyen 环境配置和样式修改

## v2.3.0 ~ v2.3.1
1. 【新增】支持了对活动商品添加图片标签，用于对活动商品的营销展示；
2. 【新增】支持了对活动商品进行“限时抢购”活动设置，配置后会在活动商品上显示剩余时间；
3. 【新增】对于商品新增了商品描述，可以用于礼包的详细描述，使玩家更加清晰的看到商品内容；
4. 【新增】支持了对活动商品的限购，超出购买限制数量后将置灰不允许继续进行下单；
5. 【新增】“独立站”功能上线，可在后台配置相应的独立站，单独显示配置商品以及站点图片等内容，支持个性配置；
6. 【新增】“Fun Tales”站点上线，对外发行主体为Fun Tales的游戏将会显示在对应站点上；
7. 【项目接入】Y-game东亚，上线支付中心区分为四个链接显示（日本、韩国、欧美、港台）
8. 【优化】用户体验以及页面优化调整。

## v2.2.2
adyen 支付接入

## v2.2.1
hotfix 修复mycard 手机端支付问题

## v2.2.0
功能迭代+支付框悬浮

## v2.1.0
接入X2 + 低版本兼容 Android 7+

## v2.0.0 
对齐版本，客户端打开自动进行角色登录

## v1.1.0
接入 雷蛇和街口 支付

## v1.0.0
全站完成，包含PayPal mycard evonet

## v0.2.0
项目启动


## v0.0.1
基于nuxt  SSR项目初始化