<template>
    <div class="account_wrap">
        <accountSidebar :index="0" ref="accountSidebarRef"></accountSidebar>
        <!-- 右边区域 -->
        <div class="account_right">
            <div class="h2">{{ $t('account.profile.accountInformation') }}</div>
            <accountHint></accountHint>
            <!-- 账号信息 -->
            <div class="ac_common_box ac_c_mb20 ac_info_box ac_c_r20">
                <div class="ac_header">{{ $t('account.profile.accountInformation') }}</div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.profile.nickname') }}</div>
                            <div class="row_2">
                                {{ userInfo.nick_name || $t('account.profile.notSet') }}
                            </div>
                        </div>
                        <div class="row_right">
                            <div
                                @click="userInfo.is_edit_nickname ? editType = 'nickname' : null"
                                class="ac_common_btn"
                                :class="{
                                    disabled: !userInfo.is_edit_nickname,
                                }"
                            >
                                {{ $t('account.profile.edit') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.profile.accountName') }}</div>
                            <div class="row_2">
                                {{ userInfo.username || $t('account.profile.notSet') }}
                            </div>
                        </div>
                        <div class="row_right" v-if="userInfo.username">
                            <check-circle-filled
                                style="color: #14ac71; font-size: 16px"
                            />
                            <div class="success_txt">{{ $t('account.profile.bound') }}</div>
                        </div>
                        <div class="row_right" v-else>
                            <div class="ac_common_btn" @click="operation = 4">{{ $t('account.profile.bind') }}</div>
                        </div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t("account.profile.accountID") }}</div>
                            <div class="row_2">{{ userInfo.user_id }}</div>
                        </div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.profile.verifyEmail') }}</div>
                            <div class="row_2">
                                {{ userInfo.email || $t('account.profile.notBound') }}
                            </div>
                        </div>
                        <div class="row_right">
                            <div class="ac_common_btn" @click="operation = 24" v-if="userInfo.email">{{ $t('account.profile.changeBind') }}</div>
                            <div class="ac_common_btn" @click="operation = 4" v-else>{{ $t('account.profile.bind') }}</div>
                        </div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{$t('account.profile.phoneNumber')}}</div>
                            <div class="row_2">
                                {{ userInfo.phone || $t('account.profile.notBound') }}
                            </div>
                        </div>
                        <div class="row_right">
                            <div class="ac_common_btn" @click="operation = 23" v-if="userInfo.phone">{{ $t('account.profile.changeBind') }}</div>
                            <div class="ac_common_btn" @click="operation = 3" v-else>{{ $t('account.profile.bind') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人信息 -->
            <div class="ac_common_box ac_c_mb20 ac_person ac_c_r20">
                <div class="ac_header">{{ $t('account.personalInfo.title') }}</div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.personalInfo.name') }}</div>
                            <div class="row_2">
                                {{ userInfo.first_name}}   {{ userInfo.last_name || $t('account.personalInfo.notSet') }}
                            </div>
                        </div>
                        <div class="row_right" @click="editType = 'personalInfo'"><span class="span_edit_btn">{{ $t('account.personalInfo.modify') }} &gt;</span></div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.personalInfo.birthday') }}</div>
                            <div class="row_2">
                                {{ userInfo.birth_date || $t('account.personalInfo.notSet') }}
                            </div>
                        </div>
                        <div class="row_right" @click="editType = 'personalInfo'" v-if="userInfo.is_edit_birth"><span class="span_edit_btn">{{ $t('account.personalInfo.modify') }} &gt;</span></div>
                        <div class="row_right" v-else><span class="span_edit_btn" style="color: #999; cursor: not-allowed;">{{ $t('account.personalInfo.modify') }} &gt;</span></div>
                    </div>
                </div>
                <div class="line">
                    <div class="line_item">
                        <div class="row">
                            <div class="row_1">{{ $t('account.personalInfo.gender') }}</div>
                            <div class="row_2">
                                {{ userInfo.genderName || $t('account.personalInfo.notSet') }}
                            </div>
                        </div>
                        <div class="row_right" @click="editType = 'personalInfo'"><span class="span_edit_btn">{{ $t('account.personalInfo.modify') }} &gt;</span></div>
                    </div>
                </div>
            </div>
            <!-- 安全检查 -->
            <div class="ac_common_box ac_c_mb20 ac_security ac_c_r20">
                <div class="ac_header">{{ $t('account.securityCheck.title') }}</div>
                <div class="progress_wrap">
                    <div class="progress_left">
                        <AccountProgress :percent="processNum"></AccountProgress>
                    </div>
                    <div class="progress_right">
                        <div class="item" :class="{'cursor': item.status === 'fail'}" v-for="item,index in securityCheckList" :key="index" @click="item.status === 'fail' ? item.clickHandler() : null">
                            <exclamation-circle-filled v-if="item.status === 'fail'"
                                style="color: #ffc107; font-size: 16px"
                            />
                            <check-circle-filled v-if="item.status === 'success'"
                                style="color: #14ac71; font-size: 16px"
                            />
                            <div class="txt">{{ item.title }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑个人信息 -->
    <accountProfileEdit :type="editType"  @refreshUserInfo="refreshUserInfo" @closeEdit="closeEdit"></accountProfileEdit>
    <!-- 验证邮箱和手机 -->
    <accountCheckEmailAndPhone :operation="operation" @refreshUserInfo="refreshUserInfo"  @closeEdit="closeEdit"></accountCheckEmailAndPhone>
</template>
<script lang="ts" setup>
import { getUserInfo } from "~/api/login";
import { useAccountStore } from "~/stores/account";
import { useAuthStore } from "~/stores/auth";
const { t, locale } = useI18n();
const accountStore = useAccountStore();
const authStore = useAuthStore();
const isLogin = computed(() => authStore.login);
// 配置首页样式
const diyWrapStyle = useState("diyWrapStyle", () => "");

//获取用户信息
interface UserProfile {
    avatar?: string; // 头像
    birth_date?: string; // 生日
    email?: string; // 绑定邮箱
    first_name?: string; // 名
    gender?: 1 | 2 | 3 | 4; // 性别：1男性，2女性，3保密，4其他
    genderName?: string; // 性别名称
    is_edit_nickname?: boolean; // 是否可以编辑昵称
    last_name?: string; // 姓
    nick_name?: string; // 昵称
    phone?: string; // 绑定手机
    user_id?: string; // 用户id
    username?: string; // 账号名
}
// 用户信息
const userInfo = computed(() => accountStore.userInfo) as any;

// 侧边栏实例
const accountSidebarRef = ref<any>(null);

// 安全检查
const securityCheck = ref<any>({});

// 编辑类型
const editType = ref<string>("");

// 进度条
const processNum = ref<number>(0);

// 验证的操作行为
const operation = ref<number>(0);
/**
 * 获取用户信息
 * @example
 */



// 刷新用户信息
const refreshUserInfo = () => {
    accountSidebarRef.value.getUserInfoFun();
};
provide('refreshAccountUserInfo', refreshUserInfo);
// 关闭编辑
const closeEdit = () => {
    editType.value = "";
    operation.value = 0;
};
// 安全检查
const securityCheckList = ref<any>([]);
const securityCheckFun = (userInfo: UserProfile) => {
    let initNum = 0;
    function initNumFun(){
        initNum++;
        return "success";
    }
    securityCheckList.value = [
        {
            title: t('account.securityCheck.bindAccountName'),
            status: userInfo.username ? initNumFun() : "fail",
            clickHandler: () => {
                operation.value = 4;
            }
        }, 
        // 验证邮箱
        {
            title: t('account.securityVerification.emailVerification'),
            status: userInfo.email ? initNumFun() : "fail",
            clickHandler: () => {
                operation.value = 4;
            }
        },
        // 绑定手机
        {
            title: t('account.securityVerification.phoneVerification'),
            status: userInfo.phone ? initNumFun() : "fail",
            clickHandler: () => {
                operation.value = 3;
            }
        },
        // 填写个人信息
        {
            title: t('account.securityCheck.fillPersonalInfo'),
            status: userInfo.first_name && userInfo.last_name && userInfo.birth_date && userInfo.gender ? initNumFun() : "fail",
            clickHandler: () => {
                editType.value = "personalInfo";
            }
        },
    ];
    
    let sum = securityCheckList.value.length;
    processNum.value = Math.floor(100 * initNum/sum);
}
onMounted(() => {
    diyWrapStyle.value = "background:#fff;";
    watch(userInfo, () => {
        securityCheckFun(userInfo.value);
    });
    watch(()=>isLogin, (newVal) => {
        if(newVal){
            refreshUserInfo();
        }
    });
});
</script>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
@import url(~/assets/styles/account/profile.scss);
</style>