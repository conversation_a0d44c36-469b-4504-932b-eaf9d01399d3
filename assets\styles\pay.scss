.c_wrap {
    width: 100%;
    max-width: 1400px;
    background: #fff;
    border-radius: 16px;
    margin: 24px auto 0;
    padding: 20px 24px;
}

.banner {
    width: 100%;
    max-height: 300px;
    overflow: hidden;
    position: relative;
    z-index: 1;

    .img_pc {
        width: 100%;
        vertical-align: top;
    }

    .img_mobile {
        display: none;
        width: 100%;
        vertical-align: top;
    }
}

.content {
    background: #f6f6f6;
    padding: 0 20px 30px;
    position: relative;
    z-index: 2;
    border: 1px solid transparent;
}

.change_wrap {
    display: flex;
    align-items: end;
    flex-wrap: wrap;

    .change_role {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
}

.pay_desc_wrap {
    height: 1px;
    margin-top: -1px;
}



.header_box {
    --header_main_w: 116px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    max-width: 640px;

    .header_main {
        width: var(--header_main_w);
        height: var(--header_main_w);
        margin-right: 24px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .region_select {
        background: #eaeaf8;
        min-width: 150px;
    }

    .right {
        height: var(--header_main_w);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-wrap: wrap;
        flex: 1;

        h3 {
            color: #111;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 26px;
            line-height: 36px;
            font-weight: bold;
            text-align: left;
            margin-bottom: 0;

            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
            display: -webkit-box;
            /* 需要对旧版Webkit浏览器做兼容 */
            -webkit-line-clamp: 2;
            /* 限制为2行 */
            -webkit-box-orient: vertical;
            /* 对齐方式 */
            word-break: break-word;
            /* 防止单词溢出 */
        }
    }
}

.role_login_hint {
    display: flex;
    align-items: center;
    margin: 10px 0 -10px;

    .txt {
        font-size: 12px;
        color: #999;
    }
}

.role_wrap {
    margin-top: -70px;
}

.change_select {
    margin-left: 40px;
    display: flex;
    align-items: center;
    margin-top: 20px;

    .region_select {
        padding: 0 3px;
    }

    .txt {
        line-height: 46px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.7);
        margin-right: 20px;
    }

    .txt_black {
        color: #000;

        &.right_line {
            border-right: 1px solid #D9D9D9;
            padding-right: 40px;
        }
    }

    .icon_change_role {
        width: 30px;
        height: 30px;
        background: url(~/assets/images/common/changeRole.png) 50% 50% / 70% 70% no-repeat;
        border-radius: 8px;
        margin-left: 20px;
        border: 1px solid rgba(26, 26, 26, 0.10);
        cursor: pointer;

        &:hover {
            box-shadow: 0 0 5px #ccc;
        }
    }
}

.pay_role_login {
    margin-left: 20px;
    background: #3A58A9;
    border-radius: 6px;

    &:hover {
        background: #5d7ed8;
    }
}

.region_select {
    height: 44px;
    border-radius: 12px;
    border: 1px solid rgba(26, 26, 26, 0.10);
    background: #fff;
    min-width: 200px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 0 14px;

    .a_select_title {
        margin-right: 10px;
        color: rgba(0, 0, 0, 0.7);
    }

    .txt {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-right: 10px;
    }

    .recently_login {
        display: flex;
        align-items: center;
        margin-left: 20px;
        color: rgba(0, 0, 0, 0.5);
        font-size: 14px;
        padding-right: 6px;
        line-height: 30px;

        span {
            font-size: 20px;
            font-weight: 1000;
            color: #466DF6;
            margin-right: 6px;
        }
    }
}

.goods_list_wrap {
    margin-top: 24px;
    padding: 20px 24px;
}

.notice_wrap {
    --notice_height: 24px;
    line-height: var(--notice_height);
    border-radius: 6px;
    background: linear-gradient(90deg, rgba(56, 99, 215, 0.1), #fff);
    padding: 6px 12px;
    color: rgba(1, 1, 1, 0.6);
    display: flex;
    align-items: center;
    font-size: 16px;

    .icon_notice {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        background: url(~/assets/images/common/notice.png) top center/100% 100% no-repeat;
        animation: notice_opacity 3s ease infinite;
    }

    .notice_txt_wrap {
        overflow: hidden;
        flex: 1;
        height: var(--notice_height);

        .notice_txt_main {
            display: flex;
            flex-direction: column;

            &.animation {
                animation: noticeScroll 6s linear infinite;
            }

            .notice_txt_item {
                width: 100%;
            }
        }
    }
}

@keyframes noticeScroll {

    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-50%);
    }
}

@keyframes notice_opacity {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.tab_wrap {
    overflow-x: auto;
    scrollbar-width: 0;
    scrollbar-color: transparent;

    /* Webkit内核浏览器（如Chrome, Safari）的滚动条样式 */
    &::-webkit-scrollbar {
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background-color: #eee;
        border-radius: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 6px;
        transition: background-color 0.3s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: #999;
    }
}

.tab_box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    border-bottom: 1px solid #ECECF0;
    position: relative;
    z-index: 1;

    .li {
        color: rgba(1, 1, 1, 0.6);
        font-family: "PingFang SC";
        font-size: 20px;
        padding: 0 10px;
        margin-right: 44px;
        cursor: pointer;
        line-height: 60px;
        flex-shrink: 0;

        &:last-child {
            margin-right: 0;
        }

        &.active,
        &:hover {
            color: #111;
            position: relative;

            &::after {
                content: "";
                height: 4px;
                background: #111;
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 100%;
                transform: translate(-50%, 0);
            }
        }
    }
}

.goods_list_box {
    position: relative;

    .goods_list_cover {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 99;
    }
}

.goods_list_box.disabled .goods_list .item {
    filter: grayscale(1);
}

.goods_btn_wrap {
    position: relative;
}

.activity_goods_label {
    position: absolute;
    left: 10px;
    top: -14px;
    height: 24px;

    img {
        max-height: 100%;
        max-width: 100%;
    }
}

// 活动商品
.activity_goods_num {
    position: absolute;
    z-index: 9;
    right: 5px;
    bottom: 8px;
    width: 34px;
    height: 16px;
    background: linear-gradient(93deg, #BEFDF3 2.14%, #EFFBDB 25.26%, #FFFBD3 39.93%, #FFE1E9 64.83%, #F6DBEC 76.83%, #E2D1FB 91.06%);
    opacity: 0.8;
    border-radius: 4px;
    transform: skew(-10deg);
    text-align: center;
    font-size: 12px;

    .label_txt {
        transform: skew(10deg);
        line-height: 16px;
        color: #171717;
        font-weight: bold;
    }
}

// 倒计时 
.countDown_wrap {
    width: 110px;
    height: 24px;
    border-radius: 6px;
    border: 2px solid #FFC716;
    transform: skew(-10deg);
    position: absolute;
    background: #FFC716;
    top: 3px;
    right: 5px;
    z-index: 99;
    display: flex;
    align-items: center;

    .icon_clock {
        width: 22px;
        height: 20px;
        margin: 0 5px;
        transform: skew(10deg);
        background-image: url(~/assets/images/common/clock.png);
        background-position: 50% 50%;
        background-size: 100% 100%;
    }

    .num {
        font-size: 12px;
        color: #111;
        font-weight: bold;
        background: #fff;
        flex: 1;
        height: 100%;
        border-radius: 6px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        text-align: center;
        line-height: 20px;

        .num_txt {
            transform: skew(10deg);
        }
    }
}

.goods_list {
    display: flex;
    flex-wrap: wrap;
    z-index: 1;
    position: relative;
    padding: 30px 0;
    width: calc(100% + 10px);
    --goods_font_height: 24px;

    .item_box {
        width: 16.66%;
        padding-right: 10px;
        margin-bottom: 10px;
    }

    .item {
        cursor: pointer;
        border-radius: 20px;
        border: 2px solid #ebebf4;
        background: #f9f9fd;
        overflow: hidden;
        transition: all 0.5s ease;
        position: relative;

        &.gray {
            filter: grayscale(1);
        }

        &:not(.gray):hover,
        &:not(.gray).active {
            background: #ffffff;
            border: 2px solid #3A58A9;
            box-shadow: 0 0 10px #eee;
            transform: translateY(-4px);

            .goods_btn {
                background: #3A58A9;
            }
        }

        &:not(.gray):hover {
            transform: translateY(-4px);
        }

        &:not(.gray).active {
            transform: translateY(0) !important;
        }

        .click_box_pc,
        .click_box_m {
            position: absolute;
            z-index: 99;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .imgBox {
            width: 100%;
            padding-top: 57.6%;
            height: 0;
            position: relative;

            .img {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 1;
            }
        }

        .h3 {
            margin: 5px auto;
            padding: 0 10px;
            color: #111;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            line-height: var(--goods_font_height);
            // height: calc(var(--goods_font_height) * 3);
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
            display: -webkit-box;
            /* 需要对旧版Webkit浏览器做兼容 */
            line-clamp: 1;
            -webkit-line-clamp: 1;
            /* 限制为2行 */
            -webkit-box-orient: vertical;
            /* 对齐方式 */
            word-break: break-word;

            /* 防止单词溢出 */
            &.two_line {
                line-clamp: 2;
                -webkit-line-clamp: 2;
            }
        }

        .cont {
            height: 80px;
        }

        .desc {
            color: #999;
            font-size: 12px;
            line-height: 20px;
            padding: 0 10px;
            margin-bottom: 10px;
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
            display: -webkit-box;
            /* 需要对旧版Webkit浏览器做兼容 */
            line-clamp: 2;
            -webkit-line-clamp: 2;
            /* 限制为2行 */
            -webkit-box-orient: vertical;
            /* 对齐方式 */
            word-break: break-word;
        }

        .goods_btn {
            margin: 12px;
            border-radius: 20px;
            line-height: 40px;
            font-size: 20px;
            color: #fff;
            background: #1A1A1A;
            padding: 0 14px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: baseline;
            transition: all 0.5s ease;

            .dollar {
                font-size: 14px;
                margin-right: 5px;
            }

            .price {
                font-weight: bold;
            }
        }
    }
}

.c_pay_h2 {
    color: #1A1A1A;
    font-size: 20px;
    font-weight: bold;
    line-height: 32px;
    padding-bottom: 10px;
}

.c_pay_p,
.c_pay_p p {
    color: #111;
    font-size: 16px;
    line-height: 1.8;
}

// 支付模块
.pay_ctrl_wrap {
    padding: 10px 24px;

    &.fixed {
        position: fixed;
        z-index: 999;
        width: 100%;
        margin: 0;
        max-width: none;
        border-radius: 0;
        bottom: 0;
        left: 0;
        background: #fff;
        box-shadow: 0 0 10px #ddd;
        height: 70px;

        .pay_box {
            height: 50px;
        }

        .pb_middle {
            display: flex;
        }
    }
}

.pay_box {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.pb_left {
    color: #111;
    font-size: 14px;
    line-height: 24px;

    .goods_desc {
        display: flex;
        align-items: center;
    }

    .name {
        color: rgba(0, 0, 0, 0.5);
    }
}

.pb_middle {
    cursor: pointer;
    display: none;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-100%, -50%);

    .txt {
        color: #888;
        margin-right: 4px;
    }

    .imgBox {
        display: flex;
        width: 120px;
        /* 设置父容器的宽度 */
        align-items: center;

        .img {
            flex: 1;

            /* 允许图片自动伸缩 */
            img {
                width: 100%;
                vertical-align: middle;
            }
        }
    }
}

.pb_right {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .price {
        color: #3A58A9;
        font-weight: 500;
        font-size: 36px;
        margin-right: 30px;
        line-height: 36px;
    }

    .price_label {
        color: #3A58A9;
        font-weight: 500;
        font-size: 24px;
        margin-right: 4px;
        line-height: 24px;
    }

    .price_txt {
        color: #111;
        font-size: 14px;
        line-height: 14px;
    }

    .pb_payBtn {
        padding: 0 50px;
        height: 50px;
        flex-shrink: 0;
        border-radius: 30px;
        background: #3A58A9;
        font-size: 20px;
        color: #fff;
        line-height: 50px;
        cursor: pointer;
        transition: all 0.5s ease;

        &:hover {
            background: #466DF6;
        }
    }
}

.pay_method {
    display: flex;
    width: calc(100% + 26px);
    flex-wrap: wrap;
    align-items: center;

    .item_wrap {
        width: 25%;
        padding-right: 26px;
    }

    .item {
        border: 2px solid rgba(243, 243, 251, 0.6);
        border-radius: 12px;
        height: 64px;
        background: rgba(243, 243, 251, 0.6);
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 40px;
        position: relative;
        cursor: pointer;

        &.active,
        &:hover {
            border: 2px solid #3A58A9;
            background: none;
        }

        &.recommand::after {
            content: "";
            width: 64px;
            height: 50px;
            right: -4px;
            top: -9px;
            position: absolute;
            z-index: 9;
            background: url(~/assets/images/common/recommand.png);
        }

        .imgBox {
            width: 120px;
            margin: 0 auto;

            .img {
                width: 100%;
            }
        }

        .icon_creditcard {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: -10px;
            height: 100%;

            .img {
                width: 38px;
                height: 38px;
                display: block;
            }

            .creditcard_img {
                max-width: 100%;
                max-height: 100%;
            }

            .creditcard_img_h70 {
                max-height: 70%;
                margin: 0 5px;
            }

            .txt {
                color: #111;
                font-size: 20px;
                align-items: center;
                margin-left: 16px;
                line-height: 24px;
            }
        }
    }
}

.order_list_empty {
    padding: 50px 0;
    width: 100%;
    text-align: center;
}


// 手机端弹窗订单
.click_box_m {
    display: none;
}

.order_fixed_wrap,
.order_fixed_model {
    width: 100%;
    z-index: 888;
    position: fixed;
    bottom: -100%;
    left: 0;
    display: none;
}

.order_fixed_model {
    height: 100%;
    z-index: 887;
    opacity: 0;
    background: #000;
    transition: opacity 0.5s ease;

    &.show {
        opacity: 0.7;
        bottom: 0;
    }
}

.order_fixed_wrap {
    height: 80%;
    transition: all 0.5s ease;

    &.show {
        bottom: 0;
    }
}

.order_fixed_box {
    height: 100%;
    width: 100%;
    background: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    --order_f_padding: 20px;

    .order_f_close {
        position: absolute;
        z-index: 9;
        height: 40px;
        width: 40px;
        right: 6px;
        top: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: rgba(1, 1, 1, 0.2);
    }

    .title {
        line-height: 50px;
        font-size: 16px;
        border-bottom: 1px solid #ECECF0;
        color: rgba(1, 1, 1, 0.7);
        text-align: center;
    }

    .order_f_main {
        flex: 1;
        overflow-y: auto;
        padding: 0 var(--order_f_padding);
        scroll-behavior: smooth;

        h3 {
            line-height: 40px;
            padding-bottom: 0;
            margin-bottom: 0;
            font-size: 18px;
            color: #111;
        }

        .line {
            font-size: 14px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            &.gray {
                color: #999;
            }

            .left {
                margin-right: 10px;
            }
        }

        .currency_line {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .currency_name {
                font-size: 16px;

                &::after {
                    content: ">";
                    transform: scaleX(0.5);
                    font-size: 18px;
                    margin-left: 8px;
                    font-family: "icon-font";
                    line-height: 20px;
                    display: inline-block;
                  }
            }
        }

        .price_line {
            font-size: 14px;
            color: #111;
            line-height: 36px;
            display: flex;
            align-items: baseline;
            justify-content: right;

            .price_num {
                color: #3A58A9;
                font-size: 22px;
                font-weight: bold;
            }
        }
    }

    .order_f_bottom {
        padding: var(--order_f_padding);
        padding-bottom: 30px;
        background: #fff;

        .order_f_payBtn {
            height: 50px;
            width: 100%;
            flex-shrink: 0;
            border-radius: 30px;
            background: #3A58A9;
            font-size: 20px;
            text-align: center;
            color: #fff;
            line-height: 46px;
            cursor: pointer;
            padding: 0;
        }
    }
}

.order_f_pay_method {

    width: 100%;

    .item_wrap {
        margin-bottom: 20px;
    }

    .item {
        border: 2px solid rgba(243, 243, 251, 0.6);
        border-radius: 10px;
        height: 54px;
        background: rgba(243, 243, 251, 0.6);
        display: flex;
        align-items: center;
        padding: 0 20px;
        position: relative;
        cursor: pointer;
        flex-wrap: wrap;

        &:first-child {
            margin-top: 0;
        }

        &.active,
        &:hover {
            border: 2px solid #3A58A9;
            background: none;
        }

        &.credit_card {
            height: auto;
            min-height: 54px;
            padding: 6px 20px;

            .credit_card_flex1_wrap {
                flex: 1;
                justify-content: center;
                align-items: center;
                flex-wrap: wrap;
                display: flex;
            }
        }

        &.recommand::after {
            content: "";
            width: 45px;
            height: 35px;
            right: -4px;
            top: -7px;
            position: absolute;
            z-index: 9;
            background: url(~/assets/images/common/recommand.png) 50% 50%/100% 100% no-repeat;
        }

        .imgBox {
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;

            .img {
                max-width: 100px;
                max-height: 40px;
                display: block;
                margin: 0 5px;
            }
        }

        .order_f_credit_card_list {
            height: 32px;
            padding: 4px;
            border-radius: 12px;
            border: 1px solid rgba(26, 26, 26, 0.1);
            background: #F3F3FB;

            .img {
                height: 100%;
            }
        }

        .icon_creditcard {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 10px;
            margin-right: 10px;
            margin-bottom: 10px;

            .img {
                width: 30px;
                height: 30px;
                display: block;
            }

            .txt {
                color: #111;
                font-size: 16px;
                align-items: center;
                margin-left: 16px;
                line-height: 24px;
            }
        }
    }
}

.my_card_wrap {
    display: flex;
    border-radius: 16px;
    flex-wrap: wrap;
    width: calc(100% + 20px);
    margin-left: -10px;

    .my_card_hint {
        width: 100%;
        line-height: 30px;
        font-size: 14px;
        padding: 0 12px;
        color: #333;
        font-weight: bold;
    }

    .item_box {
        width: 20%;
        padding: 10px;
        position: relative;

        .activity_goods_num {
            right: 15px;
            bottom: 15px;
        }

        .activity_goods_label {
            left: 5px;
            top: 0px;
            height: 24px;
        }

        .my_card_item {
            border-radius: 12px;
            border: 1px solid rgba(26, 26, 26, 0.30);
            background: #FFF;
            width: 100%;
            height: 115px;
            padding: 10px;
            cursor: pointer;

            &.active {
                border: 1px solid #3A58A9;
                background: #F3F3FB;
            }

            h4 {
                font-size: 14px;
                font-weight: bold;
                line-height: 25px;
                color: #111;
                margin-bottom: 0;
                overflow: hidden;
                /* 隐藏超出部分 */
                text-overflow: ellipsis;
                /* 显示省略号 */
                display: -webkit-box;
                /* 需要对旧版Webkit浏览器做兼容 */
                line-clamp: 1;
                -webkit-line-clamp: 1;
                /* 限制为2行 */
                -webkit-box-orient: vertical;
                /* 对齐方式 */
                word-break: break-word;

                /* 防止单词溢出 */
                &.two_line {
                    /* 需要对旧版Webkit浏览器做兼容 */
                    line-clamp: 2;
                    -webkit-line-clamp: 2;
                }
            }

            .desc {
                color: #999;
                font-size: 12px;
                overflow: hidden;
                /* 隐藏超出部分 */
                text-overflow: ellipsis;
                /* 显示省略号 */
                display: -webkit-box;
                /* 需要对旧版Webkit浏览器做兼容 */
                line-clamp: 2;
                -webkit-line-clamp: 2;
                /* 限制为2行 */
                -webkit-box-orient: vertical;
                /* 对齐方式 */
                word-break: break-word;
            }

            .countDown_wrap {
                .txt {
                    font-size: 12px;
                    color: #111;
                    font-weight: bold;
                    line-height: 20px;
                }
            }

            .txt {
                color: #666;
                font-size: 14px;
                line-height: 24px;
                display: block;
                width: 100%;
                font-weight: bold;
            }
        }
    }
}


@media screen and (max-width: 1368px) {
    .goods_list {
        .item_box {
            width: 20%;
        }
    }
}

@media screen and (max-width: 1280px) {

    .role_wrap {
        margin-top: -40px;
    }

    .header_box {
        --header_main_w: 100px;

        .header_main {
            margin-right: 20px;
            border-radius: 10px;
        }

        .right {
            h3 {
                font-size: 24px;
                line-height: 30px;
            }

        }
    }

    .region_select {
        height: 40px;
        border-radius: 10px;
        min-width: 120px;
        font-size: 14px;
    }

    .change_select {
        margin-left: 30px;

        .txt {
            line-height: 40px;
            font-size: 14px;
        }

        .txt_black {
            color: #000;

            &.right_line {
                padding-right: 30px;
            }
        }
    }

    .goods_list {
        .item_box {
            width: 25%;
        }

        .item {
            cursor: pointer;
            border-radius: 20px;
            border: 2px solid #ebebf4;
            background: #f9f9fd;
            overflow: hidden;
            transition: all 0.5s ease;

            .h3 {
                padding: 0 14px;
                font-size: 16px;
            }

            .goods_btn {
                font-size: 20px;
            }
        }
    }

    .c_pay_h2 {
        font-size: 18px;
    }

    .pay_method {
        width: calc(100% + 16px);

        .item_wrap {
            width: 33.33%;
            padding-right: 16px;
        }

        .item {
            border-radius: 10px;
            height: 60px;
            margin-bottom: 16px;
            padding: 0 20px;

            &.recommand::after {
                width: 45px;
                height: 35px;
                right: -4px;
                top: -7px;
                background: url(~/assets/images/common/recommand.png) top center/100% 100% no-repeat;
            }

            .icon_creditcard {
                padding: 0 10px;
                margin-right: 0;

                .img {
                    width: 32px;
                    height: 32px;
                }

                .txt {
                    font-size: 18px;
                }
            }
        }
    }

    .my_card_wrap {

        .item_box {
            width: 25%;
        }
    }



    .pb_right {
        .price_label {
            font-size: 24px;
            line-height: 24px;
        }

        .price {
            margin-right: 10px;
        }


        .pb_payBtn {
            padding: 0 44px;
            height: 44px;
            border-radius: 30px;
            font-size: 18px;
            line-height: 44px;
        }
    }

}

@media screen and (max-width: 980px) {
    .change_wrap {
        display: block;
    }

    .role_wrap {
        padding: 20px;
    }

    .region_select {
        min-width: 250px;
    }

    .change_select {
        margin-left: 0;
        margin-right: 30px;
    }

    .notice_wrap {
        --notice_height: 24px;
        padding: 6px 10px;
        font-size: 14px;
    }


    .tab_box {

        .li {
            font-size: 18px;
            margin-right: 30px;
            line-height: 50px;
        }
    }

    .pay_method {
        width: calc(100% + 12px);

        .item_wrap {
            width: 50%;
            padding-right: 12px;
        }

        .item {
            border-radius: 10px;
            height: 60px;
        }
    }

    .goods_list {
        padding: 20px 0;
        --goods_font_height: 24px;

        .item_box {
            width: 33.33%;
        }

        .item {
            .goods_btn {

                margin: 10px;
                border-radius: 20px;
                line-height: 40px;
                padding: 0 10px;

                .dollar {
                    font-size: 14px;
                }
            }
        }
    }

    .my_card_wrap {
        .item_box {
            width: 33.33%;
        }
    }

    .c_pay_h2 {
        font-size: 16px;
    }

    .c_pay_p,
    .c_pay_p p {
        font-size: 14px;
    }

    .pb_right {
        .price_label {
            font-size: 16px;
            line-height: 16px;
        }

        .price {
            font-size: 30px;
            line-height: 30px;
        }


        .pb_payBtn {
            padding: 0 40px;
            height: 40px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 40px;
        }
    }

}

@media screen and (max-width: 768px) {
    .role_login_hint {
        margin-bottom: 0;
    }

    .notice_wrap {
        --notice_height: 18px;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 12px;

        .icon_notice {
            width: 14px;
            height: 14px;
            margin-right: 6px;
        }

    }

    .region_select {
        min-width: 400px;
    }

    .tab_box {
        .li {
            font-size: 16px;
            margin-right: 20px;
            line-height: 50px;
        }
    }

    .content {
        background: #f6f6f6;
        padding: 0 10px 20px;
    }


    .role_wrap {
        margin-top: 24px;
        padding: 16px;
    }

    .banner {
        .img_pc {
            display: none;
        }

        .img_mobile {
            display: block;
        }
    }

    .goods_list {

        .item_box {
            width: 50%;
        }
    }

    .my_card_wrap {
        .item_box {
            width: 50%;
            padding: 4px;

            .my_card_item {
                height: 90px;
                padding: 6px;

                h4 {
                    font-size: 14px;
                    line-height: 20px;
                    padding-top: 6px;
                }

                .txt {
                    font-size: 12px;
                    line-height: 18px;
                }
            }

            .activity_goods_label {
                left: 2px;
                top: 0px;
                height: 20px;
            }

            .activity_goods_num {
                right: 5px;
                bottom: 4px;
            }

            .countDown_wrap {
                width: 78px;
                height: 18px;
                top: 2px;
                right: 2px;

                .icon_clock {
                    width: 16px;
                    height: 14px;
                    margin: 0 3px;
                    background-image: url(~/assets/images/common/clock.png);
                    background-position: 50% 50%;
                    background-size: 100% 100%;
                }

                .num {
                    font-size: 10px;
                    line-height: 14px;
                }
            }
        }
    }

    .tab_wrap {

        /* Webkit内核浏览器（如Chrome, Safari）的滚动条样式 */
        &::-webkit-scrollbar {
            height: 3px;
        }

    }

    // 手机端下隐藏
    .pay_method_wrap,
    .click_box_pc,
    .pay_ctrl_wrap {
        display: none;
    }

    .order_fixed_wrap,
    .order_fixed_model,
    .click_box_m {
        display: block;
    }

}

@media screen and (max-width: 640px) {
    .header_box {
        --header_main_w: 80px;

        .header_main {
            margin-right: 10px;
            border-radius: 8px;
        }

        .region_select {
            height: 32px;
            font-size: 12px;
        }

        .right {
            h3 {
                font-size: 16px;
                line-height: 22px;
            }

        }
    }

    .pay_role_login {
        margin-left: 10px;
    }

    .role_wrap {
        padding: 12px;
    }

    .change_select {
        width: 100%;
        margin: 10px auto 0;

        .txt_black {

            &.right_line {
                border-right: none;
                padding-right: 0;
            }
        }
    }

    .region_select {
        width: 100%;
        min-width: auto;
    }

    .content {
        background: #f6f6f6;
        padding: 0 8px 20px;
    }

    .tab_box {
        .li {
            font-size: 14px;
            margin-right: 20px;
            line-height: 40px;
            color: rgba(1, 1, 1, 0.6);
            padding: 0 8px;

            &.active,
            &:hover {

                &::after {
                    height: 3px;
                }
            }
        }
    }

    .goods_list_wrap {
        margin-top: 20px;
        padding: 8px;
    }

    .banner {
        .img_pc {
            display: none;
        }

        .img_mobile {
            display: block;
        }
    }

    .goods_list {
        padding: 10px 0;
        width: calc(100% + 6px);
        --goods_font_height: 20px;

        .item_box {
            width: 50%;
            padding-right: 6px;
            margin-bottom: 6px;
        }

        .item {
            border-radius: 10px;
            border: 1px solid #ebebf4;

            &:hover,
            &.active {
                border: 1px solid #3A58A9;
                transform: translateY(0);
            }

            .h3 {
                margin: 8px auto;
                padding: 0 6px;
                font-size: 14px;
            }

            .goods_btn {
                margin: 6px;
                border-radius: 20px;
                line-height: 36px;
                font-size: 16px;
                padding: 0 6px;

                .dollar {
                    font-size: 12px;
                    margin-right: 3px;
                }
            }
        }
    }

    .my_card_wrap {
        border-radius: 12px;
        width: calc(100% + 8px);
        margin-left: -4px;

        .my_card_hint {
            font-size: 12px;
        }
        .desc {
            font-size: 10px;
        }
    }

    .c_pay_h2 {
        font-size: 14px;
    }

    .c_pay_p,
    .c_pay_p p {
        font-size: 12px;
    }
}