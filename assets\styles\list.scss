.order_banner {
    width: 100%;
    min-height: 15.6vw;

    .img {
        width: 100%;
    }

    .banner_pc {
        display: block;
    }

    .banner_mobile {
        display: none;
    }
}

.order_content {
    width: 100%;
    padding: 20px 30px;
    background: #f6f6f6;
    margin: 0 auto;
    position: relative;
}

.order_list {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 10px 30px 40px;
    background: #ffffff;
    box-shadow: 0px 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 20px;

    .ant-tabs-bar {
        margin-bottom: 6px;
    }

}


.tab_wrap {
    height: 60px;
    border-bottom: 1px solid #ECECF0;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    justify-content: space-between;
}

.tab_box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    overflow-y: auto;

    /* 更改滚动条宽度 */
    &::-webkit-scrollbar {
        height: 3px;
    }

    /* 更改滚动条颜色 */
    &::-webkit-scrollbar-thumb {
        background-color: #F0F0F0;
        height: 2px;
    }

    /* 更改滑块样式 */
    &::-webkit-scrollbar-thumb:hover {
        background-color: #E0E0E0;
    }

    .li {
        color: rgba(1, 1, 1, 0.6);
        font-family: "PingFang SC";
        font-size: 18px;
        padding: 0 10px;
        margin-right: 44px;
        cursor: pointer;
        line-height: 60px;
        white-space: nowrap;

        &:last-child {
            margin-right: 0;
        }

        &.active,
        &:hover {
            color: #111;
            position: relative;

            &::after {
                content: "";
                height: 4px;
                background: #111;
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 100%;
                transform: translate(-50%, 0);
            }
        }
    }
}

.order_item_list {
    padding-top: 24px;
    min-height: 400px;
}

.order_item {
    display: block;
    margin: 0 auto 24px;
    box-sizing: border-box;
    border-radius: 16px;
    border: 1px solid rgba(26, 26, 26, 0.1);
    background: rgba(234, 234, 248, 0.3);
    overflow: hidden
}

.order_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    line-height: 44px;
    padding: 0 24px;
    font-size: 16px;
    color: rgba(17, 17, 17, 0.6);
    font-family: "PingFang SC", "Microsoft YaHei";
    font-weight: normal;
    border-bottom: 1px solid rgba(26, 26, 26, 0.1);

    .order_number {
        margin-right: 140px;
    }

    .order_time {
        margin-right: auto;
    }

    .order_status {
        font-size: 18px;

        &.gray {
            color: rgba(1, 1, 1, 0.5);
        }

        &.blue {
            color: #3A58A9;
            font-weight: 400;
        }
    }

}


.order_item_content {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 24px;
}

.order_info {
    --productHeight: 120px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: var(--productHeight);

    .product_img {
        width: var(--productHeight);
        height: var(--productHeight);
        border-radius: 10px;
        overflow: hidden;
        flex-shrink: 0;

        .img {
            width: 100%;
            height: 100%;
        }
    }

    .order_info_right {
        margin-left: 30px;
        height: var(--productHeight);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        h3 {
            color: #111;
            font-family: "PingFang SC";
            font-size: 24px;
            font-weight: 400;
            line-height: 1.3;
            display: -webkit-box;
            /* 使用Webkit的弹性盒模型显示 */
            -webkit-box-orient: vertical;
            /* 设置子元素垂直排列 */
            overflow: hidden;
            /* 隐藏超出的内容 */
            text-overflow: ellipsis;
            /* 用省略号显示被隐藏的文本 */
        }

        .game_name {
            border-radius: 8px;
            align-self: flex-start;
            display: inline-block;
            padding: 0 20px;
            border: 1px solid rgba(70, 109, 246, 0.50);
            background: rgba(70, 109, 246, 0.10);
            color: #466DF6;
            font-size: 16px;
            margin-top: auto;
            text-align: center;
            line-height: 28px;
        }

        .server_name {
            color: rgba(1, 1, 1, 0.5);
            font-family: "PingFang SC";
            font-size: 16px;
            margin-top: 6px;
            line-height: 24px;
        }
    }
}

.order_item_right {
    display: flex;
    flex-direction: column;
    align-items: end;
    justify-content: center;
    $order_btn_height: 36px;

    .order_price {
        color: #111;
        font-size: 28px;
        line-height: 50px;
    }

    .order_type {
        font-size: 14px;
        color: rgba(1, 1, 1, 0.5);
        height: $order_btn_height;
        text-align: center;
        line-height: 20px;
    }


    .order_btn {
        color: rgba(1, 1, 1, 0.8);
        padding: 0 20px;
        font-size: 18px;
        border: 1px solid rgba(17, 17, 17, 0.60);
        background: #FFF;
        line-height: $order_btn_height;
        border-radius: calc($order_btn_height / 2);
        cursor: pointer;
        transition: all 0.5s ease;
        white-space: nowrap;

        &:hover {
            color: #3A58A9;
            background: #faf9f9;
            border-color: #3A58A9;
            transform: translate(0, -2px);
        }
    }
}

.order_icon {
    //   flex: 1;
    position: absolute;
    left: 24px;
    top: 12px;
    width: 100px;
    height: 100px;
    border-radius: 20px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.order_pagination {
    margin-top: 32px;
    display: flex;
    justify-content: flex-end;
}

.order_list_empty {
    margin: 20px auto;
    text-align: center;
    font-size: 20px;

    .img {
        width: 50%;
        max-width: 350px;
        margin: 0 auto;

        img {
            width: 100%;
        }
    }
}

.order_line {
    display: flex;
}


@media screen and (max-width: 1280px) {
    .tab_wrap {
        height: 50px;
    }

    .tab_box {
        .li {
            font-size: 16px;
            padding: 0 10px;
            margin-right: 30px;
            line-height: 50px;
        }
    }

    .order_header {
        line-height: 36px;
        padding: 0 24px;
        font-size: 14px;

        .order_number {
            margin-right: 40px;
        }

        .order_status {
            font-size: 16px;
        }

    }
}

@media screen and (max-width: 980px) {
    .tab_wrap {
        height: 40px;
    }

    .order_item_content {
        padding: 18px;
    }

    .tab_box {
        .li {
            font-size: 14px;
            padding: 0 10px;
            margin-right: 10px;
            line-height: 40px;
        }
    }

    .order_info {
        --productHeight: 100px;

        .order_info_right {
            margin-left: 20px;

            h3 {
                font-size: 20px;
            }

            .game_name {
                font-size: 14px;
                line-height: 24px;
            }

            .server_name {
                font-size: 14px;
                line-height: 24px;
            }
        }
    }

    .order_item_right {

        .order_price {
            font-size: 26px;
        }
    }
}

@media screen and (max-width: 768px) {

    .order_banner {
        .banner_pc {
            display: none;
        }

        .banner_mobile {
            display: block;
        }
    }

    .tab_wrap {
        height: 40px;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .tab_box {
        width: auto;
        flex-wrap: nowrap;


        .li {
            flex-shrink: 0;
            font-size: 12px;
        }
    }

    .order_content {
        padding-top: 90px;
    }

    .order_list {
        border-radius: 10px;
    }

    .tab_wrap {
        display: block;
    }


    .order_info {
        --productHeight: 80px;
        height: auto;
        align-items: flex-start;

        .order_info_right {
            height: auto;
            margin-left: 12px;
            display: block;

            h3 {
                font-size: 16px;
                line-height: 24px;
                height: 50px;
            }

            .game_name {
                font-size: 14px;
                line-height: 24px;
            }

            .server_name {
                font-size: 14px;
                line-height: 40px;
                margin-left: -92px;
            }
        }
    }

    .order_item_right {
        align-items: flex-end;
        justify-content: flex-start;
        $order_btn_height: 36px;

        .order_price {
            color: #111;
            font-size: 20px;
            line-height: 24px;
            margin-bottom: 10px;
        }

        .order_type {
            font-size: 12px;
        }


        .order_btn {
            padding: 0 14px;
            font-size: 14px;
            background: #FFF;
            line-height: 26px;
            border-radius: 15px;
            margin-top: 40px;
        }
    }

    .order_line {
        display: block;
        padding: 8px 0;

        .order_number,
        .order_time {
            line-height: 24px;
            font-size: 14px;
        }
    }

}

@media screen and (max-width: 640px) {

    .order_content {
        padding: 60px 14px 20px;
    }

    .order_list {
        padding: 0 14px 30px;

    }

    .order_item_content {
        padding: 10px;
    }

    .order_info {
        --productHeight: 70px;
        flex: 1;
        position: relative;
        padding-bottom: 56px;

        .order_info_right {
            margin-left: 12px;
            display: block;
            flex: 1;

            h3 {
                font-size: 14px;
                line-height: 24px;
                height: 48px;
                -webkit-line-clamp: 2;
                /* 限制显示的行数，例如3行 */
            }

            .game_name {
                font-size: 12px;
                line-height: 20px;
                padding: 0 10px;
            }

            .server_name {
                font-size: 12px;
                line-height: 24px;
                position: absolute;
                left: 0;
                top: 72px;
                margin-left: 0;
            }
        }
    }

    .order_header {
        padding: 0 10px;

        .order_status {
            font-size: 14px;
        }

        .order_line {
            display: block;
            padding: 8px 0;

            .order_number,
            .order_time {
                font-size: 12px;
                line-height: 20px;
            }
        }
    }

    .order_item_right {
        align-items: flex-end;
        justify-content: flex-start;
        $order_btn_height: 36px;
        flex-shrink: 0;
        position: relative;
        // position: absolute;
        // right: 10px;
        // top: 10px;

        .order_price {
            color: #111;
            font-size: 14px;
            line-height: 24px;
            margin-bottom: 4px;
            font-weight: bold;
            padding-left: 5px;
            padding-top: 24px;
        }

        .order_type {
            font-size: 11px;
            line-height: 16px;
            height: auto;
            position: absolute;
            top: 56px;
            right: 0;
            white-space: nowrap;
            text-align: right;
        }


        .order_btn {
            padding: 0 14px;
            font-size: 14px;
            background: #FFF;
            line-height: 26px;
            border-radius: 15px;
            position: absolute;
            top: 60px;
            right: 0;
        }
    }
}

@media screen and (max-width: 480px) {
    .order_info {

        .order_info_right {
            margin-left: 12px;
            display: block;
            flex: 1;

            h3 {
                margin-bottom: 24px;
                font-size: 12px;
            }

            .game_name {
                font-size: 12px;
                line-height: 20px;
                padding: 0 10px;
                max-width: 100vw;
                white-space: nowrap;
                position: absolute;
                top: 82px;
                left: 0;
            }

            .server_name {
                font-size: 12px;
                line-height: 24px;
                position: absolute;
                left: 0;
                top: 102px;
                margin-left: 0;
                width: 100vw;
            }
        }
    }

    .order_item_right {

        .order_btn {
            top: 10px;
        }
    }
}