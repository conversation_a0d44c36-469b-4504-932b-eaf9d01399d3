<template>
    <div class="order_list">
        <div class="tab_wrap">
            <client-only>
                <div
                    class="project_select_wrap"
                    :class="{ fixed: gamaChangeBar }"
                >
                    <a-select
                        class="project_select"
                        :value="params.project_id"
                        :dropdown-match-select-width="false"
                        @change="handleGame"
                    >
                        <a-select-option :value="0">{{
                            $t("pages.index.all-games")
                        }}</a-select-option>
                        <a-select-option
                            v-for="(item, index) in projectList"
                            :key="index"
                            :value="item.id"
                        >
                            {{ item.name[locale] }}
                        </a-select-option>
                    </a-select>
                </div>
            </client-only>

            <div class="tab_box">
                <div
                    class="li"
                    :class="{ active: item.key == tabIndex }"
                    v-for="(item, index) in titleList"
                    :key="index"
                    @click="changeTabs(item.key)"
                >
                    {{ $t(item.title) }}
                </div>
            </div>
        </div>
        <div class="order_item_list">
            <div v-if="loading" class="order_list-empty">
                <div class="c_loading"></div>
            </div>
            <template v-else-if="totalNum > 0">
                <div
                    class="order_item"
                    v-for="source in dataList"
                    :key="source.order_id"
                >
                    <div class="order_header">
                        <div class="order_line">
                            <div class="order_number">
                                {{ source.order_id }}
                                <!-- <span>{{ $t("pages.pay.order-number") }}：</span>{{ source.order_id }} -->
                            </div>
                            <div class="order_time">
                                {{ source.create_time }}
                            </div>
                        </div>
                        <div
                            class="order_status"
                            :class="{
                                [orderStatusList[source.order_status_code - 1]
                                    .style]: true,
                            }"
                        >
                            {{
                                $t(
                                    orderStatusList[
                                        source.order_status_code - 1
                                    ].label
                                )
                            }}
                        </div>
                    </div>
                    <div class="order_item_content">
                        <div class="order_info">
                            <div class="product_img">
                                <img class="img" :src="source.icon" />
                            </div>
                            <div class="order_info_right">
                                <h3>
                                    {{
                                        source.productDisplayName &&
                                        source.productDisplayName[locale]
                                    }}
                                </h3>
                                <div class="game_name">
                                    {{
                                        source.productDisplayName &&
                                        source.gameDisplayName[locale]
                                    }}
                                </div>
                                <div class="server_name">
                                    {{
                                        source.schemeDisplayName &&
                                        source.schemeDisplayName[locale]
                                    }}
                                    | {{ source.server_name }}
                                    {{ source.role_name }}
                                </div>
                            </div>
                        </div>
                        <div class="order_item_right">
                            <div class="order_price">
                                {{ `${source.currency}${source.amount}` }}
                            </div>
                            <div
                                v-if="source.order_status_code === 1"
                                class="order_btn"
                                @click="cannelOrder(source.order_id)"
                            >
                                {{ $t("pages.pay.cancel-order") }}
                            </div>
                            <div class="order_type" v-else>
                                {{ $t("pages.pay.payment-mode") }}:
                                {{ source.pay_method }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <div v-else-if="totalNum === 0" class="order_list_empty">
                <div class="c_content_empty">
                    <div class="c_status_empty"></div>
                </div>
                <div>{{ $t("pages.pay.empty-desc") }}</div>
            </div>
        </div>
        <div v-if="totalNum > 0" class="order_pagination">
            <a-pagination
                show-quick-jumper
                v-model="params.page"
                :total="totalNum"
                @change="onChangePage"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getAxiosProjectList } from "~/api/pay";
import { getOrderList, type OrderListParams, postCloseOrder } from "~/api/my";
import { useUserStore } from "~/stores/user";
const { banner_pc, banner_mb } = useUserStore();
const bannerMyList = ref({
    pc: banner_pc,
    mb: banner_mb,
});

// 控制选择游戏锁定
const gamaChangeBar = ref(false);

// 登录态管理
import { useAuthStore } from "~/stores/auth";
const authStore = useAuthStore();

// 语言
const { locale } = useI18n();

const orderStatusList = ref([
    {
        label: "pages.pay.pending-payment",
        value: 1,
        style: "blue",
    },
    {
        label: "pages.pay.closed",
        value: 2,
        style: "gray",
    },
    {
        label: "pages.pay.pending-shipment",
        value: 3,
        style: "blue",
    },
    {
        label: "pages.pay.completed",
        value: 4,
        style: "gray",
    },
]);
const titleList = ref([
    {
        title: "pages.pay.all",
        key: 0,
    },
    {
        title: "pages.pay.pending-payment",
        key: 1,
    },
    {
        title: "pages.pay.pending-shipment",
        key: 3,
    },
    {
        title: "pages.pay.completed",
        key: 4,
    },
    {
        title: "pages.pay.closed",
        key: 2,
    },
]);
const roleInfo: any = false;
// 配置
const params = ref<OrderListParams>({
    project_id: 0,
    // role_id: "94570001000050",
    role_id: roleInfo?.roleId || undefined,
    order_status: 0, // 1  待支付   2  关闭  3  等待发货   4  完毕
    page: 1,
    page_size: 10,
});
//当前菜单tab位置
const tabIndex = ref(0);

// 项目列表
const projectList = ref<any[]>([]);
// 加载ing
const loading = ref<boolean>(true);
// 总数
const totalNum = ref<number>(0);

//获取订单列表
const dataList = ref<any[]>([]);
const orderListData = async (params: OrderListParams) => {
    let getData: any = await getOrderList(params);
    loading.value = false;
    if ((getData.code && getData.code == 200) || getData.code == 0) {
        loading.value = false;
        const { orders, total } = getData.data;
        // console.log(orders)
        dataList.value = orders || [];
        //console.log('total', total)
        totalNum.value = total || 0;
        // console.log('this.dataList', dataList.value)
    }
};

// 切换游戏
const handleGame = async (value: any) => {
    // console.log(`handleGame ${value}`)
    params.value.project_id = value;
    params.value.page = 1;
    loading.value = true;
    // console.log(params.value)
    await orderListData(params.value);
};
// 切换页面
const onChangePage = async (page: number) => {
    params.value.page = page;
    loading.value = true;
    console.log(params.value);
    await orderListData(params.value);
};
// tab 订单选项
const changeTabs = async (key: number) => {
    // console.log(key)
    tabIndex.value = key;
    params.value.order_status = key;
    params.value.page = 1;
    loading.value = true;
    await orderListData(params.value);
};
// 取消订单
const cannelOrder = async (orderId: number) => {
    // console.log('cannelOrder orderId', orderId)
    try {
        const result: any = await postCloseOrder({
            roleId: roleInfo?.roleId || "",
            orderId,
        });

        if ((result && result.code == 200) || result.code == 0) {
            message.success({
                content: () => result.message || "OK",
                class: "c_message_big",
                style: {
                    marginTop: "20vh",
                },
                duration: 3,
            });
            params.value.page = 1;
            await orderListData(params.value);
        } else {
            message.error({
                content: () => result.message,
                class: "c_message_big",
                style: {
                    marginTop: "20vh",
                },
                duration: 3,
            });
        }
    } catch (err) {
        console.error(err);
    }
};
// 请求开关
let getDataSwitch = false;
let num = 1;
let stopWatchEffect: any;
const srcollFixedMenu = () => {
    let h = Math.floor((screen.width / 640) * 280) + 10;
    if (window.scrollY > h) {
        gamaChangeBar.value = true;
    } else {
        gamaChangeBar.value = false;
    }
};
onMounted(async () => {
    // authStore.hideLoading();
    if (process.client) {
        try {
            // 获取 游戏列表
            let localVal: any = localStorage.getItem("projectList");
            localVal == "undefined" || !localVal;
            let projectListLocal: any[] =
                localVal == "undefined" || !localVal
                    ? localStorage.removeItem("projectList")
                    : JSON.parse(localVal);
            // console.log(projectListLocal?.length)
            if (projectListLocal && projectListLocal.length > 0) {
                projectList.value = projectListLocal;
            } else {
                const result: any = await getAxiosProjectList();
                if ((result && result.code == 200) || result.code == 0) {
                    let { data } = result;
                    projectList.value = data.projects;
                    localStorage.setItem(
                        "projectList",
                        JSON.stringify(data.projects)
                    );
                } else {
                    message.error({
                        content: result.message,
                        class: "c_message_big",
                        style: {
                            marginTop: "20vh",
                        },
                        duration: 3,
                    });
                }
            }
        } catch (err) {
            console.log(err);
        }
        // 判断登录态
        // console.log(authStore.isLogin)
        if (!authStore.isLogin) {
            authStore.setLoginModal(true);
            getDataSwitch = true;
        } else {
            //获取订单列表
            await orderListData(params.value);
        }
        stopWatchEffect = watchEffect(async () => {
            console.log(num++);
            if (authStore.isLogin && getDataSwitch) {
                await orderListData(params.value);
                getDataSwitch = false;
            }
        });

        // 添加滚动监听
        window.addEventListener("scroll", srcollFixedMenu, { passive: true });
    }
});

onUnmounted(() => {
    stopWatchEffect();
    window.removeEventListener("scroll", srcollFixedMenu);
});
</script>

<style lang="scss">
.project_select {
    --ps_height: 40px;
    width: 268px;
    height: var(--ps_height);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    font-size: 16px;

    .ant-select-selector {
        height: 100% !important;
        padding-right: 20px !important;
    }

    .ant-select-selection-item {
        font-size: 16px;
        line-height: var(--ps_height) !important;
    }

    .ant-select-arrow {
        right: 16px !important;
    }
}

@media screen and (max-width: 1280px) {
    .project_select {
        --ps_height: 32px;

        .ant-select-selection-item {
            font-size: 14px;
        }
    }
    .project_select_wrap {
        // position: absolute;
        // left: 0;
        // top: -70px;
        // z-index: 98;
        width: 100%;

        .project_select {
            width: 100%;
            --ps_height: 50px;

            .ant-select-selector {
                border-radius: 10px;
            }
        }
    }
}

@media screen and (max-width: 980px) {
    .project_select {
        --ps_height: 30px;
        width: 200px;
    }
    
}

@media screen and (max-width: 768px) {
    .project_select_wrap {
        // position: absolute;
        // left: 0;
        // top: -70px;
        // z-index: 98;
        width: 100%;

        .project_select {
            width: 100%;
            --ps_height: 50px;

            .ant-select-selector {
                border-radius: 10px;
            }
        }
    }
}

@media screen and (max-width: 640px) {
    .project_select_wrap {
        // top: -60px;
        padding: 10px 0;

        &.fixed {
            position: fixed;
            background: #f6f6f6;
            top: 50px;
            left: 0;
            z-index: 99;
            padding: 10px 14px;
        }

        .project_select {
            --ps_height: 40px;
        }
    }
}
</style>
<style lang="scss" scoped>
@import url(~/assets/styles/account/mylist.scss);
</style>
