<template>
    <div class="policy_wrap">
        <!-- <div class="c_loading" v-if="loadingShow"></div>
            <component :is="currentTemplate" v-else></component> -->
        <Suspense>
            <AsyncComponent></AsyncComponent>
        </Suspense>
    </div>
</template>

<script lang="ts" setup>
import { useAuthStore } from '~/stores/auth.js';
const authStore = useAuthStore();
// const currentTemplate = shallowRef(null);
const { locale } = useI18n();
const route = useRoute();
// const loadingShow = ref(true);
// const loadTemplate = async () => {
//     currentTemplate.value = await defineAsyncComponent(() =>
//         import(`~/views/policy/${route.params.label}/${locale.value}.vue`)
//     );
//     loadingShow.value = false;
// };
// loadTemplate();
const AsyncComponent = defineAsyncComponent(() =>
    import(`~/views/policy/${route.params.label}/${locale.value}.vue`)
);

onMounted(() => {
    let id = '';
    switch (route.params.label) {
        case 'cookies':
            id = '301';
            break;
        case 'refund':
            id = '401';
            break;
        case 'website':
            id = '501';
            break;
        case 'privacy':
            id = '601';
            break;
    }
    // 埋点: 低通页面加载结果 0:成功 1:失败
    YKTrack.track('view', {
        params: {
            id: id,
            value1: 0,
        }
    })
    // authStore.hideLoading();
});
// const headerBgColor = useState('headerBgColor', () => "");
// headerBgColor.value = "blue";
</script>
<style lang="scss" scoped>
.policy_wrap {
    padding: 80px 0 30px;
}
</style>
