import axios from 'axios'

export default defineNuxtPlugin((nuxtApp) => {
    // 创建 Axios 实例
    const api = axios.create({
        header: {
            "content-type": "application/json;charset=UTF-8",
            // "Authorization": app && app.globalData && app.globalData.auth_token || ""
        },
    })

    // 请求拦截器
    api.interceptors.request.use(
        (config) => {
            // 例如: 添加 auth_token 到请求头
            const auth_token = localStorage.getItem('auth_token');
            if (auth_token) {
                config.headers.Authorization = `Bearer ${auth_token}`;
            }
            return config;
        },
        (error) => {
            return Promise.reject(error);
        }
    )

    // 响应拦截器
    api.interceptors.response.use(
        (response) => {
            return response.data; // 返回 data 属性，方便使用
        },
        (error) => {
            // 例如: 处理错误，例如 401 未授权
            if (error.response?.status === 401) {
                // 跳转到登录页面
                navigateTo('/login');
            }
            return Promise.reject(error);
        }
    )

    // 将 axios 实例注入到 Nuxt 应用实例
    // nuxtApp.provide('axios', api)
    return {
        provide: {
            axios: api
        }
    }
})