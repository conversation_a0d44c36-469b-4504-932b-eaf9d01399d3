<template>
  <div class="page-account-upgrade">

    <template v-if="isShowEmailLogin">
      <div class="login-tips">
        <p>{{ t('pages.inner.updateAccount.1') }}</p>
        <!--        <p>游戏账号升级进行中！<span>请使用邮箱验证码登录的玩家完成账号升级</span>。</p>-->
        <p v-html="t('pages.inner.updateAccount.2', {span: `<span>`, spanEnd: `</span>`})"></p>
        <!--        <p><span>后续我们将不再支持邮箱验证码登录</span>。为避免影响您的游戏体验，建议您尽快完成升级。 </p>-->
        <p v-html="t('pages.inner.updateAccount.3', {span: `<span>`, spanEnd: `</span>`})"></p>
        <p>{{ t('pages.inner.updateAccount.4') }}</p>
      </div>
      <div class="login-email">
        <input class="input-item" type="text" :placeholder="t('pages.inner.updateAccount.5')" v-model="iptEmail"/>
        <div class="input-item-group">
          <input type="text" :placeholder="t('pages.inner.updateAccount.6')" v-model="iptCode"/>
          <button @click="clickSendCode">{{
              iptTimeCount > 0 ? `${iptTimeCount}s` : t('pages.inner.updateAccount.7')
            }}
          </button>
        </div>
        <button class="input-submit" @click="submitLogin">{{ t('pages.inner.updateAccount.24') }}</button>
      </div>
    </template>

    <template v-if="isShowUpgradeList">
      <div class="upgrade-list-tips">
        <p>{{ t('pages.inner.updateAccount.1') }}</p>
        <p>{{ t('pages.inner.updateAccount.8') }}</p>
        <p>{{ t('pages.inner.updateAccount.9') }}</p>
        <p><span>{{ t('pages.inner.updateAccount.10') }}</span></p>
      </div>
      <ul class="upgrade-list">
        <li v-for="item in rolesList">
          <div class="upgrade-list-title">{{ gameName }}</div>
          <div class="upgrade-list-content">
            <span>{{ item.server_name || '-' }}</span>
            {{ item.role || '-'  }}
          </div>
          <div class="upgrade-list-last">
            <div class="upgrade-list-last-left">
              <span>{{ t('pages.inner.updateAccount.11') }}</span>
              {{ item.last_login_time || '-'  }}
            </div>
            <div class="upgrade-list-last-right">
              <button @click="clickUpgrade(item)">{{ t('pages.inner.updateAccount.12') }}</button>
            </div>
          </div>
        </li>
      </ul>
    </template>

    <template v-if="isShowBindAccount">
      <div class="bind-account-tips">
        <p>{{ t('pages.inner.updateAccount.13') }}</p>
        <!--        <p>我们会将升级结果发送至{{ email }}<span>邮件内包含新邮箱账号和密码，请务必妥善保管，避免信息泄露。</span></p>-->
        <p v-html="t('pages.inner.updateAccount.14', {XX: email, span: `<span>`, spanEnd: `</span>`})" style="display: flex;flex-flow: column;"></p>
        <p><span>{{ t('pages.inner.updateAccount.15') }}</span></p>
      </div>
      <div class="bind-account">
        <div class="bind-account-top">
          <span>{{ gameName }}</span>
          <span>{{ upgradeItem.server_name ?? '---' }}</span>
          <span>{{ upgradeItem.role ?? '---' }}</span>
        </div>
        <input class="input-item" type="text" :placeholder="t('pages.inner.updateAccount.5')" v-model="username"/>
        <div style="position: relative;">
          <input class="input-item" :type="isShowPassword?'text':'password'" :placeholder="t('pages.inner.updateAccount.36')" v-model="password" style="padding-right: 50px;"/>
          <img v-if="isShowPassword" @click="isShowPassword = false" style="position: absolute;bottom: 6px;right: 6px" src="~/assets/images/inner/account_update/eye.png" alt="" width="28">
          <img v-else @click="isShowPassword = true" style="position: absolute;bottom: 6px;right: 6px" src="~/assets/images/inner/account_update/eye2.png" alt="" width="28">
        </div>
        <div style="position: relative;">
          <input class="input-item" :type="isShowConfirmPassword?'text':'password'" :placeholder="t('pages.inner.updateAccount.17')" v-model="confirmPassword" style="padding-right: 50px;"/>
          <img v-if="isShowConfirmPassword" @click="isShowConfirmPassword = false" style="position: absolute;bottom: 6px;right: 6px" src="~/assets/images/inner/account_update/eye.png" alt="" width="28">
          <img v-else @click="isShowConfirmPassword = true" style="position: absolute;bottom: 6px;right: 6px" src="~/assets/images/inner/account_update/eye2.png" alt="" width="28">
        </div>
        <button class="input-submit" @click="submitBoundAccount">{{ t('pages.inner.updateAccount.18') }}</button>
      </div>
    </template>

    <template v-if="isUpgradeSuccess">
      <div class="upgrade-success-tips">
        <p>{{ t('pages.inner.updateAccount.19') }}</p>
        <p><span>{{ t('pages.inner.updateAccount.20') }}</span></p>
      </div>
      <div class="upgrade-success-back">
        <button class="input-submit" @click="clickClosPage">{{ t('pages.inner.updateAccount.21') }}</button>
        <button class="input-submit" @click="clickEnterGame">{{ t('pages.inner.updateAccount.22') }}</button>
      </div>
    </template>

    <template v-if="isNotice">
      <div v-if="isNoticeMask" style="overflow: hidden;position: fixed;top:0;left:0;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.3);"></div>
      <!--        <div v-if="isNoticeMask" style="position: fixed;top:0;left:0;width: 100vw;height: 100vh;"></div>-->
      <div class="notice">
        <div class="top">{{ noticeTitle }}</div>
<!--        <div class="content" :style="{height: noticeContentHeight, overflow: 'auto'}">-->
        <div class="content" :style="{overflow: 'auto'}">
          <p v-for="p in noticeContent">{{ p }}</p>
        </div>
        <div class="bottom">
          <button @click="clickClosPage">{{ t('pages.inner.updateAccount.23') }}</button>
        </div>
      </div>
    </template>

  </div>
</template>
<script lang="ts" setup>
import {
  setAccountUpdateToken,
  emailLogin,
  sendEmailLoginCode,
  toUpgradeAccount,
  boundAccount
} from "~/api/account_update";
import {useAuthStore} from '~/stores/auth.js';
import {useUserStore} from '~/stores/user.js';
// import { format } from 'date-fns'

const {$jsbridge} = useNuxtApp() as any;
// 不使用布局
definePageMeta({
  layout: false
})
// 占位标题 防止webview用url代替标题显示（导致android内嵌顶部操作栏UIL文字长度溢出）
useHead({
  title: "PLAYBEST",
});
const {t, setLocale} = useI18n({
  useScope: 'local',
  locale: 'en',
  fallbackLocale: 'en',
})

const route = useRoute();
const userStore = useUserStore();
const authStore = useAuthStore();
console.log(userStore)
console.log(authStore)

const lang = ref('en');


const iptEmail = ref('');
const iptCode = ref('');
const iptTimeCount = ref(0);


const email = ref('');
const game = ref('');
const rolesList = ref<any[]>([{
  'server_name': '---',
  'role': '---',
}]);


const username = ref('');
const password = ref('');
const isShowPassword = ref(false);
const confirmPassword = ref('');
const isShowConfirmPassword = ref(false);
const bindResult = ref({});


const isShowEmailLogin = ref(false);
const isShowUpgradeList = ref(false);
const isShowBindAccount = ref(false);
const isUpgradeSuccess = ref(false);
const isNotice = ref(false);
const isNoticeMask = ref(false);
// const isNoticeMask2 = ref(false);

// const boxWidth = ref('auto');
// const noticeWidth = ref('calc(100% - (26px * 2))');
// const noticeWidth = ref('auto');
const noticeContentHeight = ref('auto');
// const noticeContentHeight = ref('auto');
const noticeTitle = ref('');
const noticeContent = ref<any[]>([]);

const appId = ref(0);
const gameId = ref('');
const gameName = ref('');
const channelId = ref('');

const upgradeItem = ref({});

const success = (msg: string) => {
  message.success({
    content: msg,
    class: 'c_message_big',
    style: {
      marginTop: '20vh',
    },
    duration: 3,
  });
};

const error = (msg: string) => {
  message.error({
    content: msg,
    class: 'c_message_big',
    style: {
      marginTop: '20vh',
    },
    duration: 3,
  });
};

class ValidateHelper {
  static validateEmail(email: string) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  }

  static validatePassword(password: string) {
    const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/;
    return passwordPattern.test(password);
  }
}

const successUpgrade = (data: any) => {
  bindResult.value = data;
  $jsbridge.gsInvokeNative({
    'name': 'account_upgrade',
    'data': {
      "token": bindResult.value.token,
      "user_id": bindResult.value.uid,
      "account": bindResult.value.username ?? username.value,
    },
  })
};

const getUpgradeAccountList = async () => {
  toUpgradeAccount(appId.value, gameId.value, gameName.value, channelId.value, lang.value).then(res => {
    // code, data, message
    if (res.code == 0) {
      isShowUpgradeList.value = true;
      email.value = res.data.email
      game.value = res.data.game
      if(res.data.roles.length > 0){
        rolesList.value = res.data.roles.map(item => {
          // const format = moment("2024-12-11 12:14:21 (GMT+8)").format('YYYY-MM-DD HH:mm:ss');
          // item.last_login_time = format(item.last_login_time, 'yyyy-MM-dd HH:mm:ss')
          return item;
        })
      }
    } else if (res.code === 2011) {
      // 邮箱尚未注册
      noticeTitle.value = t('pages.inner.updateAccount.25');
      noticeContent.value = [
        t('pages.inner.updateAccount.1'),
        t('pages.inner.updateAccount.26'),
        t('pages.inner.updateAccount.27'),
      ];
      isNotice.value = true;
    } else if (res.code === 2043) {
      // 账号无需升级
      noticeTitle.value = t('pages.inner.updateAccount.28');
      noticeContent.value = [
        t('pages.inner.updateAccount.1'),
        t('pages.inner.updateAccount.29'),
      ];
      isNotice.value = true;
      successUpgrade(res.data) // 兼容app端
    } else {
      error(res.message);
    }
  })
};


const clickSendCodeLock = ref(false);
const clickSendCode = async () => {
  if (iptTimeCount.value > 0) {
    return;
  }
  if (clickSendCodeLock.value) {
    return;
  }
  clickSendCodeLock.value = true
  const res = await sendEmailLoginCode(appId.value, gameId.value, iptEmail.value, lang.value);
  if (res.code === 0) {
    success(t('pages.inner.updateAccount.30'))
    iptTimeCount.value = 60;
    (function startTimer() {
      setTimeout(() => {
        if (iptTimeCount.value > 0) {
          iptTimeCount.value--;
          startTimer();
        }
      }, 1000);
    })();
  } else if (res.code === 2028) {
    error(t('pages.inner.updateAccount.31'))
  } else if (res.code === 2011) {
    // error(t('pages.inner.updateAccount.32'))
    // 邮箱尚未注册
    noticeTitle.value = t('pages.inner.updateAccount.37');
    noticeContent.value = [
      t('pages.inner.updateAccount.1'),
      t('pages.inner.updateAccount.26'),
      t('pages.inner.updateAccount.27'),
    ];
    isNotice.value = true;
    isNoticeMask.value = true;
  } else if (res.code === 2043) {
    // 账号无需升级
    noticeTitle.value = t('pages.inner.updateAccount.28');
    noticeContent.value = [
      t('pages.inner.updateAccount.1'),
      t('pages.inner.updateAccount.29'),
    ];
    isNotice.value = true;
    isNoticeMask.value = true;
  } else {
    error(res.message)
  }
  clickSendCodeLock.value = false
};

const submitLoginLock = ref(false);
const submitLogin = async () => {
  if (submitLoginLock.value) {
    return;
  }
  submitLoginLock.value = true
  const res = await emailLogin(appId.value, gameId.value, iptEmail.value, iptCode.value, lang.value);
  if (res.code === 0) {
    isShowEmailLogin.value = false;
    getUpgradeAccountList() // TODO test
    // clickUpgrade({
    //   'server_name': 'server_name',
    //   'role': 'role',
    // })
  } else {
    error(res.message)
  }
  submitLoginLock.value = false
};

const clickUpgrade = async (item: any) => {
  isShowUpgradeList.value = false;
  isShowBindAccount.value = true;
  upgradeItem.value = item;
};

const submitBoundAccountLock = ref(false);
const submitBoundAccount = async () => {
  if (!ValidateHelper.validateEmail(username.value)) {
    error(t('pages.inner.updateAccount.31'));
    return;
  }
  if (password.value.length < 8) {
    error(t('pages.inner.updateAccount.35'));
    return;
  }
  if (password.value.length > 20) {
    error(t('pages.inner.updateAccount.35'));
    return;
  }
  if (!ValidateHelper.validatePassword(password.value)) {
    error(t('pages.inner.updateAccount.34'));
    return;
  }
  if (password.value !== confirmPassword.value) {
    error(t('pages.inner.updateAccount.33'));
    return;
  }
  if (submitBoundAccountLock.value) {
    return;
  }
  submitBoundAccountLock.value = true
  const res = await boundAccount(appId.value, gameId.value, username.value, password.value, confirmPassword.value, lang.value);
  if (res.code == 0) {
    successUpgrade(res.data)
    isShowBindAccount.value = false;
    isUpgradeSuccess.value = true;
  }else if (res.code === 2011) {
    // 邮箱尚未注册
    noticeTitle.value = t('pages.inner.updateAccount.25');
    noticeContent.value = [
      t('pages.inner.updateAccount.1'),
      t('pages.inner.updateAccount.26'),
      t('pages.inner.updateAccount.27'),
    ];
    isNotice.value = true;
    isNoticeMask.value = true;
  } else if (res.code === 2043) {
    // 账号无需升级
    noticeTitle.value = t('pages.inner.updateAccount.28');
    noticeContent.value = [
      t('pages.inner.updateAccount.1'),
      t('pages.inner.updateAccount.29'),
    ];
    isNotice.value = true;
    isNoticeMask.value = true;
  } else {
    error(res.message);
  }
  submitBoundAccountLock.value = false
};

const clickClosPage = () => {
  // $jsbridge.close();
  $jsbridge.gsInvokeNative({
    'name': 'account_upgrade_close',
    'data': {}, // android 实现需要提供空对象
  })
};

const clickEnterGame = () => {
  $jsbridge.gsInvokeNative({
    'name': 'account_upgrade_enter_game',
    'data': {}, // android 实现需要提供空对象
  })
};

onMounted(() => {
  // const windowWidth = window.innerWidth;
  // const windowHeight = window.innerHeight;
  // if(windowWidth > windowHeight){
  //   // // 检查并移除旧的 meta 标签
  //   // const existingMeta = document.querySelector('meta[name="viewport"]');
  //   // if (existingMeta) {
  //   //   existingMeta.remove();
  //   // }
  //   // // 动态添加新的 meta 标签
  //   // const meta = document.createElement('meta');
  //   // meta.name = 'viewport';
  //   // meta.content = `width=${windowHeight}, initial-scale=1.0`;
  //   // document.head.appendChild(meta);
  //   // document.body.style.width = `${windowHeight}px`;
  //   // document.body.style.margin = '0 auto';
  //   boxWidth.value= `${windowHeight + 100}px`;
  //   noticeWidth.value= `${windowHeight + 100 - 52}px`;
  //   noticeContentHeight.value= `${window.innerHeight - (38 * 2) - 70 - 100}px`;
  //   noticeContentHeight.value= `${windowHeight + 100 - (38 * 2) - 70 - 100}px`;
  // }

  $jsbridge.switchBarChange(2) // 保险期间 兼容下ios 可能也不需要
  setTimeout(()=>{
    document.title = t('pages.inner.updateAccount.0')
  }, 100)
  // https://payment-test.playbest.net/inner/account_update?appId=********&gameId=27&gameName=自在西游&channelId=239&lang=zh-Hans&accessToken=
  appId.value = Number(route.query['appId']);
  gameId.value = route.query['gameId'];
  gameName.value = route.query['gameName'];
  channelId.value = route.query['channelId'];
  lang.value = route.query['lang'];
  const accessToken = route.query['accessToken'];
  // const accessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.D529stt6QjWPUV51qJBjvOCzYQIFZD8CsbKgbX_VATA';
  // success(JSON.stringify(route.query))
  let langs = ['en','id','ja','ko','th','zh-Hans','zh-Hant']
  if(langs.includes(lang.value)){
    setLocale(lang.value)
  }else{
    setLocale('en')
  }
  if (accessToken !== undefined && accessToken !== '') {
    setAccountUpdateToken(accessToken)
    getUpgradeAccountList() // TODO test
    // clickUpgrade({
    //   'server_name': 'server_name',
    //   'role': 'role',
    // })
  } else {
    isShowEmailLogin.value = true;
  }
  // noticeTitle.value = t('pages.inner.updateAccount.37');
  // noticeContent.value = [
  //   t('pages.inner.updateAccount.1'),
  //   t('pages.inner.updateAccount.26'),
  //   t('pages.inner.updateAccount.27'),
  // ];
  // isNotice.value = true;
  // isNoticeMask.value = true;
});
</script>
<style lang="scss">
//.header_m,
//.menu-close{
//  display: none !important;
//}
.ant-message-custom-content span:last-child {
  word-break: break-word;
}
</style>
<style lang="scss" scoped>
.page-account-upgrade {
  min-height: 100vh;
  background-image: url("~/assets/images/inner/account_update/bg.png");
  background-size: 100% 100%;
  padding-top: 38px;
  padding-bottom: 100px;
}

.login-tips,
.upgrade-list-tips,
.bind-account-tips,
.upgrade-success-tips {
  margin: 0 26px 0;
  padding: 22px 19px 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  //font-size: 20px;
  > p {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    :deep(span)  {
      color: #466DF6;
    }
  }
}

.input-item {
  width: 100%;
  height: 40px;
  outline: 0;
  border: 0;
  border-radius: 8px;
  padding-left: 16px;
}

.input-item-group {
  display: flex;
  height: 40px;

  > input {
    flex: 1;
    outline: 0;
    border: 0;
    border-radius: 8px;
    padding-left: 16px;
  }

  position: relative;

  > button {
    outline: 0;
    border: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding: 0 20px;
    margin: auto;
    height: 30px;
    background-color: transparent;
    border-left: 1px solid #D9D9D9;
    color: #848484;
  }
}

.input-submit {
  border: 0;
  outline: 0;
  width: 100%;
  height: 40px;
  background: #4E83E4;
  color: #fff;
  border-radius: 8px;

  .input-item-group {
    margin-top: 18px;

    > button {
      color: #848484;
    }
  }
}

.login-tips {
  margin-bottom: 12px;
}

.login-email {
  margin: 0 26px;

  .input-item {
    margin-top: 18px;
  }

  .input-item-group {
    margin-top: 18px;
  }

  .input-submit {
    margin-top: 30px;
  }
}

.upgrade-list {
  margin: 58px 26px 0;

  > li {
    border-radius: 16px;
    overflow: hidden;
    background: #FFF;
    margin-bottom: 24px;
    padding-bottom: 14px;

    .upgrade-list-title {
      padding: 8px 19px 5px;
      background: linear-gradient(90deg, rgb(225, 231, 251) 5.5%, rgb(241, 244, 253) 100%);
      font-size: 16px;
    }

    .upgrade-list-content {
      padding: 7px 19px 5px;
      display: flex;
      color: #466DF6;

      span {
        width: 120px;
      }
    }

    .upgrade-list-last {
      padding: 0 19px;
      display: flex;
      justify-content: space-between;

      .upgrade-list-last-left {
        display: flex;
        flex-flow: column;
        color: #5F5F5F;
      }

      .upgrade-list-last-right {
        button {
          background: #4E83E4;
          border: 0;
          outline: 0;
          color: #fff;
          width: 100px;
          height: 32px;
          border-radius: 40px;
        }
      }
    }
  }
}

.bind-account {
  margin: 37px 26px 0;

  .bind-account-top {
    display: flex;
    justify-content: space-between;
    padding: 0 23px;
    >span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .input-item {
    margin-top: 18px;
  }

  .input-submit {
    margin-top: 30px;
  }
}

.upgrade-success-back {
  margin: 0 44px 44px;
  display: flex;
  padding-top: 60px;

  > button {
    flex: 1;
  }

  > button:first-child {
    border-radius: 8px;
    border: 1px solid #4E83E4;
    background: rgba(255, 255, 255, 0.25);
    color: #333;
    margin-right: 14px;
  }
}

.notice {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  border-radius: 8px;
  background: #fff;
  margin: 38px 26px 0;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  .top {
    display: flex;
    justify-content: center;
    padding: 12px 0 12px;
    font-size: 18px;
    border-bottom: 1px solid #ECECF0;
  }

  .content {
    padding: 12px 30px 0;
    font-size: 16px;

    > p {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .bottom {
    display: flex;
    justify-content: center;
    padding: 12px 0;

    > button {
      width: 200px;
      height: 40px;
      border: 0;
      outline: 0;
      background: #4E83E4;
      color: #fff;
      border-radius: 8px;
    }
  }
}
</style>
<i18n lang="json">
{
  "en": {
    "pages.inner.updateAccount.0": "Upgrade Account",
    "pages.inner.updateAccount.1": "Dear Player, ",
    "pages.inner.updateAccount.2": "Your game account is undergoing an upgrade process! {span}Players who currently log in using email verification codes are required to complete the upgrade.{spanEnd}",
    "pages.inner.updateAccount.3": "{span}Email verification code login will no longer be supported.{spanEnd} To ensure a seamless gaming experience, we recommend that you complete the upgrade as soon as possible.",
    "pages.inner.updateAccount.4": "Please enter your email and verification code, then click \"Next\".",
    "pages.inner.updateAccount.5": "Email Address",
    "pages.inner.updateAccount.6": "Please enter the verification code",
    "pages.inner.updateAccount.7": "Get",
    "pages.inner.updateAccount.8": "Email verification code login will no longer be supported. To ensure a seamless gaming experience, we recomment that players who currently log in using email verification codes are required to complete the upgrade.",
    "pages.inner.updateAccount.9": "After clicking \"Upgrade\", you will need to reset your email and password.",
    "pages.inner.updateAccount.10": "Upon completion of the upgrade, you can log in using your email and password.",
    "pages.inner.updateAccount.11": "Last seen",
    "pages.inner.updateAccount.12": "Upgrade",
    "pages.inner.updateAccount.13": "Please enter your new email and password, and click \"Confirm\" to complete the setup.",
    "pages.inner.updateAccount.14": "We will send the upgrade results to {XX}. {span}The email will contain your new email and password. Please keep this information confidential.{spanEnd}",
    "pages.inner.updateAccount.15": "Upon completion of the upgrade, you can log in using your email and password.",
    "pages.inner.updateAccount.16": "Password must be 8-20 characters, and must include a combination of letters and numbers.",
    "pages.inner.updateAccount.17": "Please re-enter your password.",
    "pages.inner.updateAccount.18": "Confirm",
    "pages.inner.updateAccount.19": "Congratulations! Your account upgrade is complete!",
    "pages.inner.updateAccount.20": "Upon completion of the upgrade, you can log in using your email and password.",
    "pages.inner.updateAccount.21": "Close the Page",
    "pages.inner.updateAccount.22": "Enter the Game",
    "pages.inner.updateAccount.23": "Log in",
    "pages.inner.updateAccount.24": "Next",
    "pages.inner.updateAccount.25": "Email address not registered.",
    "pages.inner.updateAccount.26": "Email verification codes are only used for the account upgrade process. They cannot be used to register or log in to your game account.",
    "pages.inner.updateAccount.27": "Please use other methods to register or log in.",
    "pages.inner.updateAccount.28": "Account does not need to be upgraded",
    "pages.inner.updateAccount.29": "Your account does not require an upgrade. Please log in using your email and password as usual.",
    "pages.inner.updateAccount.30": "Sent",
    "pages.inner.updateAccount.31": "Please enter a valid email",
    "pages.inner.updateAccount.32": "Email address not registered.",
    "pages.inner.updateAccount.33": "The two passwords entered do not match, please re-enter",
    "pages.inner.updateAccount.34": "Password must contain a combination of at least two of the following: letters, numbers, and special characters",
    "pages.inner.updateAccount.35": "Password must be between 8-20 characters",
    "pages.inner.updateAccount.36": "Password",
    "pages.inner.updateAccount.37": "Notification"
  },
  "id": {
    "pages.inner.updateAccount.0": "Peningkatan Akun",
    "pages.inner.updateAccount.1": "Pemain terhormat:",
    "pages.inner.updateAccount.2": "Peningkatan akun game sedang berlangsung! {span}Pemain yang masuk menggunakan kode verifikasi email silakan selesaikan peningkatan akun.{spanEnd}",
    "pages.inner.updateAccount.3": "{span}Saat ini, login dengan kode verifikasi email tidak didukung.{spanEnd} Untuk menghindari mempengaruhi pengalaman bermain game Anda, kami sarankan Anda menyelesaikan peningkatan secepat mungkin.",
    "pages.inner.updateAccount.4": "Masukkan email dan kode verifikasi, lalu klik langkah berikutnya.",
    "pages.inner.updateAccount.5": "Alamat Email",
    "pages.inner.updateAccount.6": "Kode Verifikasi",
    "pages.inner.updateAccount.7": "Dapatkan",
    "pages.inner.updateAccount.8": "Saat ini tidak mendukung login dengan kode verifikasi email. Untuk menghindari pengaruh terhadap pengalaman bermain game Anda, pemain yang masuk dengan kode verifikasi email harap menyelesaikan peningkatan akun.",
    "pages.inner.updateAccount.9": "Setelah mengklik peningkatan, Anda perlu mengatur ulang akun email dan kata sandi.",
    "pages.inner.updateAccount.10": "Akun yang telah diselesaikan peningkatannya dapat masuk dengan akun email dan kata sandi.",
    "pages.inner.updateAccount.11": "Waktu login terakhir",
    "pages.inner.updateAccount.12": "Peningkatan",
    "pages.inner.updateAccount.13": "Silakan masukkan akun email baru dan kata sandi, lalu klik \"Konfirmasi\" untuk menyelesaikan pengaturan.",
    "pages.inner.updateAccount.14": "Kami akan mengirim hasil peningkatan ke {XX}.{span}Email akan berisi akun email baru dan kata sandi. Harap simpan dengan baik untuk menghindari kebocoran informasi.{spanEnd}",
    "pages.inner.updateAccount.15": "Akun yang telah diselesaikan peningkatannya dapat masuk dengan akun email dan kata sandi.",
    "pages.inner.updateAccount.16": "Kata sandi harus terdiri dari 8-20 karakter, kombinasi huruf dan angka",
    "pages.inner.updateAccount.17": "Silakan masukkan kembali kata sandi Anda.",
    "pages.inner.updateAccount.18": "Konfirmasi",
    "pages.inner.updateAccount.19": "Selamat! Peningkatan akun telah selesai!",
    "pages.inner.updateAccount.20": "Akun yang telah diselesaikan peningkatannya dapat masuk dengan akun email dan kata sandi.",
    "pages.inner.updateAccount.21": "Tutup Halaman",
    "pages.inner.updateAccount.22": "Masuk ke Game",
    "pages.inner.updateAccount.23": "Log In",
    "pages.inner.updateAccount.24": "Next",
    "pages.inner.updateAccount.25": "Email belum terdaftar",
    "pages.inner.updateAccount.26": "Kode verifikasi email hanya digunakan untuk proses peningkatan akun dan tidak mendukung digunakan untuk mendaftar atau masuk ke akun game.",
    "pages.inner.updateAccount.27": "Silakan kembali dan gunakan cara lain untuk mendaftar atau masuk.",
    "pages.inner.updateAccount.28": "Akun tidak perlu ditingkatkan",
    "pages.inner.updateAccount.29": "Akun Anda tidak perlu ditingkatkan. Silakan kembali dan masuk dengan akun email dan kata sandi.",
    "pages.inner.updateAccount.30": "Terkirim",
    "pages.inner.updateAccount.31": "Silakan masukkan email yang valid",
    "pages.inner.updateAccount.32": "Email belum terdaftar",
    "pages.inner.updateAccount.33": "Konfirmasi kata sandi tidak cocok, silakan masukkan lagi",
    "pages.inner.updateAccount.34": "Kata sandi harus terdiri dari kombinasi minimal dua jenis karakter dari huruf, angka, dan simbol khusus",
    "pages.inner.updateAccount.35": "Kata sandi harus antara 8-20 karakter",
    "pages.inner.updateAccount.36": "Kata Sandi",
    "pages.inner.updateAccount.37": "Notifikasi"
  },
  "ja": {
    "pages.inner.updateAccount.0": "新アカウントへの移行",
    "pages.inner.updateAccount.1": "ご利用中のお客様へ：",
    "pages.inner.updateAccount.2": "ームアカウントの新アカウントへの移行をお願いしています。{span}メールによる認証コードを使用してログインしている場合は、新アカウントへ移行してください。{spanEnd}",
    "pages.inner.updateAccount.3": "{span}現在、メールの認証コードによるログインはサポートされていません。{spanEnd}できるだけ早く移行し、ゲームを利用しやすくすることをおすすめします。",
    "pages.inner.updateAccount.4": "メールアドレスと認証コードを入力し、「次へ」をクリックしてください。",
    "pages.inner.updateAccount.5": "メールアドレスを入力してください",
    "pages.inner.updateAccount.6": "認証コードを入力してください",
    "pages.inner.updateAccount.7": "送信",
    "pages.inner.updateAccount.8": "現在、メールの認証コードによるログインはサポートされていません。メールによる認証コードを使用してログインしている場合は、新アカウントへ移行してください。",
    "pages.inner.updateAccount.9": "アカウント移行をクリックした後、メールアカウントとパスワードを再設定する必要があります。",
    "pages.inner.updateAccount.10": "移行が完了すると、メールアドレスとパスワードでログインできるようになります。",
    "pages.inner.updateAccount.11": "最終ログイン時刻",
    "pages.inner.updateAccount.12": "移行",
    "pages.inner.updateAccount.13": "新しいメールアカウントとパスワードを入力し、「確認」ボタンをクリックして設定を完了してください。",
    "pages.inner.updateAccount.14": "アカウント移行の結果を{XX}。{span}宛に送信します。新しいメールアカウントとパスワードが記載されていますので、大切に保管してください。{spanEnd}",
    "pages.inner.updateAccount.15": "移行が完了したアカウントは、メールアカウントとパスワードでログインできるようになります。",
    "pages.inner.updateAccount.16": "8～20桁のパスワード（英字・数字を両方含むもの）",
    "pages.inner.updateAccount.17": "パスワード確認",
    "pages.inner.updateAccount.18": "確認",
    "pages.inner.updateAccount.19": "お待たせしました！アカウントの移行が完了しました！",
    "pages.inner.updateAccount.20": "今後はメールアカウントとパスワードでログインできます。",
    "pages.inner.updateAccount.21": "ページを閉じる",
    "pages.inner.updateAccount.22": "ゲームに入る",
    "pages.inner.updateAccount.23": "ログインへ",
    "pages.inner.updateAccount.24": "次へ",
    "pages.inner.updateAccount.25": "メールアドレスはまだ登録されていません",
    "pages.inner.updateAccount.26": "メール認証コードはアカウントの移行プロセス専用で、ゲームアカウントの登録やログインには使用できません。",
    "pages.inner.updateAccount.27": "他の方法で登録またはログインしてください。",
    "pages.inner.updateAccount.28": "新アカウントへの移行は必要ありません",
    "pages.inner.updateAccount.29": "あなたのアカウントは移行する必要がありません。メールアカウントとパスワードを使ってログインしてください。",
    "pages.inner.updateAccount.30": "送信済み",
    "pages.inner.updateAccount.31": "有効なメールアドレスを入力してください",
    "pages.inner.updateAccount.32": "メールアドレスはまだ登録されていません",
    "pages.inner.updateAccount.33": "2回入力したパスワードが一致しません",
    "pages.inner.updateAccount.34": "パスワードには、半角英数字記号を2種類以上含むように設定してください",
    "pages.inner.updateAccount.35": "パスワードは8-20文字の間で入力してください",
    "pages.inner.updateAccount.36": "パスワードを入力してください",
    "pages.inner.updateAccount.37": "お知らせ"
  },
  "ko": {
    "pages.inner.updateAccount.0": "계정 업그레이드",
    "pages.inner.updateAccount.1": "플레이어님",
    "pages.inner.updateAccount.2": "게임 계정 업그레이드가 진행 중입니다! {span}메일 인증번호로 로그인하시는 플레이어님께서는 계정 업그레이드를 완료해 주세요.{spanEnd}",
    "pages.inner.updateAccount.3": "{span}현재 이메일 인증번호 로그인을 지원하지 않습니다.{spanEnd} 게임 플레이에 영향이 없도록 속히 업그레이드를 완료해 주시기 바랍니다.",
    "pages.inner.updateAccount.4": "메일과 인증번호를 입력하신 후 [다음]을 클릭해 주세요.",
    "pages.inner.updateAccount.5": "이메일을 입력하세요",
    "pages.inner.updateAccount.6": "인증번호를 입력해 주세요",
    "pages.inner.updateAccount.7": "받기",
    "pages.inner.updateAccount.8": "현재 이메일 인증번호 로그인을 지원하지 않습니다. 메일 인증번호로 로그인하시는 플레이어님께서는 계정 업그레이드를 완료해 주세요. ",
    "pages.inner.updateAccount.9": "업그레이드 클릭 후 메일 계정과 비밀번호를 재설정해야 합니다.",
    "pages.inner.updateAccount.10": "업그레이드가 완료된 계정은 메일 계정과 비밀번호로 로그인하실 수 있습니다.",
    "pages.inner.updateAccount.11": "마지막 로그인 시간",
    "pages.inner.updateAccount.12": "업그레이드",
    "pages.inner.updateAccount.13": "새로운 메일 계정과 비밀번호를 입력해 주세요. [확인]을 클릭하시면 설정이 완료됩니다.",
    "pages.inner.updateAccount.14": "업그레이드 결과를 {XX}(으)로 발송할 예정이.{span}며메일에는 새로운 메일 계정과 비밀번호가 포함되어 있으니 정보가 유출되지 않도록 보안에 유의해 주세요.{spanEnd}",
    "pages.inner.updateAccount.15": "업그레이드가 완료된 계정은 메일 계정과 비밀번호로 로그인하실 수 있습니다.",
    "pages.inner.updateAccount.16": "비밀번호는 8-20자리로, 숫자와 문자를 조합해 주세요",
    "pages.inner.updateAccount.17": "비밀번호를 다시 입력해 주세요",
    "pages.inner.updateAccount.18": "확인",
    "pages.inner.updateAccount.19": "계정 업그레이드가 완료되었습니다!업그레이드가 완료된 계정은 메일 계정과 비밀번호로 로그인하실 수 있습니다.",
    "pages.inner.updateAccount.20": "업그레이드가 완료된 계정은 메일 계정과 비밀번호로 로그인하실 수 있습니다.",
    "pages.inner.updateAccount.21": "페이지를 닫다",
    "pages.inner.updateAccount.22": "게임에 들어가다",
    "pages.inner.updateAccount.23": "로그인하기",
    "pages.inner.updateAccount.24": "다음",
    "pages.inner.updateAccount.25": "등록된 메일이 아닙니다.",
    "pages.inner.updateAccount.26": "메일 인증번호는 계정 업그레이드 용도로만 사용되며, 가입 및 게임 계정 로그인용으로는 사용하실 수 없습니다.",
    "pages.inner.updateAccount.27": "다른 방식을 통해 가입 및 로그인을 진행해 주세요.",
    "pages.inner.updateAccount.28": "계정 업그레이드 불필요",
    "pages.inner.updateAccount.29": "플레이어님의 계정은 업그레이드를 진행하실 필요가 없습니다. 메일 계정과 비밀번호로 로그인해 주세요.",
    "pages.inner.updateAccount.30": "전송 완료",
    "pages.inner.updateAccount.31": "유효한 이메일을 입력해 주세요",
    "pages.inner.updateAccount.32": "등록된 메일이 아닙니다.",
    "pages.inner.updateAccount.33": "두 번 입력한 비밀번호가 일치하지 않습니다. 다시 입력해 주세요  ",
    "pages.inner.updateAccount.34": "암호는 문자, 숫자, 특수문자 중 2가지 이상을 조합해야 합니다",
    "pages.inner.updateAccount.35": "비밀번호는 8-20자 이내여야 합니다",
    "pages.inner.updateAccount.36": "비밀번호를 입력하세요",
    "pages.inner.updateAccount.37": "공지"
  },
  "th": {
    "pages.inner.updateAccount.0": "อัพเกรดบัญชี",
    "pages.inner.updateAccount.1": "สวัสดีนักเล่นเกมทุกท่าน!",
    "pages.inner.updateAccount.2": "การอัพเกรดบัญชีเกมกำลังดำเนินการ! {span}ผู้เล่นที่เข้าสู่ระบบด้วยรหัสยืนยันอีเมลโปรดทำการอัพเกรดบัญชีให้เสร็จสมบูรณ์{spanEnd}",
    "pages.inner.updateAccount.3": "{span}ขณะนี้ไม่รองรับการเข้าสู่ระบบด้วยรหัสยืนยันอีเมล{spanEnd} เพื่อหลีกเลี่ยงไม่ให้ส่งผลกระทบต่อประสบการณ์การเล่นเกมของคุณ ขอแนะนำให้คุณทำการอัพเกรดให้เสร็จสมบูรณ์โดยเร็วที่สุด.",
    "pages.inner.updateAccount.4": "กรุณาป้อนอีเมลและรหัสยืนยัน แล้วคลิกขั้นต่อไป.",
    "pages.inner.updateAccount.5": "ที่อยู่อีเมล",
    "pages.inner.updateAccount.6": "กรุณาป้อนรหัสยืนยัน",
    "pages.inner.updateAccount.7": "รับ",
    "pages.inner.updateAccount.8": "ขณะนี้ไม่รองรับการเข้าสู่ระบบด้วยรหัสยืนยันอีเมล เพื่อหลีกเลี่ยงไม่ให้ส่งผลกระทบต่อประสบการณ์การเล่นเกมของคุณ ผู้เล่นที่เข้าสู่ระบบด้วยรหัสยืนยันอีเมลโปรดทำการอัพเกรดบัญชี.",
    "pages.inner.updateAccount.9": "หลังจากกดอัพเกรด คุณจะต้องตั้งค่าบัญชีอีเมลและรหัสผ่านใหม่.",
    "pages.inner.updateAccount.10": "บัญชีที่ได้รับการอัพเกรดแล้ว สามารถเข้าสู่ระบบด้วยบัญชีอีเมลและรหัสผ่าน.",
    "pages.inner.updateAccount.11": "เวลาเข้าสู่ระบบล่าสุด",
    "pages.inner.updateAccount.12": "อัพเกรด",
    "pages.inner.updateAccount.13": "กรุณากรอกบัญชีอีเมลใหม่และรหัสผ่าน แล้วคลิก \"ยืนยัน\" เพื่อทำการตั้งค่าให้เสร็จสมบูรณ์.",
    "pages.inner.updateAccount.14": "เราจะส่งผลการอัพเกรดไปยัง {XX} {span}อีเมลจะประกอบด้วยบัญชีอีเมลใหม่และรหัสผ่าน โปรดเก็บรักษาอย่างดีเพื่อหลีกเลี่ยงการรั่วไหลของข้อมูล.{spanEnd}",
    "pages.inner.updateAccount.15": "บัญชีที่ได้รับการอัพเกรดแล้ว สามารถเข้าสู่ระบบด้วยบัญชีอีเมลและรหัสผ่าน.",
    "pages.inner.updateAccount.16": "รหัสผ่านต้องมีความยาว 8-20 ตัวอักษร และประกอบด้วยตัวอักษรและตัวเลข",
    "pages.inner.updateAccount.17": "กรุณาป้อนรหัสผ่านอีกครั้ง",
    "pages.inner.updateAccount.18": "ยืนยัน",
    "pages.inner.updateAccount.19": "ยินดีด้วยนะคุณ! การอัพเกรดบัญชีได้สำเร็จแล้ว!",
    "pages.inner.updateAccount.20": "บัญชีที่ได้รับการอัพเกรดแล้ว สามารถเข้าสู่ระบบด้วยบัญชีอีเมลและรหัสผ่าน.",
    "pages.inner.updateAccount.21": "ปิดหน้าเพจ",
    "pages.inner.updateAccount.22": "เข้าเกม",
    "pages.inner.updateAccount.23": "ล็อกอิน",
    "pages.inner.updateAccount.24": "ขั้นต่อไป",
    "pages.inner.updateAccount.25": "อีเมลยังไม่ได้ลงทะเบียน",
    "pages.inner.updateAccount.26": "รหัสยืนยันอีเมลใช้ได้เฉพาะในกระบวนการอัพเกรดบัญชีเท่านั้น ไม่รองรับการใช้ในการลงทะเบียนหรือเข้าสู่ระบบบัญชีเกม.",
    "pages.inner.updateAccount.27": "โปรดกลับไปและใช้วิธีอื่นในการลงทะเบียนหรือเข้าสู่ระบบ.",
    "pages.inner.updateAccount.28": "บัญชีไม่ต้องทำการอัพเกรด",
    "pages.inner.updateAccount.29": "บัญชีของคุณไม่ต้องทำการอัพเกรด โปรดกลับไปเข้าสู่ระบบด้วยบัญชีอีเมลและรหัสผ่าน.",
    "pages.inner.updateAccount.30": "ถูกส่ง",
    "pages.inner.updateAccount.31": "กรุณาใส่อีเมลที่ถูกต้อง",
    "pages.inner.updateAccount.32": "อีเมลยังไม่ได้ลงทะเบียน",
    "pages.inner.updateAccount.33": "รหัสผ่านทั้งสองช่องไม่ตรงกัน กรุณาใส่ใหม่อีกครั้ง",
    "pages.inner.updateAccount.34": "รหัสผ่านต้องประกอบด้วยอักขระอย่างน้อย 2 ชนิดขึ้นไป ได้แก่ ตัวอักษร ตัวเลข และอักขระพิเศษ",
    "pages.inner.updateAccount.35": "รหัสผ่านต้องมีความยาว 8-20 ตัวอักษร",
    "pages.inner.updateAccount.36": "รหัสผ่าน",
    "pages.inner.updateAccount.37": "แจ้งเตือน"
  },
  "zh-Hans": {
    "pages.inner.updateAccount.0": "账号升级",
    "pages.inner.updateAccount.1": "尊敬的玩家：",
    "pages.inner.updateAccount.2": "游戏账号升级进行中！{span}请使用邮箱验证码登录的玩家完成账号升级。{spanEnd}",
    "pages.inner.updateAccount.3": "{span}当前不支持邮箱验证码登录。{spanEnd}为避免影响您的游戏体验，建议您尽快完成升级。",
    "pages.inner.updateAccount.4": "请输入邮箱和验证码，并点击下一步。",
    "pages.inner.updateAccount.5": "请输入邮箱",
    "pages.inner.updateAccount.6": "请输入验证码",
    "pages.inner.updateAccount.7": "获取",
    "pages.inner.updateAccount.8": "当前不支持邮箱验证码登录。为避免影响您的游戏体验，请使用邮箱验证码登录的玩家完成账号升级。",
    "pages.inner.updateAccount.9": "点击升级后，您需要重新设置邮箱账号和密码。",
    "pages.inner.updateAccount.10": "完成升级的账号可使用邮箱账号和密码登录。",
    "pages.inner.updateAccount.11": "最后登录时间：",
    "pages.inner.updateAccount.12": "升级",
    "pages.inner.updateAccount.13": "请输入新邮箱账号和密码，点击“确认”完成设置。",
    "pages.inner.updateAccount.14": "我们会将升级结果发送至{XX}{span}邮件内包含新邮箱账号和密码，请务必妥善保管，避免信息泄露。{spanEnd}",
    "pages.inner.updateAccount.15": "完成升级的账号可使用邮箱账号和密码登录。",
    "pages.inner.updateAccount.16": "密码为8-20位，字母加数字组合",
    "pages.inner.updateAccount.17": "请再次输入密码",
    "pages.inner.updateAccount.18": "确认",
    "pages.inner.updateAccount.19": "恭喜您！账号升级已完成！",
    "pages.inner.updateAccount.20": "完成升级的账号可使用邮箱账号和密码登录。",
    "pages.inner.updateAccount.21": "关闭页面",
    "pages.inner.updateAccount.22": "进入游戏",
    "pages.inner.updateAccount.23": "去登录",
    "pages.inner.updateAccount.24": "下一步",
    "pages.inner.updateAccount.25": "邮箱尚未注册",
    "pages.inner.updateAccount.26": "邮箱验证码仅用于账号升级流程，不支持用于注册或登录游戏账号。",
    "pages.inner.updateAccount.27": "请返回使用其他方式注册或登录。",
    "pages.inner.updateAccount.28": "账号无需升级",
    "pages.inner.updateAccount.29": "您的账号无需升级，请返回使用邮箱账号和密码登录。",
    "pages.inner.updateAccount.30": "已发送",
    "pages.inner.updateAccount.31": "请输入有效邮箱",
    "pages.inner.updateAccount.32": "邮箱尚未注册",
    "pages.inner.updateAccount.33": "两次密码输入不一致，请重新输入",
    "pages.inner.updateAccount.34": "密码需要字母、数字、特殊符号两种以上组合",
    "pages.inner.updateAccount.35": "密码需在8-20位之间",
    "pages.inner.updateAccount.36": "请输入密码",
    "pages.inner.updateAccount.37": "提示"
  },
  "zh-Hant": {
    "pages.inner.updateAccount.0": "帳號升級",
    "pages.inner.updateAccount.1": "尊敬的玩家：",
    "pages.inner.updateAccount.2": "遊戲帳號升級進行中！{span}請使用郵箱驗證碼登入的玩家完成帳號升級。{spanEnd}",
    "pages.inner.updateAccount.3": "{span}當前不支持郵箱驗證碼登入。{spanEnd}為避免影響您的遊戲體驗，建議您盡快完成升級。",
    "pages.inner.updateAccount.4": "請輸入郵箱和驗證碼，並點擊下一步。",
    "pages.inner.updateAccount.5": "請輸入郵箱",
    "pages.inner.updateAccount.6": "請輸入驗證碼",
    "pages.inner.updateAccount.7": "獲取",
    "pages.inner.updateAccount.8": "當前不支持郵箱驗證碼登入。為避免影響您的遊戲體驗，請使用郵箱驗證碼登入的玩家完成帳號升級。",
    "pages.inner.updateAccount.9": "點擊升級後，您需要重新設置郵箱帳號和密碼。",
    "pages.inner.updateAccount.10": "完成升級的帳號可使用郵箱帳號和密碼登入。",
    "pages.inner.updateAccount.11": "最後登入時間：",
    "pages.inner.updateAccount.12": "升級",
    "pages.inner.updateAccount.13": "請輸入新郵箱帳號和密碼，點擊“確認”完成設置。",
    "pages.inner.updateAccount.14": "我們會將升級結果發送至{XX}{span}郵件內包含新郵箱帳號和密碼，請務必妥善保管，避免信息泄露。{spanEnd}",
    "pages.inner.updateAccount.15": "完成升級的帳號可使用郵箱帳號和密碼登入。",
    "pages.inner.updateAccount.16": "密碼為8-20位，字母加數字組合",
    "pages.inner.updateAccount.17": "請再次輸入密碼",
    "pages.inner.updateAccount.18": "確認",
    "pages.inner.updateAccount.19": "恭喜您！帳號升級已完成！",
    "pages.inner.updateAccount.20": "完成升級的帳號可使用郵箱帳號和密碼登入。",
    "pages.inner.updateAccount.21": "關閉頁面",
    "pages.inner.updateAccount.22": "進入遊戲",
    "pages.inner.updateAccount.23": "去登入",
    "pages.inner.updateAccount.24": "下一步",
    "pages.inner.updateAccount.25": "郵箱尚未注冊",
    "pages.inner.updateAccount.26": "郵箱驗證碼僅用於帳號升級流程，不支持用於注冊或登入遊戲帳號。",
    "pages.inner.updateAccount.27": "請返回使用其他方式注冊或登入。",
    "pages.inner.updateAccount.28": "帳號無需升級",
    "pages.inner.updateAccount.29": "您的帳號無需升級，請返回使用郵箱帳號和密碼登入。",
    "pages.inner.updateAccount.30": "已發送",
    "pages.inner.updateAccount.31": "請輸入有效郵箱",
    "pages.inner.updateAccount.32": "郵箱尚未注冊",
    "pages.inner.updateAccount.33": "兩次密碼輸入不一致，請重新輸入",
    "pages.inner.updateAccount.34": "密碼需要字母、數字、特殊符號兩種以上組合",
    "pages.inner.updateAccount.35": "密碼需在8-20位之間",
    "pages.inner.updateAccount.36": "請輸入密碼",
    "pages.inner.updateAccount.37": "提示"
  }
}
</i18n>