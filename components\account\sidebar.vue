<template>
    <div class="account_sidebar">
        <div class="header_wrap">
            <div class="header">
                <div class="header_img" @click="editHeader">
                    <img
                        class="img"
                        :src="userInfo.avatar || defaultHeader"
                        alt="header"
                    />
                </div>
                <div class="ac_icon edit" @click="editHeader"></div>
            </div>
            <div class="header_phoneNum">{{ userInfo.nick_name }}</div>
        </div>
        <div class="sidebar_list_wrap">
            <div class="sidebar_list">
                <div class="item mobile_sidebar" @click="showSidebarMenu()">
                    <div class="item_col">
                        <div class="txt">{{ menuList[$prop.index].txt }}</div>
                        <down-outlined />
                    </div>
                </div>
                <div class="sidebar_list_box" :class="{ show: showMobileMenu }">
                    <div
                        class="item"
                        v-for="(item, index) in menuList"
                        :key="index"
                        :class="{ active: $prop.index === index }"
                        @click="changeMenu(index)"
                    >
                        <div class="item_col">
                            <div class="ac_icon" :class="item.icon"></div>
                            <div class="txt">{{ item.txt }}</div>
                        </div>
                    </div>
                </div>
                <!-- <div class="item">
                <div class="item_col">
                    <div class="ac_icon icon_2"></div>
                    <div class="txt">我的订单</div>
                </div>
            </div>-->
            </div>
        </div>
    </div>

    <!-- 头像选择弹窗 -->
    <a-modal
        v-model:visible="showHeaderChoice"
        class="ac_header_choice_pop"
        :footer="null"
    >
        <!-- 头像选择 -->
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_header_choice">
            <div class="h3">{{ $t('account.avatar.selectAvatar') }}</div>
            <div class="ac_header_choice_content">
                <div class="ac_header_choice_box">
                    <div class="c_loading" v-if="avatarList.length == 0"></div>
                    <div
                        class="ac_header_choice_item"
                        :class="{ active: changeHeaderUrl === item }"
                        @click="changeHeader(item)"
                        v-for="(item, index) in avatarList"
                        :key="index"
                    >
                        <check-circle-filled class="header_choice_icon" />
                        <img class="img" :src="item" alt="header_img" />
                    </div>
                </div>
            </div>
            <div class="pop_btn_line">
                <a-button
                    class="c_small_btn blue"
                    :loading="confirmHeaderLoading"
                    :disabled="confirmHeaderLoading"
                    type="primary"
                    @click="confirmHeader"
                    >{{ $t('account.common.confirm') }}</a-button
                >
                    <div
                    class="c_small_btn empty"
                    @click="showHeaderChoice = false"
                >
                    {{ $t('account.common.cancel') }}
                </div>
            </div>
        </div>
    </a-modal>
</template>
<script lang="ts" setup>
import defaultHeader from "~/assets/images/account/header_default.png";
import { useAuthStore } from "~/stores/auth";
import { getAvatarList, postUpdateUserInfo } from "~/api/login";
import { useAccountStore } from "~/stores/account";
import { getUserInfo } from "~/api/login";

const { t, locale } = useI18n();

// 定义组件属性
const $prop = defineProps({
    index: {
        // 配置数据数据
        type: Number,
        default: 0,
    }
});
const accountStore = useAccountStore();
const userInfo = computed(() => accountStore.userInfo) as ComputedRef<any>;
const authStore = useAuthStore();
// 是否显示选择头像弹窗
const showHeaderChoice = ref<boolean>(false);
// 确认选择头像loading
const confirmHeaderLoading = ref<boolean>(false);
// 菜单
const menuList = ref([
    { icon: "icon_1", txt: t("account.profile.accountInformation"), url: `/${locale.value}/account/profile` },
    { icon: "icon_2", txt: t("account.orders.myOrders"), url: `/${locale.value}/account/mylist` },
    { icon: "icon_4", txt: t("account.security.title"), url: `/${locale.value}/account/security` },
]);
//手机端是否显示下拉菜单
const showMobileMenu = ref(false);
//切换菜单
const changeMenu = (index: number) => {
    // $emit('changeMenu',index);
    navigateTo(menuList.value[index].url);
};
// 编辑头像
const editHeader = async () => {
    if (avatarList.value.length == 0) {
        await getAvatarListFun();
    }
    showHeaderChoice.value = true;
};

// 获取头像选择列表
const avatarList = ref<any>([]);
const changeHeaderUrl = ref<string>("");
const getAvatarListFun = async () => {
    try {
        const res: any = await getAvatarList();
        if (res.code == 200 || res.code == 0) {
            avatarList.value = res.data.list;
        } else {
            console.log(res);
        }
    } catch (error) {
        console.log(error);
    }
};
// 选择头像
const changeHeader = (item: string) => {
    console.log(item);
    changeHeaderUrl.value = item;
};

// 性别
const genderI18n = ["account.personalInfo.male", "account.personalInfo.female", "account.personalInfo.secret", "account.personalInfo.other"];
// 获取用户信息
const getUserInfoFun = async () => {
    try {
        const result: any = await getUserInfo();
        if (result.code === 200 || result.code === 0) {
            if (result.data.gender) {
                result.data.genderName = t(genderI18n[result.data.gender - 1]) || "";
            }
            // securityCheckFun(result.data);
            accountStore.setUserInfo(result.data);
        } else {
            message.error({
                content: result.message,
                class: "c_message_big",
                style: {
                    marginTop: "20vh",
                },
                duration: 3,
                onClose: () => {
                    if (
                        result.code == 500 &&
                        result.message == "record not found"
                    ) {
                        navigateTo("/404");
                    }

                    if(result.code == 401){
                        authStore.logout(()=>{
                            navigateTo(`/${locale.value}/account/login`);
                        });
                    }
                },
            });
            console.error(result.message);
        }
    } catch (error) {}
};

// const $emit = defineEmits(['refreshUserInfo']);
// 确认选择头像
const confirmHeader = async () => {
    if (changeHeaderUrl.value) {
        confirmHeaderLoading.value = true;
        try {
            const res: any = await postUpdateUserInfo({
                avatar: changeHeaderUrl.value,
            });
            confirmHeaderLoading.value = false;
            if (res.code == 200 || res.code == 0) {
                console.log(res);
                showHeaderChoice.value = false;
                getUserInfoFun();
            }
        } catch (error) {
            console.log(error);
        }
    } else {
        message.warning(t("account.avatar.selectAvatar"));
    }
};

// 手机端显示下拉菜单
const showSidebarMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
};

onMounted(async () => {
    getUserInfoFun();
});
defineExpose({
    getUserInfoFun
})
</script>
<style lang="scss">
.ac_header_choice_pop {
    .ant-modal-content {
        background: none;
        .ant-modal-body {
            padding: 0;
        }
    }
}
@media screen and (max-width: 640px) {
    .ac_header_choice_pop {
        padding: 0 10px;
    }
}
</style>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);

.account_sidebar {
    width: 350px;
    position: relative;
    z-index: 9;
    flex-shrink: 0;

    .header {
        position: relative;
        width: 80px;
        height: 80px;
        margin: 0 auto;
        flex-shrink: 0;

        .edit {
            position: absolute;
            bottom: -4px;
            left: 50%;
            margin-left: -40px;
            z-index: 9;
            cursor: pointer;
        }
    }

    .header_img {
        height: 100%;
        width: 100%;
        border: 2px solid #c3c3c3;
        border-radius: 50%;
        overflow: hidden;
        z-index: 1;
        position: relative;
        cursor: pointer;

        .img {
            width: 100%;
            height: 100%;
            vertical-align: top;
        }
    }

    .header_phoneNum {
        color: #111;
        font-size: 20px;
        line-height: 50px;
        margin: 0 auto;
        text-align: center;
        min-height: 20px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
}

.mobile_sidebar {
    display: none;
}
.sidebar_list_wrap {
    position: relative;
    z-index: 9;
}
.sidebar_list {
    width: 100%;

    .item {
        height: 51px;
        line-height: 50px;
        position: relative;
        padding: 0 36px 0 12px;
        cursor: pointer;
        margin-bottom: 1px;
        transition: all 0.3s ease;

        &.active,
        &:hover {
            ::after {
                content: "";
                width: 5px;
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: #a5b9ff;
            }

            border-radius: 6px;
            overflow: hidden;
            background: #758ed0
                linear-gradient(
                    90deg,
                    rgba(31, 71, 178, 0.46) 14.5%,
                    rgba(31, 71, 178, 0) 100%
                );

            .item_col {
                color: #fff;
                border-bottom: none;
            }
        }

        .item_col {
            padding-left: 20px;
            border-bottom: 1px solid #efeff4;
            display: flex;
            font-size: 18px;
            color: #111;
            align-items: center;

            .ac_icon {
                width: 25px;
                height: 25px;
                margin-right: 20px;
            }
        }
    }
}

.ac_header_choice {
    background: #fff;
    width: 100%;
    max-width: 530px;
    .h3 {
        line-height: 50px;
        font-size: 16px;
        font-weight: 500;
        border-bottom: 1px solid #ececf0;
        text-align: center;
    }
    .ac_header_choice_content {
        overflow-y: auto;
        height: 350px;
    }
    .ac_header_choice_box {
        width: 490px;
        margin: 0 auto;
        padding: 20px 0;
        display: flex;
        align-content: flex-start;
        flex-wrap: wrap;
        gap: 10px;
        .ac_header_choice_item {
            width: 90px;
            height: 90px;
            border: 3px solid transparent;
            border-radius: 50%;
            padding: 3px;
            position: relative;
            position: relative;
            cursor: pointer;
            .header_choice_icon {
                display: none;
            }
            &.active {
                border: 3px solid #466df6;
                .header_choice_icon {
                    display: block;
                    position: absolute;
                    bottom: 2px;
                    right: 2px;
                    color: #466df6;
                    font-size: 24px;
                    background: #fff;
                    border-radius: 50%;
                }
            }
            &:hover {
                border: 3px solid #466df6;
            }
            .img {
                width: 100%;
                height: 100%;
                background: #fff;
                overflow: hidden;
                border-radius: 50%;
            }
        }
    }
    .pop_btn_line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px;
        border-top: 1px solid #ececf0;
    }
}

@media screen and (max-width: 1368px) {
}

@media screen and (max-width: 1280px) {
    .account_sidebar {
        width: 250px;
    }

    .sidebar_list {
        width: 100%;

        .item {
            .item_col {
                padding-left: 5px;
                font-size: 14px;

                .ac_icon {
                    margin-right: 5px;
                }
            }
        }
    }
}

@media screen and (max-width: 980px) {
    .account_sidebar {
        width: 100%;
        display: flex;
        flex-direction: column-reverse;
    }
    .sidebar_list_wrap {
        height: 50px;
        margin-bottom: 20px;
    }
    .header_wrap {
        display: flex;
        padding: 10px 20px;
        align-items: center;
        margin-bottom: 20px;
        overflow: hidden;
        border-radius: 16px;
        box-shadow: 0 0 10px #eee;
        border: 1px solid #e8e8e8;
        justify-content: center;
        .header {
            width: 60px;
            height: 60px;
            margin: 0;

            .edit {
                transform: scale(0.8);
            }
        }

        .header_phoneNum {
            margin: 0 0 0 20px;
            font-size: 18px;
        }
    }

    .sidebar_list {
        width: 100%;
        border-radius: 16px;
        margin-bottom: 20px;
        background: #758ed0
            linear-gradient(
                90deg,
                rgba(31, 71, 178, 0.46),
                rgba(31, 71, 178, 0)
            );

        .item {
            padding: 0;
            margin-bottom: 0;
            border-bottom: none;
            border-top: 1px solid #869bd4;

            &.active,
            &:hover {
                ::after {
                    display: none;
                }

                border-radius: 0;
                overflow: hidden;
                background: none;

                .item_col {
                    color: #fff;
                }
            }

            .item_col {
                padding: 0 20px;
                color: #fff;
                border-bottom: none;
                font-size: 16px;

                .ac_icon {
                    display: none;
                }
            }
        }
    }

    .mobile_sidebar {
        display: block;
        border-top: none !important;

        .item_col {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }
    .sidebar_list_box {
        visibility: hidden;
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        &.show {
            visibility: visible;
            opacity: 1;
            max-height: inherit;
        }
    }
}

@media screen and (max-width: 768px) {
    .ac_c_mb20,
    .sidebar_list_wrap,
    .header_wrap {
        margin-bottom: 14px;
    }
}

@media screen and (max-width: 640px) {
    .ac_header_choice {
        .ac_header_choice_box {
            width: 100%;

            .ac_header_choice_item {
                width: 70px;
                height: 70px;
            }
        }
        .pop_btn_line {
            padding:20px 10px;
        }
    }
}
</style>
