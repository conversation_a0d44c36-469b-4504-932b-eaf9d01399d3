@import "reset-css";

html {
    scroll-behavior: smooth;
}

body,
html {
    margin: 0;
    background: #000;
}
body.common_body {
    background: #000;
}

body img {
    vertical-align: inherit;
}

.body_fixed {
    overflow: hidden !important;
    width: 100% !important;
    height: 100% !important;
}

* {
    box-sizing: border-box;
}

:root {
    /* 变量分 全局变量和局部变量 */
    --base_color: #3A58A9;
    --base_blue_color: #6E9BDF;
    --gray_color: #EAEAF8;
}

.flex_center {
    display: flex;
    justify-content: center;
}

.flex_1 {
    flex: 1;
}

.flex_sb {
    display: flex;
    justify-content: space-between;
}

.dn {
    display: none !important;
}

// common  icon
.c_icon_user {
    width: 20px;
    height: 22px;
    background: url(~/assets/images/common/user.png) top center/100% 100% no-repeat;
}

.c_icon_home {
    width: 19px;
    height: 22px;
    background: url(~/assets/images/common/home.png) top center/100% 100% no-repeat;
}

.c_icon_logout {
    width: 20px;
    height: 20px;
    background: url(~/assets/images/common/logout.png) top center/100% 100% no-repeat;
}

.c_icon_order {
    width: 21px;
    height: 25px;
    background: url(~/assets/images/common/order.png) top center/100% 100% no-repeat;
}

.c_logo {
    width: 107px;
    height: 14px;
    background: url(~/assets/images/common/play_best_white.png) top center/100% 100% no-repeat;
}

.c_logo_blue {
    width: 178px;
    height: 24px;
    background: url(~/assets/images/common/play_best_blue.png) top center/100% 100% no-repeat;
}

.c_icon_email {
    width: 20px;
    height: 20px;
    background: url(~/assets/images/common/email.png) top center/100% 100% no-repeat;
}

.c_icon_password {
    width: 20px;
    height: 20px;
    background: url(~/assets/images/common/password.png) top center/100% 100% no-repeat;
}

.c_loading {
    height: 100px;
    width: 100px;
    margin: 40px auto;
    background: url('~/assets/images/loading.png') top center/100% 100% no-repeat;
    animation: rotateInfinite 1s linear infinite;
}

@keyframes rotateInfinite {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(-360deg);
    }
}

.c_btn_a {
    transition: all 0.5s ease;
    cursor: pointer;

    &:hover {
        opacity: 0.9;
        transform: translateY(-2px);
    }
}

.c_btn_blue {
    font-size: 20px;
    background: #3A58A9;
    line-height: 50px;
    text-align: center;
    color: #fff;
    border-radius: 30px;
}

.c_small_btn, button.c_small_btn {
    line-height: 38px;
    height: 40px;
    font-size: 16px;
    min-width: 160px;
    transition: all 0.5s ease;
    padding: 0 20px;
    cursor: pointer;
    border-radius: 20px;
    text-align: center;
    border: 1px solid transparent;

    &.blue {
        background: var(--base_color);
        color: #fff;
    }
    &.empty {
        background:none;
        color: #111;
        border: 1px solid #D9D9D9;
        &:hover {
            background: #eee;
        }
    }

    &.blue:hover {
        background: #5a7adb;
        transform: translateY(-2px)
    }
}

.c_message_big {
    font-size: 18px !important;

    .ant-message-custom-content {
        align-items: center;
        display: flex;
    }
}

// 各种错误提示  一共5个
.c_content_empty {
    max-width: 400px;
    width: 50%;
    margin: 20px auto;
}

.c_status_empty {
    max-width: 400px;
    width: 100%;
    padding-top: 75%;
    height: 0;
    background: url(~/assets/images/common/empty.png) top center/100% 100% no-repeat;
}

.c_status_no_auth {
    max-width: 400px;
    width: 100%;
    padding-top: 75%;
    height: 0;
    background: url(~/assets/images/common/no_auth.png) top center/100% 100% no-repeat;
}

.c_status_404 {
    max-width: 400px;
    width: 100%;
    padding-top: 75%;
    height: 0;
    background: url(~/assets/images/common/404.png) top center/100% 100% no-repeat;
}

.c_status_network_error {
    max-width: 400px;
    width: 100%;
    padding-top: 75%;
    height: 0;
    background: url(~/assets/images/common/network_error.png) top center/100% 100% no-repeat;
}

.c_status_search_nothing {
    max-width: 400px;
    width: 100%;
    padding-top: 75%;
    height: 0;
    background: url(~/assets/images/common/search_nothing.png) top center/100% 100% no-repeat;
}

.banner_pc {
    display: block;
}

.banner_mobile {
    display: none;
}

.ant-modal-close {
    z-index: 9999!important;   
}

.c_link {
    color: var(--base_color);
    cursor: pointer;
}

@media screen and (max-width: 768px) {

    .banner_pc {
        display: none;
    }

    .banner_mobile {
        display: block;
    }
}



/* 自定义 NProgress 样式 */
#nprogress {
    pointer-events: none;
}

#nprogress .bar {
    background: linear-gradient(90deg, rgb(229, 255, 114), rgb(0, 173, 130), rgb(0, 217, 255), rgb(0, 46, 130), rgb(137, 107, 243), rgb(255, 94, 255));
    /* 修改进度条颜色 */
    overflow: hidden;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-size: 400% 100%;
    height: 8px;
    /* 修改进度条高度 */
    animation: rainbow 4s infinite;
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;
    width: 100%;
}

#nprogress .peg {
    width: 80px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.01), rgba(255, 255, 255, 0.5), #ffffff);
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    /* 修改进度条阴影 */
}

#nprogress .spinner {
    display: block;
    position: fixed;
    z-index: 1031;
    top: 15px;
    right: 15px;
}

#nprogress .spinner-icon {
    width: 24px;
    height: 24px;
    box-sizing: border-box;

    border: solid 2px transparent;
    border-top-color: #29d;
    border-left-color: #29d;
    border-radius: 50%;

    animation: nprogress-spinner 400ms linear infinite;
}

@keyframes rainbow {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes nprogress-spinner {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}



.page-enter-active,
.page-leave-active {
    transition: all 0.4s;
}

.page-enter-from,
.page-leave-to {
    opacity: 0;
    filter: blur(1rem);
}

.ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(6px);
}

.currency_select_drawer {
    .ant-drawer-content {
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;

        .ant-drawer-header {
            .ant-drawer-header-title {
                text-align: center;
                flex-direction: row-reverse;

                .ant-drawer-title {
                    font-size: 18px;
                }

                .ant-drawer-close {
                    position: absolute;
                    right: 0;
                }
            }
        }
        
        .ant-drawer-body {
            padding: 0 20px;
            
            .ant-radio-group {
                display: flex;
                flex-direction: column;

                .ant-radio-wrapper {
                    flex-direction: row-reverse;
                    justify-content: space-between;
                    font-size: 18px;
                    padding: 20px 0;
                    border-bottom: 1px solid #ECECF0;

                    &:last-child {
                        border-bottom: none;
                    }

                    span:last-child {
                        flex: 1;
                    }
                }
            }
        }

        .ant-drawer-footer {
            .order_f_payBtn {
                height: 50px;
                width: 100%;
                flex-shrink: 0;
                border-radius: 30px;
                background: #3A58A9;
                font-size: 20px;
                text-align: center;
                color: #fff;
                line-height: 46px;
                cursor: pointer;
                padding: 0;
            }
        }
    }
}