export default {
    "account.login.accountLogin": "账号登录",
    "account.login.emailLogin": "邮箱登录",
    "account.login.pleaseEnterEmail": "请输入邮箱",
    "account.login.pleaseEnterPassword": "请输入密码",
    "account.login.forgotPassword": "忘记密码",
    "account.login.registerNow": "立即注册",
    "account.login.cancelLoginAuthorization": "取消登录授权",
    "account.login.confirm": "确认",
    "account.login.return": "返回",
    "account.login.onlyOfficialGameEmail": "本网站当前仅支持使用官方游戏邮箱账号进行登录。",
    "account.login.notSupportEmailVerificationCode": "暂不支持使用邮箱验证码登录",
    "account.login.useEmailVerificationCodeOrThirdParty": "使用邮箱验证码或其他三方登录账号（如Google、Facebook、苹果等）请先前往游戏内进行邮箱账号绑定。",
    "account.login.pleaseEnterVerificationCode": "请输入验证码",
    "account.login.get": "获取",
    "account.login.sent": "已发送",
    "account.register.accountRegistration": "账号注册",
    "account.register.enterEmail": "输入邮箱",
    "account.register.passwordRequirements": "密码为8-20位，字母加数字组合",
    "account.register.confirmPassword": "请再次输入密码",
    "account.register.register": "注册",
    "account.register.alreadyHaveAccount": "已有账号去登录",
    "account.register.agreeTerms": "我已阅读并同意游戏服务合约和隐私保护政策",
    "account.register.ageConfirmation": "我已年满14周岁",
    "account.register.termsAndAgeConfirmation": "为保障您的权益，请您确认已阅读并同意游戏服务合约和隐私保护政策，且已年满14周岁。",
    "account.register.termsConfirmation": "为保障您的权益，请您确认已阅读并同意游戏服务合约和隐私保护政策。",
    "account.common.confirm": "确认",
    "account.common.return": "返回",
    "account.forgotPassword.title": "忘记密码",
    "account.forgotPassword.enterEmail": "输入邮箱",
    "account.forgotPassword.nextStep": "下一步",
    "account.forgotPassword.noSecurityVerification": "账号未进行安全验证",
    "account.forgotPassword.contactSupport": "找回密码需验证邮箱或手机号码，如您未进行验证，",
    "account.forgotPassword.contactSupport2":"请“联系客服”",
    "account.forgotPassword.cannotModify": "找回密码需验证邮箱或手机号码，如您未进行验证，则暂无法修改",
    "account.securityVerification.title": "安全验证",
    "account.securityVerification.methods": "您可以使用以下方式进行验证",
    "account.securityVerification.phoneVerification": "手机号码验证",
    "account.securityVerification.emailVerification": "邮箱验证",
    "account.securityVerification.phoneInstructions2": "点击“获取”验证码，验证短信将发送至您的手机号码 { XX }",
    "account.securityVerification.emailInstructions2": "点击“获取”验证码，验证码将发送至您的邮箱 { XX }",
    "account.securityVerification.enterVerificationCode": "输入验证码",
    "account.resetPassword.setNewPassword": "设置新密码",
    "account.resetPassword.success": "修改密码成功，请重新登录",
    "account.profile.accountInformation": "账号信息",
    "account.profile.emailVerificationAvailable": "现可以进行邮箱验证",
    "account.profile.emailVerificationImportance": "我们十分重视您的账号安全。请进行邮箱验证，以确保您的账号资产不会收到侵害",
    "account.profile.skipVerification": "暂不操作",
    "account.profile.startEmailVerification": "开始验证邮箱",
    "account.profile.nickname": "昵称",
    "account.profile.accountName": "账号名",
    "account.profile.accountID": "账号ID",
    "account.profile.verifyEmail": "验证邮箱",
    "account.profile.phoneNumber": "手机号码",
    "account.profile.notBound": "未绑定",
    "account.profile.notVerified": "未验证",
    "account.profile.notSet": "未设置",
    "account.profile.verify": "去验证",
    "account.profile.bind": "去绑定",
    "account.profile.bound": "已绑定",
    "account.profile.changeBind": "换绑",
    "account.profile.edit": "编辑",
    "account.securityCheck.title": "安全检查",
    "account.securityCheck.bindAccountName": "绑定账号名",
    "account.securityCheck.accountNameBound": "账号名已绑定",
    "account.securityCheck.verifyEmail": "验证电子邮箱",
    "account.securityCheck.emailVerified": "电子邮箱已验证",
    "account.securityCheck.fillPersonalInfo": "填写个人信息",
    "account.securityCheck.personalInfoFilled": "个人信息已填写",
    "account.securityCheck.bindPhoneNumber": "绑定手机号码",
    "account.securityCheck.phoneNumberBound": "手机号码已绑定",
    "account.securityCheck.completionProgress": "完成进度",
    "account.personalInfo.title": "个人信息",
    "account.personalInfo.name": "姓名",
    "account.personalInfo.birthday": "生日",
    "account.personalInfo.gender": "性别",
    "account.personalInfo.notSet": "未设置",
    "account.personalInfo.edit": "编辑",
    "account.personalInfo.modify": "修改",
    "account.editNickname.title": "编辑您的昵称",
    "account.editNickname.changeLimit": "设置您的昵称后，您在180天内不可再次进行变更。",
    "account.editNickname.currentNickname": "当前昵称",
    "account.editNickname.notSet": "未设置",
    "account.editNickname.enterNewNickname": "请输入新昵称",
    "account.editNickname.nicknameLength": "昵称长度至少3个字符",
    "account.editNickname.privacyWarning": "为保护您的隐私，请勿使用您的真实姓名、地址、电话号码或其他个人信息",
    "account.common.confirm": "确认",
    "account.common.cancel": "取消",
    "account.setEmailAccount.title": "设置邮箱账号",
    "account.setEmailAccount.description": "验证邮箱将作为账号名使用；设置成功后可使用邮箱登录",
    "account.setEmailAccount.agreement": "点击“确认”按钮即表示同意Playbest保存该邮箱号码，并允许在发送通知和进行安全验证时使用。",
    "account.setEmailAccount.enterEmail": "输入邮箱",
    "account.setEmailAccount.enterVerificationCode": "输入验证码",
    "account.common.get": "获取",
    "account.common.sent": "已发送",
    "account.setEmailAccount.passwordRequirements": "密码为8-20位，字母加数字组合",
    "account.setEmailAccount.confirmPassword": "请再次输入密码",
    "account.common.confirm": "确认",
    "account.verifyEmail.title": "验证邮箱",
    "account.verifyEmail.agreement": "点击“验证”按钮即表示同意Playbest保存该邮箱号码，并允许在发送通知和进行安全验证时使用",
    "account.verifyEmail.enterEmail": "输入邮箱",
    "account.verifyEmail.enterVerificationCode": "输入验证码",
    "account.common.get": "获取",
    "account.common.sent": "已发送",
    "account.verifyEmail.verify": "验证",
    "account.bindPhoneNumber.title": "绑定手机号码",
    "account.bindPhoneNumber.description": "为了确保账号的安全性，请绑定手机号码",
    "account.bindPhoneNumber.agreement": "点击“绑定”按钮即表示同意Playbest保存该手机号码，并允许在发送通知和进行安全验证时使用。",
    "account.bindPhoneNumber.select": "选择",
    "account.bindPhoneNumber.enterPhoneNumber": "输入手机号码",
    "account.bindPhoneNumber.enterVerificationCode": "输入验证码",
    "account.common.get": "获取",
    "account.common.sent": "已发送",
    "account.bindPhoneNumber.bind": "绑定",
    "account.changeEmail.title": "换绑验证邮箱",
    "account.changeEmail.currentEmail": "当前验证邮箱",
    "account.changeEmail.description": "换绑邮箱后，接受通知与验证信息都将发送至新的邮箱",
    "account.securityVerification.title": "安全验证",
    "account.securityVerification.methods": "您可以使用以下方式进行验证",
    "account.securityVerification.phoneVerification": "手机号码验证",
    "account.securityVerification.emailVerification": "邮箱验证",
    "account.securityVerification.emailInstructions": "点击“获取”验证码，验证码将发送至您的邮箱",
    "account.securityVerification.enterVerificationCode": "输入验证码",
    "account.common.get": "获取",
    "account.common.sent": "已发送",
    "account.common.nextStep": "下一步",
    "account.changeEmail.agreement": "点击“验证”按钮即表示同意Playbest保存该邮箱号码，并允许在发送通知和进行安全验证时使用",
    "account.changeEmail.enterEmail": "输入邮箱",
    "account.changePhoneNumber.title": "换绑手机号",
    "account.changePhoneNumber.currentPhoneNumber": "当前手机号",
    "account.changePhoneNumber.description": "换绑手机号后，接受通知与验证信息都将发送至新的手机号码",
    "account.securityVerification.methods": "您可以使用以下方式进行验证",
    "account.securityVerification.phoneVerification": "手机号码验证",
    "account.securityVerification.phoneInstructions": "点击“获取”验证码，验证短信将发送至您的手机号码",
    "account.securityVerification.enterVerificationCode": "输入验证码",
    "account.common.get": "获取",
    "account.common.sent": "已发送",
    "account.common.nextStep": "下一步",
    "account.changePhoneNumber.agreement": "点击“绑定”按钮即表示同意Playbest保存该手机号码，并允许在发送通知和进行安全验证时使用。",
    "account.resetPassword.setNewPassword": "设置新密码",
    "account.resetPassword.enterOriginalPassword": "请输入原密码",
    "account.resetPassword.verifyOriginalPassword": "验证原密码",
    "account.resetPassword.setNewPassword": "请设置新密码",
    "account.personalInfo.enterFirstName": "请输入名",
    "account.personalInfo.enterLastName": "请输入姓",
    "account.personalInfo.selectGender": "请选择性别",
    "account.personalInfo.gender": "性别",
    "account.personalInfo.selectBirthday": "请选择生日",
    "account.personalInfo.birthdayWarning": "生日信息提交后仅可修改一次，请谨慎填写。",
    "account.personalInfo.male": "男",
    "account.personalInfo.female": "女",
    "account.personalInfo.secret": "保密",
    "account.personalInfo.other": "其他",
    "account.common.save": "保存",
    "account.avatar.selectAvatar": "选择一个头像",
    "account.avatar.pleaseSelectAvatar": "请选择头像",
    "account.security.title": "安全管理",
    "account.security.passwordManagement": "密码管理",
    "account.security.passwordUpdateRecommendation": "建议定期更新密码以避免他人未经授权登录你的PLAYBEST账号",
    "account.security.lastPasswordUpdateTime": "上次更新密码时间",
    "account.security.changePassword": "修改密码",
    "account.common.modify": "修改",
    "account.common.set": "设置",
    "account.orders.myOrders": "我的订单",
    "account.orders.all": "全部",
    "account.orders.pendingPayment": "待付款",
    "account.orders.pendingShipment": "待发货",
    "account.orders.completed": "已完成",
    "account.orders.closed": "已关闭",
    "account.orders.allGames": "全部游戏",
    "account.orders.orderNumber": "订单编号",
    "account.orders.orderTime": "下单时间",
    "account.orders.cancelOrder": "取消订单",   
    "account.orders.paymentMethod": "支付方式",
    "account.orders.noOrders": "你还没有任何订单",
    // 注册相关
    "account.register.emailEmpty": "请填写邮箱",
    "account.register.passwordEmpty": "请填写密码",
    "account.register.confirmPasswordEmpty": "密码确认不可为空",
    "account.register.emailInvalid": "请输入有效邮箱",
    "account.register.emailAlreadyRegistered": "邮箱已被注册",
    "account.register.passwordLengthInvalid": "密码需在8-20位之间",
    "account.register.passwordTooSimple": "密码需要字母、数字、特殊符号两种以上组合",
    "account.register.passwordInvalidChars": "密码中含有不支持的特殊符号",
    "account.register.passwordMismatch": "新密码输入不一致，请重新输入",

    // 登录相关
    "account.login.emailEmpty": "请填写邮箱",
    "account.login.passwordEmpty": "请填写密码",
    "account.login.emailInvalid": "请输入有效邮箱",
    "account.login.emailNotRegistered": "玩家不存在",
    "account.login.credentialsInvalid": "邮箱或密码错误，请重新输入",
    "account.login.tooManyAttempts": "密码输入错误次数过多，请稍后再试",

    // 验证码相关
    "account.verification.emailEmpty": "请填写邮箱",
    "account.verification.passwordEmpty": "请填写密码",
    "account.verification.emailInvalid": "请输入有效邮箱",
    "account.verification.codeSent": "验证码已发送",
    "account.verification.noSecurityEmail": "此账号未进行安全邮箱验证",
    "account.verification.emailNotFound": "玩家不存在",
    "account.verification.codeInvalid": "请填写正确的验证码",
    "account.verification.codeReused": "验证码过期，请重试",

    // 邮箱绑定相关
    "account.email.alreadyBound": "邮箱已被注册",
    "account.email.bindSuccess": "账号名绑定成功",
    "account.email.accountBindSuccess": "您已成功设置账号名并验证邮箱",
    "account.email.empty": "请填写邮箱",
    "account.email.passwordEmpty": "请填写密码",
    "account.email.invalid": "请输入有效邮箱",
    "account.email.codeEmpty": "请填写验证码",
    "account.email.codeSent": "验证码已发送",
    "account.email.verificationSuccess": "邮箱验证成功",

    // 通用错误
    "account.error.gameLimitExceeded": "邮箱关联账号数已达上限",
    "account.error.tooManyRequests": "验证码获取频繁，请稍后再试",
    "account.error.dailyLimitExceeded": "今日验证码获取次数已达上限",
    "account.error.codeExpired": "验证码过期，请重试",

    // 手机绑定相关
    "account.phone.empty": "请填写手机号码",
    "account.phone.invalid": "请输入有效手机号",
    "account.phone.bindSuccess": "手机号码绑定成功",
    "account.phone.changeBindSuccess": "手机号码换绑成功",
    "account.phone.areaCodeEmpty": "请填写区号",
    "account.phone.areaCode": "区号",

    // 密码相关
    "account.password.originalEmpty": "请输入原密码",
    "account.password.newEmpty": "请填写新密码",
    "account.password.originalIncorrect": "原密码输入错误，请重新输入",
    "account.password.sameAsCurrent": "新密码与当前密码相同",
    "account.password.mismatch": "新密码输入不一致，请重新输入",
    "account.password.changeSuccess": "修改密码成功，请重新登录",

    // 忘记密码
    "account.forgotPassword.noSecurityContact": "账号未进行安全验证",

    // 昵称相关
    "account.nickname.empty": "请填写您的新昵称",
    "account.nickname.tooShort": "昵称长度至少3个字符",
    "account.nickname.tooLong": "昵称过长，请重新输入",
    "account.nickname.sameAsCurrent": "您输入的昵称与当前昵称相同",
    "account.nickname.containsSensitiveWords": "昵称含有敏感词，请重新输入",
    "account.nickname.editLimitReached": "昵称修改需间隔180天，请耐心等待",
    "account.nickname.setSuccess": "昵称设置成功",

    // 其他错误
    "account.error.network": "网络似乎不太稳定，请稍后重试",
    "account.error.loginExpired": "登录信息已失效，请重新登录",

    // 注销账号
    ...{
        "account.security.deregister.s10.t1": "账号注销中",
        "account.security.deregister.s8.t1": "账号注销申请被驳回",
        "account.security.deregister.s9.t1": "账号注销申请审核中",
        "account.security.deregister": "注销账号",
        "account.security.deregister.apply": "申请注销",
        "account.security.deregister.desc": "发起账号注销申请后，将在30天内完成注销。",
        "account.security.deregister.popup.title": "注销您的Playbest账号",
        "pages.security.deregister.8": "注销您的{gamename}游戏账号",
        "account.security.deregister.s1.title": "请注意！您正在申请注销您的Playbest账号！",
        "account.security.deregister.s1.p1": "注销账号：{XXX}",
        "account.security.deregister.s1.p2": "账号一经注销完成即永久删除，且无法逆转。",
        "account.security.deregister.s1.p3": "所有账号内的游戏、虚拟货币余额、未使用完毕的服务、个人资料和历史记录也将被一并永久删除。",
        "account.security.deregister.s1.f1": "如您确认注销Playbest账号，请按本流程指引完成注销操作。",
        "account.security.deregister.nextStep": "下一步",
        "account.security.deregister.s3.btn1": "下一步({XXX}s)",
        "account.security.deregister.s3.t1": "阅读游戏账号注销条件，并在页面底部勾选确认",
        "account.security.deregister.s4.t1": "阅读游戏账号《注销须知》，并在页面底部勾选确认",
        "account.security.deregister.s3.check": "我已详细阅读并满足账号注销条件",
        "account.security.deregister.s4.check": "我已详细阅读并同意《注销须知》",
        "account.security.deregister.s3.p1": "亲爱的玩家，我们对您的注销深表遗憾。为保证您的账号和财产安全，在您提交的注销申请生效前，请您确保已满足以下条件，我们的游戏团队也有权视情况进行验证:",
        "account.security.deregister.s3.p2": "<b>1、账号处于安全状态:</b> 账号正常使用中，未处于违规处理(指账号因违反该游戏相关协议、规则被采取封号及其他处理措施)期间，且没有被盗、被封等风险\n<b>2、游戏收益已结清或得到妥善处理:</b>包括但不限于游戏代币、其他游戏虚拟道具以及其他游戏增值服务(为描述方便，统称“游戏收益”)。请您妥善处理，若未处理，视为您自愿放弃该等游戏收益\n<b>3、账号无未完成状态订单:</b> 包括充值游戏代币，购买其他游戏虚拟道具及其他周边商品等\n<b>4、账号无任何纠纷:</b> 包括投诉举报、被投诉举报、仲裁、诉讼等纠纷\n<b>5、账号无任何风险:</b>  该账号在最近1个月内无异常登录记录，无其他安全风险。\n<b>6、账号无任何交易:</b>  该账号内无任何未完成的订单或交易，包括但不限于平台周边商城中未完成的订单。\n<b>7、账号无任何合同:</b> 不存在任何由于该账号被注销而导致的未了结的合同关系与其他基于该账号的存在而产生的或维持的权利义务以及本平台认为注销该会由此产生未了结的权利义务而产生纠纷的情况。\n<b>8、其他应满足的条件</b>\n\n*温馨提示: 注销账号冷静期为30天，在审核期间您的账号进入冻结期。如果您希望撤销注销申请，请在账号冻结期间登录游戏解除冻结或与我们取得联系。您的账号将于冻结期结束后彻底删除。\n由于用户在游戏中可在一个或多个大区创建角色，如您在游戏中多个大区均创建了游戏角色，游戏账号注销将会对本账号下所有大区及角色进行注销。",
        "account.security.deregister.s4.p1": "1.在注销前，您须保证您的Playbest账号同时满足下列条件。如果您未能满足本条中任何一项条件，Playbest有权拒绝您的注销账号申请。\na)申请注销时，您的Playbest账号应当可以正常使用，且不存在任何可能产生账号安全风险的情况（包括但不限于通行证账号被封禁或冻结、账号异常登录行为、异常使用行为等）。\nb)不存在任何权属争议，且不存在任何非法使用他人账号、与第三人共用账号及/或其他可能产生账号使用权纠纷或损害第三方合法权益的情形。\n2.在您已阅读并同意注销须知，且通过账号安全验证及个人信息验证后，您的账号将正式进入账号注销冻结期（下亦称“冻结期”），冻结期为【30】天。若您于冻结期内使用该账号登录或使用任何Playbest服务，则可通过点击登录页面的“激活”按钮重新激活账号（即用户点击“激活”按钮后，该账号的注销流程将立刻终止，该账号将重新恢复正常使用）。若冻结期内您未曾使用该账号登录或通过该账号使用任何Playbest服务，该账号将于冻结期结束后完成注销。\n3.Playbest账号注销的后果\na)您的Playbest账号注销后，您将无法再通过该账号登录并使用任何Playbest服务，亦无法通过登录通行证账号找回您于该账号内设置的任何内容和信息，即便您使用相同的注册信息再次注册通行证账号并使用Playbest服务。\nb)您曾通过该账号登录、使用Playbest服务所产生的所有内容、信息、数据、角色、记录将会被删除或匿名化处理（法律法规另有规定除外）。您将无法再访问、传输、获取、继续使用和找回前述被删除或匿名化处理的内容、信息、数据、记录，同时亦无权要求Playbest找回。\nc)您在使用Playbest账号过程已获得的充值余额及因充值而获取的相关虚拟道具、历史对局记录、各类优惠券、VIP会员卡、月卡、周卡、未过期的道具、未完成的任务、该账号内其他已产生但尚未消耗的权益及/或预期可能产生的权益等均视为您自行放弃，将无法继续使用且无法恢复。\nd)一旦账号注销完成，您注册并使用该账号及相关Playbest服务时与我们曾签署过的所有协议将相应终止，但相关协议中已与您约定继续有效的部分或法律法规另有规定除外。\ne)注销Playbest账号并不代表账号注销前已发生的账号行为和已产生的相关责任得到豁免或减轻.\n4.在Playbest账号注销完成后，您的账号所涉及的个人信息和数据将被删除或匿名化处理。您知晓并同意在适用法律和法规允许的最大范围内，在某些情况下，我们可能无法删除您的个人资料。这些情况包括：\na)为了保护本公司之业务、系统和用户免受诈欺活动影响；\nb)应对损害现有功能的技术问题；\nc)为其他用户行使权利所必须；\nd)根据合法程序遵守执法要求；\ne)用于科学或历史研究；\nf)出于与您与本公司关系合理相关的本公司内部目的，或为了履行法律上之义务。",
        "pages.security.deregister.23": "1. 注销您的{}游戏账号不会影响您正常使用您的Playbest账号，您仍可以通过该Playbest账号登录Playbest的其他游戏或Playbest官网。\n2. 在注销前，您须保证您的游戏账号同时满足下列条件。如果您未能满足本条中任何一项条件，Playbest有权拒绝您的注销账号申请。\na) 申请注销时，您的账号应当可以正常使用，且不存在任何可能产生账号安全风险的情况（包括但不限于通行证账号被封禁或冻结、账号异常登录行为、异常使用行为等）。\nb) 不存在任何权属争议，且不存在任何非法使用他人账号、与第三人共用账号及/或其他可能产生账号使用权纠纷或损害第三方合法权益的情形。\n3. 在您已阅读并同意注销须知，且通过账号安全验证及个人信息验证后，您的账号将正式进入账号注销冻结期（下亦称“冻结期”），冻结期为【30】天。若您于冻结期内使用该账号登录或使用任何该游戏所提供的服务，则可通过点击登录页面的“激活”按钮重新激活账号（即用户点击“激活”按钮后，该账号的注销流程将立刻终止，该账号将重新恢复正常使用）。若冻结期内您未曾使用该账号登录或通过该账号使用任何该游戏所提供的服务，该账号将于冻结期结束后完成注销。\n4. 游戏账号注销的后果\na) 您的游戏账号注销后，您将无法再通过该账号登录并使用任何该游戏提供的服务，亦无法通过登录该账号找回您于该账号内设置的任何内容和信息，即便您使用相同的注册信息再次注册{}账号。\nb) 您曾通过该账号登录、使用{}服务所产生的所有内容、信息、数据、记录将会被删除或匿名化处理（法律法规另有规定除外）。您将无法再访问、传输、获取、继续使用和找回前述被删除或匿名化处理的内容、信息、数据、记录，同时亦无权要求Playbest找回。\nc) 您在使用{gamename}账号过程已获得的充值余额及因充值而获取的相关虚拟道具、历史对局记录、各类优惠券、VIP会员卡、月卡、周卡、未过期的道具、未完成的任务、该账号内其他已产生但尚未消耗的权益及/或预期可能产生的权益等均视为您自行放弃，将无法继续使用且无法恢复。\nd) 一旦账号注销完成，您注册并使用该账号及相关该游戏所提供的服务时与我们曾签署过的所有协议将相应终止，但相关协议中已与您约定继续有效的部分或法律法规另有规定除外。\ne) 注销该游戏账号并不代表账号注销前已发生的账号行为和已产生的相关责任得到豁免或减轻.\n5. 在{}账号注销完成后，您的账号所涉及的个人信息和数据将被删除或匿名化处理。您知晓并同意在适用法律和法规允许的最大范围内，在某些情况下，我们可能无法删除您的个人资料。这些情况包括：\n(a) 为了保护本公司之业务、系统和用户免受诈欺活动影响；\n(b) 应对损害现有功能的技术问题；\n(c) 为其他用户行使权利所必须；\n(d) 根据合法程序遵守执法要求；\n(e) 用于科学或历史研究；\n(f) 出于与您与本公司关系合理相关的本公司内部目的，或为了履行法律上之义务。",
        "pages.security.deregister.24": "下一步",
        "pages.security.deregister.25": "我不注销了",
        "account.security.deregister.s5.t1": "选择注销原因",
        "pages.security.deregister.27": "注销账号",
        "account.security.deregister.s5.t3": "多选，最多选三项",
        "account.security.deregister.s5.p1": "游戏不好玩",
        "account.security.deregister.s5.p2": "游戏权益不够",
        "account.security.deregister.s5.p3": "充值问题",
        "account.security.deregister.s5.p4": "不想玩游戏了",
        "account.security.deregister.s5.p5": "这个账号不使用了",
        "account.security.deregister.s5.p6": "其他",
        "account.security.deregister.s5.p7": "其他注销原因，最大长度为30个字符，超过时不可再填写",
        "account.security.deregister.s5.b1": "提交",
        "account.security.deregister.s6.t1": "确定要注销账号吗?",
        "account.security.deregister.s6.p1": "亲爱的玩家，感谢您对我们的支持。",
        "account.security.deregister.s6.p2": "注销流程完成后，您的帐号将会被删除，请谨慎操作。",
        "account.security.deregister.s6.p3": "请知悉，完成全部注销流程后，您的账号数据，以及通过该账号登录产品后产生的使用数据将会被永久删除且无法恢复，请谨慎选择并操作。",
        "account.security.deregister.s6.b1": "确认注销",
        "account.security.deregister.s6.b2": "我再想想",
        "account.security.deregister.s7.t1": "您的账号注销申请提交成功",
        "account.security.deregister.s7.t1-1": "感谢您对我们游戏的支持",
        "pages.security.deregister.45": "注销账号：",
        "account.security.deregister.s7.p2": "申请时间：{XXX}",
        "account.security.deregister.s7.p3": "除法律法规规定和注销协议约定情形外，将在您提交申请之日起的30个工作日内完成注销审核。",
        "account.security.deregister.s7.d2": "在审核期间您的账号进入冻结期。如果您希望撤销注销申请，请在账号冻结期间登录游戏解除冻结或与我们取得联系。您的账号将于冻结期结束后彻底删除。",
        "account.security.deregister.s7.d3": "点击“我知道了”按钮退出当前账号",
        "account.security.deregister.s7.b1": "我知道了",
        "account.security.deregister.s3.btn2": "放弃注销",
        "pages.security.deregister.52": "我不注销了",
        "pages.security.deregister.53": "账号注销中",
        "account.security.deregister.s5.t2": "注销账号：{XXX}",
        "pages.security.deregister.55": "申请时间：",
        "pages.security.deregister.56": "您的账号已提交注销申请",
        "account.security.deregister.s10.t2": "将于提交申请之日起的30日内完成注销",
        "pages.security.deregister.58": "除法律法规规定和注销协议约定情形外，将在您提交申请之日起的30个工作日内完成注销审核。",
        "account.security.deregister.s8.p4": "如您想放弃注销流程，请点击“放弃注销”；如确定注销此账号，点击“更换账号”后可通过其他账号进行登录",
        "pages.security.deregister.60": "放弃注销",
        "account.security.deregister.s8.b1": "更换账号",
        "pages.security.deregister.62": "您的账号注销申请被驳回",
        "pages.security.deregister.63": "注销账号：",
        "pages.security.deregister.64": "申请时间：",
        "account.security.deregister.s8.t2": "亲爱的玩家，经审核您的账号存在以下风险，暂不符合注销条件",
        "pages.security.deregister.68": "放弃注销",
        "pages.security.deregister.69": "更换账号",
        "pages.security.deregister.70": "您的账号注销申请正在审核中",
        "pages.security.deregister.71": "注销账号：",
        "pages.security.deregister.72": "申请时间：",
        "pages.security.deregister.73": "账号注销申请审核中",
        "account.security.deregister.s9.p3": "亲爱的玩家，感谢您的耐心等待。我们已收到您的账号注销申请。由于系统检测到您的账号存在异常情况，我们已将您的申请转入人工审核流程。",
        "account.security.deregister.s9.p4": "人工审核过程可能需要一定时间，请您耐心等待。感谢您的理解与支持。",
        "pages.security.deregister.77": "放弃注销",
        "pages.security.deregister.78": "更换账号",
        "account.security.deregister.msg2": "您已取消注销账号",
        "account.security.deregister.msg1": "请至少选择一项注销原因",
        "pages.security.deregister.81": "每天只能注销一次账号，请明日再进行账号注销操作",
        "pages.security.deregister.82": "账号注销中，暂无法支付",
        "pages.security.deregister.83": "如有问题请联系客服",
        "pages.security.deregister.84": "账号注销中。如有问题请联系客服或登录账号中心处理。",
        "pages.security.deregister.85": "账号注销申请被驳回。如有问题请联系客服或登录账号中心处理。",
        "pages.security.deregister.86": "账号注销申请审核中。如有问题请联系客服或登录账号中心处理。",
        "pages.security.deregister.s7.app": "关闭页面将为您退出当前账号"
    },
    
    // web-view 内嵌账号中心
    "account.inner.lastName": "姓",
    "account.inner.firstName": "名",
    "account.inner.goToSetting": "去设置",
    "account.inner.nicknameNotSet": "昵称未设置",
    "account.inner.bind": "绑定",
    "account.inner.verify": "验证",
    "account.inner.changeBinding": "更改绑定",
    "account.inner.bound": "已绑定",
    "account.inner.passwordManagement": "密码管理",
    "account.inner.lastUpdate": "上次更新",
    "account.inner.update": "更新",
    "account.inner.switchAccount": "切换账号",
    "account.inner.deleteAccount": "注销账号",
    "account.inner.unbind": "解绑",
    "account.inner.unbindConfirm": "是否解除{media_name}绑定？",
    "account.inner.unbindConfirmDesc": "解除绑定后将无法使用{media_name}账号{nickname}作为第三方登录方式登录该游戏账号",
    "account.inner.hint":"提示",
    "account.inner.confirmSwitchAccount": "确定切换账号吗？",
    "account.inner.step.verify": "验证",
    "account.inner.step.finished": "完成！",
    "account.inner.step.verifySuccess": "验证成功",
    "account.inner.step.ok":"好的",
    "account.inner.bindAccount.desc": "设置完成后，可使用邮箱账号和密码登录。",
    "account.inner.unbindAccount.onlyOne": "为保证可正常登录游戏，请至少保留一种第三方登录方式。或请前往绑定账号名后，再执行解绑操作。",
    "account.inner.unbindAccount.unbindSuccess": "解绑成功",
    "account.inner.setAccountName.title": "设置账号名",
    "account.inner.setAccountName.success": "设置成功",
    "account.inner.setAccountName.desc": "已为您成功设置账号名",
    "account.inner.emailVerificationSuccess": "邮箱验证成功",
    "account.inner.emailVerificationSuccessDesc": "已为您成功验证邮箱\n通知与验证信息将发送至该邮箱",
    "account.inner.changeEmailSuccess": "换绑验证邮箱成功",
    "account.inner.changeEmailSuccessDesc": "已为您成功换绑邮箱\n通知与验证信息将发送至新邮箱",
    "account.inner.phoneBindSuccess": "手机号码绑定成功",
    "account.inner.phoneBindSuccessDesc": "已为您成功绑定手机号码\n通知与验证信息将发送至该手机号",
    "account.inner.changePhoneSuccess": "换绑验证手机号码成功",
    "account.inner.changePhoneSuccessDesc": "已为您成功换绑手机号码\n通知与验证信息将发送至新手机号码",
    "account.inner.changePasswordSuccess": "修改密码成功",
    "account.inner.changePasswordSuccessDesc": "请重新登录账号",
    "account.inner.bindSuggestion": "尊敬的游客，为保障您的账号安全与使用便利，建议您尽快绑定邮箱账号名或社交媒体账号。未绑定账号可能会导致部分功能受限。",
    "account.inner.bindSuccess": "绑定成功", 
    "account.profile.editProfile": "编辑资料",
}
