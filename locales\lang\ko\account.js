export default {
    "account.login.accountLogin": "계정 로그인",
    "account.login.emailLogin": "이메일 로그인",
    "account.login.pleaseEnterEmail": "이메일을 입력하세요",
    "account.login.pleaseEnterPassword": "비밀번호를 입력하세요",
    "account.login.forgotPassword": "비밀번호 찾기",
    "account.login.registerNow": "신규 가입",
    "account.login.cancelLoginAuthorization": "로그인 권한 취소",
    "account.login.confirm": "확인",
    "account.login.return": "돌아가기",
    "account.login.onlyOfficialGameEmail": "현재 공식 게임 메일 계정을 통한 로그인만 지원하고 있습니다.",
    "account.login.notSupportEmailVerificationCode": "메일 인증번호를 통한 로그인은 현재 지원되지 않습니다",
    "account.login.useEmailVerificationCodeOrThirdParty": "메일 인증 또는 기타 제3자 로그인 계정(구글, 페이스북, ios 등)은 먼저 인게임에서 메일 계정 연동을 진행해 주세요.",
    "account.login.pleaseEnterVerificationCode": "인증 코드를 입력해 주세요",
    "account.login.get": "받기",
    "account.login.sent": "전송 완료",
    "account.register.accountRegistration": "회원 가입",
    "account.register.enterEmail": "이메일 입력",
    "account.register.passwordRequirements": "비밀번호는 8-20자리 숫자문자 조합으로 입력하세요",
    "account.register.confirmPassword": "비밀번호를 다시 입력하세요",
    "account.register.register": "계정 생성",
    "account.register.alreadyHaveAccount": "기존 계정으로 로그인",
    "account.register.agreeTerms": "저는 게임 서비스 계약 및 개인정보 보호 정책을 읽고 동의했습니다",
    "account.register.ageConfirmation": "저는 만 14세 이상입니다",
    "account.register.termsAndAgeConfirmation": "귀하의 권익을 보호하기 위해, 게임 서비스 계약 및 개인정보 보호 정책을 읽고 동의 및 만 14세 이상임을 확인해 주시기 바랍니다.",
    "account.register.termsConfirmation": "이용자의 권익을 보장하기 위하여 이용약관과 개인정보 처리방침을 읽고 동의하여 주세요.",
    "account.common.confirm": "확인",
    "account.common.return": "돌아가기",
    "account.forgotPassword.title": "비밀번호 분실",
    "account.forgotPassword.enterEmail": "이메일 입력",
    "account.forgotPassword.nextStep": "다음",
    "account.forgotPassword.noSecurityVerification": "계정이 보안 검증을 완료하지 않았습니다",
    "account.forgotPassword.contactSupport": "분실 된 비밀번호를 찾으시려면 이메일 또는 전화번호가 인증되어야 하며, 인증하지 않았을 ",
    "account.forgotPassword.contactSupport2": "경우 [고객센터]로 연락 바랍니다",
    "account.forgotPassword.cannotModify": "비밀번호 찾기에는 메일 또는 휴대폰 인증이 필요합니다. 인증을 진행하지 않으면 비밀번호를 변경할 수 없습니다",
    "account.securityVerification.title": "보안 인증",
    "account.securityVerification.methods": "다음 방법을 사용하여 인증할 수 있습니다",
    "account.securityVerification.phoneVerification": "전화번호 인증",
    "account.securityVerification.emailVerification": "이메일 인증",
    "account.securityVerification.phoneInstructions2": "[받기] 버튼을 클릭하면 인증코드가 귀하의 전화번호/이메일  { XX } 로 전송됩니다",
    "account.securityVerification.emailInstructions2": "[받기] 버튼을 클릭하면 인증 코드가 귀하의 이메일  { XX } 으로 전송됩니다",
    "account.securityVerification.enterVerificationCode": "인증코드 입력",
    "account.resetPassword.setNewPassword": "새 비밀번호 설정",
    "account.resetPassword.success": "비밀번호가 변경 되었습니다. 다시 로그인 하세요",
    "account.profile.accountInformation": "계정 정보",
    "account.profile.emailVerificationAvailable": "이제 이메일 인증이 가능합니다",
    "account.profile.emailVerificationImportance": "당사는 회원님의 계정 보안을 매우 중요하게 생각합니다. 계정 자산이 유출되지 않도록 이메일 인증을 진행해 주세요",
    "account.profile.skipVerification": "나중에 하기",
    "account.profile.startEmailVerification": "이메일 인증하기",
    "account.profile.nickname": "닉네임",
    "account.profile.accountName": "계정명",
    "account.profile.accountID": "계정ID",
    "account.profile.verifyEmail": "인증 이메일",
    "account.profile.phoneNumber": "휴대폰 번호",
    "account.profile.notBound": "미연동",
    "account.profile.notVerified": "미인증",
    "account.profile.notSet": "미설정",
    "account.profile.verify": "인증하기",
    "account.profile.bind": "연동",
    "account.profile.bound": "연동 완료",
    "account.profile.changeBind": "연동 교체",
    "account.profile.edit": "편집",
    "account.securityCheck.title": "보안 점검",
    "account.securityCheck.bindAccountName": "계정 연동하기",
    "account.securityCheck.accountNameBound": "연동 된 계정",
    "account.securityCheck.verifyEmail": "이메일 인증하기",
    "account.securityCheck.emailVerified": "이메일 인증 완료",
    "account.securityCheck.fillPersonalInfo": "개인 정보를 입력하세요",
    "account.securityCheck.personalInfoFilled": "개인 정보가 입력되었습니다",
    "account.securityCheck.bindPhoneNumber": "휴대폰 번호 연동하기",
    "account.securityCheck.phoneNumberBound": "휴대폰 번호 연동 완료",
    "account.securityCheck.completionProgress": "인증 메일 변경",
    "account.personalInfo.title": "개인 정보",
    "account.personalInfo.name": "이름",
    "account.personalInfo.birthday": "생일",
    "account.personalInfo.gender": "성별",
    "account.personalInfo.notSet": "미설정",
    "account.personalInfo.edit": "편집",
    "account.personalInfo.modify": "수정",
    "account.editNickname.title": "닉네임 수정",
    "account.editNickname.changeLimit": "닉네임을 설정하신 후에는 180일 동안 변경할 수 없습니다",
    "account.editNickname.currentNickname": "현재 닉네임",
    "account.editNickname.notSet": "미설정",
    "account.editNickname.enterNewNickname": "새로운 닉네임을 입력해 주세요",
    "account.editNickname.nicknameLength": "닉네임은 최소 3자 이상이어야 합니다",
    "account.editNickname.privacyWarning": "개인정보 보호를 위해 실명, 주소, 전화번호 또는 기타 개인 정보를 사용하지 마세요",
    "account.common.confirm": "확인",
    "account.common.cancel": "취소",
    "account.setEmailAccount.title": "이메일 계정 설정",
    "account.setEmailAccount.description": "인증 메일은 계정명으로 사용됩니다. 설정 완료 후 메일 로그인을 이용하실 수 있습니다",
    "account.setEmailAccount.agreement": "[확인] 버튼을 클릭하면 Playbest가 해당 전화번호를 저장하고 알림발송 및 보안 인증에 사용할 수 있도록 허용하는데 동의한 것으로 간주 됩니다",
    "account.setEmailAccount.enterEmail": "이메일 주소 입력",
    "account.setEmailAccount.enterVerificationCode": "인증코드 입력",
    "account.common.get": "받기",
    "account.common.sent": "전송 완료",
    "account.setEmailAccount.passwordRequirements": "비밀번호는 8-20자리 숫자문자 조합으로 입력하세요",
    "account.setEmailAccount.confirmPassword": "비밀번호를 다시 입력하세요",
    "account.common.confirm": "확인",
    "account.verifyEmail.title": "이메일 인증",
    "account.verifyEmail.agreement": "[인증] 버튼을 클릭하면 Playbest가 해당 전화번호를 저장하고 알림발송 및 보안 인증에 사용할 수 있도록 허용하는데 동의한 것으로 간주 됩니다",
    "account.verifyEmail.enterEmail": "이메일 주소 입력",
    "account.verifyEmail.enterVerificationCode": "인증코드 입력",
    "account.common.get": "받기",
    "account.common.sent": "전송 완료",
    "account.verifyEmail.verify": "인증",
    "account.bindPhoneNumber.title": "휴대폰 번호 연동하기",
    "account.bindPhoneNumber.description": "계정의 보안을 위하여 휴대폰 번호를 연동해 주세요",
    "account.bindPhoneNumber.agreement": "[연동] 버튼을 클릭하면 Playbest가 해당 휴대폰 번호를 저장하고 알림 발송 및 보안 인증에 사용할 수 있도록 허용하는 것에 동의한 것으로 간주됩니다",
    "account.bindPhoneNumber.select": "선택",
    "account.bindPhoneNumber.enterPhoneNumber": "휴대폰 번호 입력",
    "account.bindPhoneNumber.enterVerificationCode": "인증코드 입력",
    "account.common.get": "받기",
    "account.common.sent": "전송 완료",
    "account.bindPhoneNumber.bind": "연동",
    "account.changeEmail.title": "인증 메일 변경",
    "account.changeEmail.currentEmail": "현재 인증 이메일",
    "account.changeEmail.description": "이메일을 변경하면, 알림 및 인증 정보가 새 이메일로 전송됩니다",
    "account.securityVerification.title": "보안 인증",
    "account.securityVerification.methods": "다음 방법을 사용하여 인증할 수 있습니다",
    "account.securityVerification.phoneVerification": "휴대폰 번호 인증",
    "account.securityVerification.emailVerification": "이메일 인증",
    "account.securityVerification.emailInstructions": "[받기] 버튼을 클릭하면 인증 코드가 귀하의 이메일으로 전송됩니다",
    "account.securityVerification.enterVerificationCode": "인증코드 입력",
    "account.common.get": "받기",
    "account.common.sent": "전송 완료",
    "account.common.nextStep": "다음",
    "account.changeEmail.agreement": "[인증] 버튼을 클릭하면 Playbest가 해당 전화번호를 저장하고 알림발송 및 보안 인증에 사용할 수 있도록 허용하는데 동의한 것으로 간주 됩니다",
    "account.changeEmail.enterEmail": "이메일 주소 입력",
    "account.changePhoneNumber.title": "전화번호 변경",
    "account.changePhoneNumber.currentPhoneNumber": "현재 전화번호",
    "account.changePhoneNumber.description": "휴대폰 번호/이메일 주소 연동 교체 시 수락 알림 및 인증 정보가 새 번호/이메일 주소로 전송됩니다",
    "account.securityVerification.methods": "다음 방법을 사용하여 인증할 수 있습니다",
    "account.securityVerification.phoneVerification": "휴대폰 번호 인증",
    "account.securityVerification.phoneInstructions": "[받기] 버튼을 클릭하면 인증코드가 귀하의 휴대폰 번호 전송됩니다",
    "account.securityVerification.enterVerificationCode": "인증코드 입력",
    "account.common.get": "받기",
    "account.common.sent": "전송 완료",
    "account.common.nextStep": "다음",
    "account.changePhoneNumber.agreement": "[연동] 버튼을 클릭하면 Playbest가 해당 번호를 저장하고 알림 발송 및 보안 인증에 사용할 수 있도록 허용하는 것에 동의한 것으로 간주됩니다",
    "account.resetPassword.setNewPassword": "새 비밀번호 설정",
    "account.resetPassword.enterOriginalPassword": "현재 비밀번호를 입력해 주세요",
    "account.resetPassword.verifyOriginalPassword": "원래 비밀번호를 입력하세요",
    "account.resetPassword.setNewPassword": "새로운 비밀번호를 설정해 주세요",
    "account.personalInfo.enterFirstName": "이름을 입력하세요",
    "account.personalInfo.enterLastName": "성씨를 입력하세요",
    "account.personalInfo.selectGender": "성별을 선택하세요",
    "account.personalInfo.gender": "성별",
    "account.personalInfo.selectBirthday": "생일을 입력하세요",
    "account.personalInfo.birthdayWarning": "생일 정보는 제출 후 1회만 변경할 수 있음으로 신중하게 입력해 주세요",
    "account.personalInfo.male": "남자",
    "account.personalInfo.female": "여자",
    "account.personalInfo.secret": "기밀",
    "account.personalInfo.other": "기타",
    "account.common.save": "저장",
    "account.avatar.selectAvatar": "프로필을 선택하세요",
    "account.avatar.pleaseSelectAvatar": "프로필 사진을 선택하세요",
    "account.security.title": "보안 관리",
    "account.security.passwordManagement": "비밀번호 관리",
    "account.security.passwordUpdateRecommendation": "PLAYBEST계정 무단 액세스 방지 등 보안성을 위해 정기적인 비밀번호 변경을 권장 드립니다",
    "account.security.lastPasswordUpdateTime": "마지막 비밀번호 변경 시간",
    "account.security.changePassword": "비밀번호 변경",
    "account.common.modify": "수정",
    "account.common.set": "설정",
    "account.orders.myOrders": "나의 주문",
    "account.orders.all": "전부",
    "account.orders.pendingPayment": "결제 대기 중",
    "account.orders.pendingShipment": "발송 대기 중",
    "account.orders.completed": "이미 완료",
    "account.orders.closed": "이미 닫힘",
    "account.orders.allGames": "전체 게임",
    "account.orders.orderNumber": "주문번호",
    "account.orders.orderTime": "주문 시간",
    "account.orders.cancelOrder": "주문 취소",   
    "account.orders.paymentMethod": "결제 방법",
    "account.orders.noOrders": "주문이 존재하지 않습니다",
    // 注册相关
    "account.register.emailEmpty": "이메일 주소를 입력해 주세요",
    "account.register.passwordEmpty": "비밀번호를 입력해 주세요",
    "account.register.confirmPasswordEmpty": "비밀번호 확인을 빈칸으로 둘 수 없습니다",
    "account.register.emailInvalid": "유효한 이메일을 입력해 주세요",
    "account.register.emailAlreadyRegistered": "이미 등록된 메일주소입니다",
    "account.register.passwordLengthInvalid": "비밀번호는 8-20자 이내여야 합니다",
    "account.register.passwordTooSimple": "암호는 문자, 숫자, 특수문자 중 2가지 이상을 조합해야 합니다",
    "account.register.passwordInvalidChars": "비밀번호에 지원되지 않는 특수 문자가 포함되어 있습니다",
    "account.register.passwordMismatch": "두 번 입력한 비밀번호가 일치하지 않습니다. 다시 입력해 주세요",

    // 登录相关
    "account.login.emailEmpty": "이메일 주소를 입력해 주세요",
    "account.login.passwordEmpty": "비밀번호를 입력해 주세요",
    "account.login.emailInvalid": "유효한 이메일을 입력해 주세요",
    "account.login.emailNotRegistered": "존재하지 않는 유저",
    "account.login.credentialsInvalid": "이메일 또는 비밀번호가 잘못되었습니다. 다시 입력해 주세요",
    "account.login.tooManyAttempts": "비밀번호 입력 오류가 너무 많습니다. 잠시 후에 다시 시도해 주세요",

    // 验证码相关
    "account.verification.emailEmpty": "이메일 주소를 입력해 주세요",
    "account.verification.passwordEmpty": "비밀번호를 입력해 주세요",
    "account.verification.emailInvalid": "유효한 이메일을 입력해 주세요",
    "account.verification.codeSent": "인증 코드가 전송되었습니다",
    "account.verification.noSecurityEmail": "해당 계정은 안전 메일 인증을 하지 않았습니다",
    "account.verification.emailNotFound": "존재하지 않는 유저",
    "account.verification.codeInvalid": "인증번호를 정확히 입력해 주세요",
    "account.verification.codeReused": "인증번호가 만료되었습니다. 다시 시도해 주세요",

    // 邮箱绑定相关
    "account.email.alreadyBound": "이미 등록된 메일주소입니다",
    "account.email.bindSuccess": "계정명 연동 성공",
    "account.email.accountBindSuccess": "계정명 설정 및 이메일 인증이 성공적으로 완료되었습니다",
    "account.email.empty": "이메일 주소를 입력해 주세요",
    "account.email.passwordEmpty": "비밀번호를 입력해 주세요",
    "account.email.invalid": "유효한 이메일을 입력해 주세요",
    "account.email.codeEmpty": "인증번호를 입력해 주세요",
    "account.email.codeSent": "인증 코드가 전송되었습니다",
    "account.email.verificationSuccess": "이메일 인증 성공",

    // 通用错误
    "account.error.gameLimitExceeded": "메일 관련 계정 수 상한 도달",
    "account.error.tooManyRequests": "인증코드 요청이 너무 잦습니다. 잠시 후 다시 시도해 주세요",
    "account.error.dailyLimitExceeded": "금일 인증코드 요청 횟수가 최대치에 도달했습니다",
    "account.error.codeExpired": "인증번호가 만료되었습니다. 다시 시도해 주세요",

    // 手机绑定相关
    "account.phone.empty": "휴대폰 번호를 입력해 주세요",
    "account.phone.invalid": "유효한 휴대폰 번호를 입력해 주세요",
    "account.phone.bindSuccess": "전화번호 연동 성공",
    "account.phone.changeBindSuccess": "휴대폰 번호 변경이 성공적으로 완료되었습니다",
    "account.phone.areaCodeEmpty": "지역번호를 입력해 주세요.",  
    "account.phone.areaCode": "지역번호",

    // 密码相关
    "account.password.originalEmpty": "현재 비밀번호를 입력해 주세요",
    "account.password.newEmpty": "새로운 비밀번호를 입력해 주세요",
    "account.password.originalIncorrect": "입력한 기존 비밀번호가 잘못되었습니다. 다시 입력해 주세요",
    "account.password.sameAsCurrent": "현재 비밀번호와 같습니다",
    "account.password.mismatch": "새로운 비밀번호가 일치하지 않습니다, 다시 입력해 주세요",
    "account.password.changeSuccess": "비밀번호가 변경 되었습니다. 다시 로그인 하세요",

    // 忘记密码
    "account.forgotPassword.noSecurityContact": "계정이 보안 검증을 완료하지 않았습니다",

    // 昵称相关
    "account.nickname.empty": "새로운 닉네임을 입력해 주세요",
    "account.nickname.tooShort": "닉네임은 최소 3자 이상이어야 합니다",
    "account.nickname.tooLong": "닉네임이 너무 깁니다. 다시 입력해 주세요",
    "account.nickname.sameAsCurrent": "입력하신 닉네임이 현재 닉네임과 같습니다. 다시 입력해 주세요",
    "account.nickname.containsSensitiveWords": "닉네임에 금지어가 포함되어 있습니다, 다시 입력해 주세요",
    "account.nickname.editLimitReached": "닉네임 변경은 180일 간격으로 가능합니다. 잠시만 기다려주세요.",
    "account.nickname.setSuccess": "닉네임 설정 성공",

    // 其他错误
    "account.error.network": "네트워크가 불안정합니다, 잠시 후 다시 시도해 주세요",
    "account.error.loginExpired": "로그인 정보가 더 이상 유효하지 않습니다. 다시 로그인 하세요",

    // 注销账号
    ...{
        "account.security.deregister.s10.t1": "계정 말소 중",
        "account.security.deregister.s8.t1": "계정 말소 기각",
        "account.security.deregister.s9.t1": "계정 말소 심사 중",
        "account.security.deregister": "계정 말소",
        "account.security.deregister.apply": "말소 신청",
        "account.security.deregister.desc": "계정 말소 신청 후 30일 내에 삭제가 완료됩니다.",
        "account.security.deregister.popup.title": "Playbest 계정 말소",
        "pages.security.deregister.8": "{gamename} 게임 계정 말소",
        "account.security.deregister.s1.title": "주의! 귀하는 Playbest 계정 말소 신청을 하고 계십니다!",
        "account.security.deregister.s1.p1": "말소 계정:{XXX}",
        "account.security.deregister.s1.p2": "계정이 말소되면 영구 삭제되며 복구가 불가능합니다.",
        "account.security.deregister.s1.p3": "계정 내 모든 게임, 가상화폐 잔액, 미사용 완료 서비스, 개인정보와 기록도 함께 영구 삭제됩니다.",
        "account.security.deregister.s1.f1": "Playbest 계정 말소를 여전히 원하신다면, 본 절차 안내에 따라 말소를 완료하시기 바랍니다.",
        "account.security.deregister.nextStep": "다음",
        "account.security.deregister.s3.btn1": "다음({XXX}s)",
        "account.security.deregister.s3.t1": "게임 계정 말소 조건을 읽으시고\n페이지 하단에 체크하여 확인하십시오.",
        "account.security.deregister.s4.t1": "게임 계정 말소 조건을 읽으시고\n페이지 하단에 체크하여 확인하십시오.",
        "account.security.deregister.s3.check": "내용을 자세히 읽었으며 계정 말소 조건을 충족합니다.",
        "account.security.deregister.s4.check": "‘말소 주의사항’을 자세히 읽었으며 이에 동의합니다.",
        "account.security.deregister.s3.p1": "친애하는 플레이어님, 귀하의 탈퇴에 깊은 유감을 표합니다. 귀하의 계정과 자산 안전을 보장하기 위해, 제출하신 탈퇴 요청이 유효하기 위해서는 다음 조건을 충족해야 합니다. 저희 게임 팀은 필요에 따라 검증할 권한이 있습니다:",
        "account.security.deregister.s3.p2": "1. 계정이 안전한 상태여야 함: 계정이 정상적으로 사용 중이며, 위반 처리가 이루어지지 않아야 하고(게임 관련 협약 및 규칙 위반에 따라 계정이 정지되는 등의 조치) 도난, 정지 등 위험이 없어야 합니다\n2. 게임 수익이 정산되었거나 적절히 처리되어야 함: 여기에는 게임 토큰, 기타 가상 아이템 및 기타 게임 부가 서비스가 포함되며(편의상 “게임 수익”으로 통칭), 귀하께서 적절히 처리해야 합니다. 미처리 시 해당 게임 수익을 자발적으로 포기한 것으로 간주합니다\n3. 계정에 미완료 상태의 주문이 없어야 함: 여기에는 게임 토큰 충전, 기타 가상 아이템 구매 및 기타 주변 상품 구매가 포함됩니다\n4. 계정에 어떠한 분쟁이 없어야 함: 여기에는 불만 신고, 제소, 중재, 소송 등이 포함됩니다\n5. 계정에 아무런 위험이 없어야 함: 해당 계정이 최근 1개월 내에 비정상 로그인 기록이 없고, 기타 보안 위험이 없어야 합니다\n6. 계정에 어떠한 거래가 없어야 함: 해당 계정 내에 미완료된 주문이나 거래가 없어야 하며, 여기에는 플랫폼 주변 상점에서의 미완료 주문이 포함됩니다\n7. 계정에 어떠한 계약이 없어야 함: 해당 계정이 삭제로 인해 남아 있는 미결 계약 관계 및 기타 권리 의무가 존재하지 않거나, 플랫폼에서 삭제로 인해 발생할 수 있는 분쟁에 대한 권리 의무가 미완료되지 않도록 해야 합니다\n8. 기타 충족해야 할 조건\n\n*팁: 계정 탈퇴에는 30일의 냉각 기간이 있으며, 검토 기간 동안 귀하의 계정은 동결 상태에 들어갑니다. 탈퇴 요청을 철회하고 싶으시면, 동결 기간 동안 게임에 로그인하여 동결을 해제하시거나 저희에게 연락해 주세요. 귀하의 계정은 동결 기간이 끝난 후 완전히 삭제됩니다.\n사용자가 게임 내에서 하나 이상의 지역에 캐릭터를 생성할 수 있으므로, 게임 내 여러 지역에서 캐릭터를 생성한 경우, 게임 계정 삭제는 해당 계정 아래의 모든 지역 및 캐릭터에 대해 삭제됩니다.",
        "account.security.deregister.s4.p1": "1. 말소하기 전에 귀하의 Playbest 계정이 다음 조건을 동시에 충족하는지 확인하십시오. 본 조항의 조건 중 하나라도 충족하지 못하는 경우 Playbest는 계정 말소 신청을 거부할 권리가 있습니다.\na) 말소 신청 시, 귀하의 Playbest 계정은 정상적으로 사용 가능하며, 계정 보안에 위험을 초래할 수 있는 어떤 상황(통행증 계정의 차단 또는 동결, 비정상적인 계정 로그인 행위, 비정상적인 사용 행위 등을 포함하되 이에 국한되지 않음)도 없어야 합니다. \nb) 어떤 소유권 분쟁도 없으며, 타인의 계정을 불법적으로 사용하는 상황, 제3자와 계정을 공유하는 상황 및/또는 계정 사용권에 대한 분쟁을 일으키는 기타 상황 또는 제3자의 정당한 권익을 침해할 수 있는 상황이 없어야 합니다.\n 2. 귀하는 말소 주의사항을 읽고 이에 동의하였습니다. 계정보안검증 및 개인정보검증을 통과한 후 해당 계정은 공식적으로 계정말소동결기간(이하 ‘동결기간’)에 들어가며, 동결기간은 [30]일입니다. 동결기간 내에 해당 계정을 사용하여 로그인하거나 Playbest 서비스를 이용할 경우 로그인 페이지의 ‘활성화’ 버튼을 클릭하여 계정을 재활성화할 수 있습니다(즉, 유저가 ‘활성화’ 버튼을 클릭하면 해당 계정의 말소 절차가 즉시 종료되고 계정은 다시 정상적으로 사용 가능합니다). 동결기간 내에 해당 계정으로 로그인하지 않았거나 해당 계정을 통해 Playbest 서비스를 이용하지 않은 경우 해당 계정은 동결기간 종료 후 말소됩니다.\n3. Playbest 계정 말소의 결과\n a) 귀하의 Playbest 계정이 말소되면 더 이상 해당 계정으로 로그인하여 Playbest 서비스를 이용하실 수 없습니다. 통행증 계정에 로그인하거나 동일한 등록 정보로 다시 통행증 계정에 가입하여 Playbest 서비스를 이용하더라도 해당 계정에 설정한 콘텐츠와 정보는 찾을 수 없습니다. \n b) 귀하가 해당 계정으로 로그인하여 Playbest 서비스를 이용하였을 때 생성된 모든 콘텐츠, 정보, 데이터, 역할, 기록은 삭제 또는 익명화 처리됩니다(법률과 법규에 따로 규정되어 있지 않은 한). 귀하는 앞서 언급한 삭제 또는 익명화된 콘텐츠, 정보, 데이터, 기록을 더 이상 방문, 전송, 획득, 계속 사용하거나 찾을 수 없으며 Playbest에 찾기 요청을 제기할 권한이 없습니다.\n c) 귀하가 Playbest 계정을 이용하는 동안 이미 획득한 충전 잔액 및 충전으로 획득한 관련 가상 아이템, 과거 게임 기록, 각종 쿠폰, VIP 회원카드, 월 카드, 주 카드, 유통기한이 지나지 않은 아이템, 미완료 미션, 해당 계정 내 기타 발생하였으나 아직 소모되지 않은 권익 및/또는 발생할 것으로 예상되는 권익 등은 스스로 포기한 것으로 간주하여 더 이상 사용할 수 없으며 회복할 수 없습니다.\n d) 일단 계정 말소가 완료되면, 귀하가 계정에 등록하여 해당 Playbest 서비스를 이용할 때 당사와 체결했던 모든 계약은, 해당 계약 중 귀하와 계속 유지하기로 약정한 부분 또는 법률 법규에 특별한 규정이 없는 한, 종료됩니다.\n e)Playbest 계정을 말소한다고 해서 계정 말소 전에 발생한 계정 행위와 그에 따른 책임이 면제되거나 경감되는 것은 아닙니다.\n 4. Playbest 계정 말소가 완료되면 귀하의 계정과 관련된 개인정보 및 데이터는 삭제 또는 익명화 처리됩니다. 귀하는 해당 법률과 법규가 허용하는 최대 범위 내에서, 특정 상황에서, 귀하의 개인 정보를 삭제하지 못할 수 있음을 알고 이에 동의합니다. 이러한 상황은 다음과 같습니다.\n a) 당사의 업무, 시스템 및 유저를 사기 활동으로부터 보호해야 할 경우,\n b) 기존 기능을 손상시키는 기술적 문제에 대응해야 할 경우,\n c) 기타 유저의 권리 행사를 위하여 필요한 경우,\n d) 합법적인 절차에 따라 법 집행 요구사항을 준수해야 할 경우,\n e) 과학적 또는 역사적 연구에 사용될 경우,\n f) 귀하 및 당사와의 관계와 합리적으로 관련된 당사의 내부 목적 또는 법적 의무를 이행해야 할 경우.",
        "pages.security.deregister.23": "1. 귀하의 게임 계정의 말소는 Playbest 통행증의 정상적인 이용에 영향을 미치지 않습니다. 해당 통행증 계정을 통해 Playbest의 다른 게임이나 Playbest 홈페이지에 계속 로그인할 수 있습니다.\n2. 말소하기 전에 귀하의 게임 계정이 다음 조건을 동시에 충족하는지 확인하십시오. 본 조항의 조건 중 하나라도 충족하지 못하는 경우 Playbest는 계정 말소 신청을 거부할 권리가 있습니다.\na) 말소 신청 시, 귀하의 계정은 정상적으로 사용 가능하며, 계정 보안에 위험을 초래할 수 있는 어떤 상황(통행증 계정의 차단 또는 동결, 비정상적인 계정 로그인 행위, 비정상적인 사용 행위 등을 포함하되 이에 국한되지 않음)도 없어야 합니다. \nb) 어떤 소유권 분쟁도 없으며, 타인의 계정을 불법적으로 사용하는 상황, 제3자와 계정을 공유하는 상황 및/또는 계정 사용권에 대한 분쟁을 일으키는 기타 상황 또는 제3자의 정당한 권익을 침해할 수 있는 상황이 없어야 합니다.\n3. 귀하는 말소 주의사항을 읽고 이에 동의하였습니다. 계정보안검증 및 개인정보검증을 통과한 후 해당 계정은 공식적으로 계정말소동결기간(이하 ‘동결기간’)에 들어가며, 동결기간은 [30]일입니다. 동결기간 내에 해당 계정을 사용하여 로그인하거나 Playbest 서비스를 이용할 경우 로그인 페이지의 ‘활성화’ 버튼을 클릭하여 계정을 재활성화할 수 있습니다(즉, 유저가 ‘활성화’ 버튼을 클릭하면 해당 계정의 말소 절차가 즉시 종료되고 계정은 다시 정상적으로 사용 가능합니다). 동결기간 내에 해당 계정으로 로그인하지 않았거나 해당 계정을 통해 Playbest 서비스를 이용하지 않은 경우 해당 계정은 동결 기간 종료 후 말소됩니다.\n4. Playbest 계정 말소의 결과\n a) 귀하의 게임 계정이 말소되면 더 이상 해당 계정으로 로그인하여 해당 게임이 제공하는 서비스를 이용하실 수 없습니다. 또한 해당 계정에 로그인하거나 동일한 등록 정보로 다시 {} 계정에 가입하더라도 해당 계정에 설정한 콘텐츠와 정보는 찾을 수 없습니다. \n b) 귀하가 해당 계정으로 로그인하여 서비스를 이용하였을 때 생성된 모든 콘텐츠, 정보, 데이터, 역할, 기록은 삭제 또는 익명화 처리됩니다(법률과 법규에 따로 규정되어 있지 않은 한). 귀하는 앞서 언급한 삭제 또는 익명화된 콘텐츠, 정보, 데이터, 기록을 더 이상 방문, 전송, 획득, 계속 사용하거나 찾을 수 없으며 Playbest에 찾기 요청을 제기할 권한이 없습니다.\nc) 귀하가  계정을 이용하는 동안 이미 획득한 충전 잔액 및 충전으로 획득한 관련 가상 아이템, 과거 게임 기록, 각종 쿠폰, VIP 회원카드, 월 카드, 주 카드, 유통기한이 지나지 않은 아이템, 미완료 미션, 해당 계정 내 기타 발생하였으나 아직 소모되지 않은 권익 및/또는 발생할 것으로 예상되는 권익 등은 스스로 포기한 것으로 간주하여 더 이상 사용할 수 없으며 회복할 수 없습니다.\n d) 일단 계정 말소가 완료되면, 귀하가 계정에 등록하여 해당 게임이 제공한 서비스를 이용할 때 당사와 체결했던 모든 계약은, 해당 계약 중 귀하와 계속 유지하기로 약정한 부분 또는 법률 법규에 특별한 규정이 없는 한, 종료됩니다.\n e) 해당 게임 계정을 말소한다고 해서 계정 말소 전에 발생한 계정 행위와 그에 따른 책임이 면제되거나 경감되는 것은 아닙니다.\n5. 계정 말소가 완료되면 귀하의 계정과 관련된 개인정보 및 데이터는 삭제 또는 익명화 처리됩니다. 귀하는 해당 법률과 법규가 허용하는 최대 범위 내에서, 특정 상황에서, 귀하의 개인 정보를 삭제하지 못할 수 있음을 알고 이에 동의합니다. 이러한 상황은 다음과 같습니다.\n a) 당사의 업무, 시스템 및 유저를 사기 활동으로부터 보호해야 할 경우,\n b) 기존 기능을 손상시키는 기술적 문제에 대응해야 할 경우,\n c) 기타 유저의 권리 행사를 위하여 필요한 경우,\n d) 합법적인 절차에 따라 법 집행 요구사항을 준수해야 할 경우,\n e) 과학적 또는 역사적 연구에 사용될 경우,\n f) 귀하 및 당사와의 관계와 합리적으로 관련된 당사의 내부 목적 또는 법적 의무를 이행해야 할 경우.",
        "pages.security.deregister.24": "다음",
        "pages.security.deregister.25": "계정 말소 취소",
        "account.security.deregister.s5.t1": "계정 말소 원인",
        "pages.security.deregister.27": "말소 계정",
        "account.security.deregister.s5.t3": "다중 선택, 최대 3항 선택 가능",
        "account.security.deregister.s5.p1": "게임이 재미 없음",
        "account.security.deregister.s5.p2": "게임 혜택 부족",
        "account.security.deregister.s5.p3": "충전 이슈",
        "account.security.deregister.s5.p4": "게임하기 싫어졌음",
        "account.security.deregister.s5.p5": "이 계정을 더 이상 사용하지 않음",
        "account.security.deregister.s5.p6": "기타",
        "account.security.deregister.s5.p7": "기타 말소 사유는 30자 이내로 기입하여 주세요",
        "account.security.deregister.s5.b1": "제출",
        "account.security.deregister.s6.t1": "회원 말소를 정말 진행하시겠습니까?",
        "account.security.deregister.s6.p1": "친애하는 유저님, 여러분의 지원에 감사드립니다.",
        "account.security.deregister.s6.p2": "말소 절차가 완료되면 계정이 삭제되므로 철저히 확인하고 진행하시기 바랍니다.",
        "account.security.deregister.s6.p3": "전체 말소 과정이 완료되면 귀하의 계정 데이터와 본 계정을 통해 제품에 로그인한 후 생성된 사용 데이터는 영구적으로 삭제되며 복구가 불가능하므로 신중히 선택하여 진행하시기 바랍니다.",
        "account.security.deregister.s6.b1": "말소 확인",
        "account.security.deregister.s6.b2": "고민해 볼게요",
        "account.security.deregister.s7.t1": "귀하의 계정 말소 신청서가 성공적으로 제출되었습니다",
        "account.security.deregister.s7.t1-1": "저희 게임에 보내주신 성원에 감사드립니다",
        "pages.security.deregister.45": "말소 계정:",
        "account.security.deregister.s7.p2": "신청 시간:{XXX}",
        "account.security.deregister.s7.p3": " 법률과 법규의 규정 및 말소 계약서에 약정된 경우를 제외하고 신청서를 제출한 날로부터 30영업일 이내에 말소 검토를 완료합니다.",
        "account.security.deregister.s7.d2": "검토 기간 귀하의 계정은 동결됩니다. 말소 신청을 철회하시려면 계정 동결기간 게임에 로그인하여 동결을 해제하시거나 우리와 연락해 주시기 바랍니다. 귀하의 계정은 동결 기간이 끝나면 완전히 삭제됩니다.",
        "account.security.deregister.s7.d3": "[알겠습니다]버틀을 클릭하고 현재 계정 로그아웃",
        "account.security.deregister.s7.b1": "알겠습니다",
        "account.security.deregister.s3.btn2": "말소 철회",
        "pages.security.deregister.52": "탈퇴하지 않겠습니다",
        "pages.security.deregister.53": "계정 말소 중",
        "account.security.deregister.s5.t2": "말소 계정:{XXX}",
        "pages.security.deregister.55": "신청 시간:",
        "pages.security.deregister.56": "귀하의 계정은 이미 말소 신청서를 제출하였습니다",
        "account.security.deregister.s10.t2": "말소 신청서를 제출한 날로부터 30일 이내에 완료됩니다",
        "pages.security.deregister.58": " 법률과 법규의 규정 및 말소 계약서에 약정된 경우를 제외하고 신청서를 제출한 날로부터 30영업일 이내에 말소 검토를 완료합니다.",
        "account.security.deregister.s8.p4": "탈퇴 절차를 포기하려면 [탈퇴 철회]버튼을  클릭하고, 해당 계정을 정말 탈퇴하려면 [계정 변경] 버튼을 클릭한 후 다른 계정을 통해 로그인할 수 있습니다.",
        "pages.security.deregister.60": "말소 철회",
        "account.security.deregister.s8.b1": "계정 교체",
        "pages.security.deregister.62": "귀하의 계정 말소 신청이 거부되었습니다",
        "pages.security.deregister.63": "말소 계정:",
        "pages.security.deregister.64": "신청 시간:",
        "account.security.deregister.s8.t2": "친애하는 유저님, 검토 결과 귀하의 계정에는 다음과 같은 위험이 있어 현재 말소 조건을 충족하지 않습니다",
        "pages.security.deregister.67": "말소 절차 철회를 원하시면 ‘말소 철회’를 클릭하십시오 우리는 당신을 위해 게임에 입장할 겁니다. 그래도 계정 말소를 원하신다면 ‘계정 교체’을 클릭하신 후 다른 계정으로 로그인하실 수 있습니다.",
        "pages.security.deregister.68": "말소 철회",
        "pages.security.deregister.69": "계정 교체",
        "pages.security.deregister.70": "귀하의 계정 말소 신청은 현재 검토 중입니다",
        "pages.security.deregister.71": "말소 계정:",
        "pages.security.deregister.72": "신청 시간:",
        "pages.security.deregister.73": "계정 말소 심사 중",
        "account.security.deregister.s9.p3": "친애하는 유저님, 기다려 주셔서 감사합니다. 귀하의 계정 말소 신청은 이미 접수되었습니다. 시스템에서 귀하의 계정에 이상이 감지되어 귀하의 신청서를 수동 검토 프로세스로 전환합니다.",
        "account.security.deregister.s9.p4": "수동 검토 과정은 시간이 걸릴 수 있으니 기다려주시기 바랍니다. 이해와 지원에 감사드립니다.",
        "account.security.deregister.s9.p5": "말소 절차 철회를 원하시면 ‘말소 철회’를 클릭하십시오 우리는 당신을 위해 게임에 입장할 겁니다. 그래도 계정 말소를 원하신다면 ‘계정 교체’을 클릭하신 후 다른 계정으로 로그인하실 수 있습니다.",
        "pages.security.deregister.77": "말소 철회",
        "pages.security.deregister.78": "계정 교체",
        "account.security.deregister.msg2": "계정 말소를 철회하였습니다",
        "account.security.deregister.msg1": "최소 1개의 말소 원인을 선택해 주세요",
        "pages.security.deregister.81": "계정 말소는 매일 1번만 가능합니다, 내일 다시 말소를 진행해 주세요",
        "pages.security.deregister.82": "계정 말소 중으로 현재 결제를 진행할 수 없습니다",
        "pages.security.deregister.83": "문의사항이 있으시다면 고객센터를 찾아주세요",
        "pages.security.deregister.84": "계정 말소 중. 문의사항이 있으시다면 고객센터를 찾아주세요 또는 계정 센터에 로그인하여 처리하십시오",
        "pages.security.deregister.85": "계정 말소 기각. 문의사항이 있으시다면 고객센터를 찾아주세요 또는 계정 센터에 로그인하여 처리하십시오",
        "pages.security.deregister.86": "계정 말소 심사 중. 문의사항이 있으시다면 고객센터를 찾아주세요 또는 계정 센터에 로그인하여 처리하십시오",
        "pages.security.deregister.s7.app": "페이지를 닫으면 현재 계정을 로그아웃시킬 것입니다."
    },

    "account.inner.lastName": "성",
    "account.inner.firstName": "이름",
    "account.inner.goToSetting": "설정",
    "account.inner.nicknameNotSet": "닉네임 미설정",
    "account.inner.bind": "연동",
    "account.inner.verify": "인증하기",
    "account.inner.changeBinding": "연동 교체",
    "account.inner.bound": "연동 완료",
    "account.inner.passwordManagement": "비밀번호 관리",
    "account.inner.lastUpdate": "마지막 업데이트",
    "account.inner.update": "업데이트",
    "account.inner.switchAccount": "계정 전환",
    "account.inner.deleteAccount": "회원 탈퇴",
    "account.inner.unbind": "연동 해제",
    "account.inner.unbindConfirm": "{media_name} 연동을 해제하시겠습니까?",
    "account.inner.unbindConfirmDesc": "연동 해제 후에는 {media_name} 계정 {nickname}을(를) 사용하여 제3자 로그인 방식으로 해당 게임 계정에 로그인하실 수 없습니다.",
    "account.inner.hint":"알림",
    "account.inner.confirmSwitchAccount": "계정을 전환하시겠습니까?",
    "account.inner.step.verify": "검증",
    "account.inner.step.finished": "완료!",
    "account.inner.step.verifySuccess": "검증 완료",
    "account.inner.step.ok":"확인",
    "account.inner.bindAccount.desc": "설정 후 메일 계정과 비밀번호로 로그인하실 수 있습니다.",
    "account.inner.unbindAccount.onlyOne": "게임에 정상적으로 로그인할 수 있도록 최소한 하나의 제3자 로그인 방식을 유지해 주세요. 또는 계정명을 먼저 연동한 후에 연동 해제를 진행해 주세요.",
    "account.inner.unbindAccount.unbindSuccess": "연동 해제",
    "account.inner.setAccountName.title": "계정 이름을 설정하다",
    "account.inner.setAccountName.success": "설정 완료",
    "account.inner.setAccountName.desc": "계정명을 설정하였습니다",
    "account.inner.emailVerificationSuccess": "메일 인증 완료",
    "account.inner.emailVerificationSuccessDesc": "메일 인증을 완료하였습니다.\n알림 및 인증 정보가 해당 메일로 발송됩니다.",
    "account.inner.changeEmailSuccess": "인증 메일 변경 완료",
    "account.inner.changeEmailSuccessDesc": "메일 변경을 완료하였습니다.\n알림 및 인증 정보가 새로운 메일로 발송됩니다.",
    "account.inner.phoneBindSuccess": "휴대전화 번호 연동 완료",
    "account.inner.phoneBindSuccessDesc": "휴대전화 번호 연동을 완료하였습니다.\n알림 및 인증 정보가 해당 번호로 발송됩니다.",
    "account.inner.changePhoneSuccess": "인증 휴대전화 번호를 변경했습니다",
    "account.inner.changePhoneSuccessDesc": "휴대전화 번호 변경을 완료하였습니다.\n알림 및 인증 정보가 새로운 휴대전화 번호로 발송됩니다.",
    "account.inner.changePasswordSuccess": "비밀번호 변경 완료",
    "account.inner.changePasswordSuccessDesc": "다시 로그인해 주세요",
    "account.inner.bindSuggestion": "존경하는 플레이어님, 계정의 안전과 편리한 이용을 위해 이메일 계정명 또는 소셜 미디어 계정을 연동하시길 권장 드립니다. 계정을 연동하지 않을 경우 일부 기능이 제한될 수 있습니다.",
    "account.inner.bindSuccess": "연동 완료",
    "account.profile.editProfile": "프로필 편집",
}
