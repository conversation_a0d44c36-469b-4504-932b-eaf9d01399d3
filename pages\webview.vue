<template>
    <div class="web_view">
        <!-- <div class="order_fixed_model show"></div> -->
        <div class="order_fixed_wrap show" style="display: block; height: 90%;">
            <div class="order_fixed_box">
                <CloseOutlined class="order_f_close" @click="closeOrderFixed" />
                <div class="title">{{ $t('pay.comfirm.account') }}</div>
                <!-- 订单主体 -->
                <div v-if="webviewLoading" class="order_list_empty">
                    <div class="c_loading"></div>
                </div>
                <div class="order_f_main" v-if="!webviewLoading">
                    <h3>{{ $t('pages.pay.character-information') }}</h3>
                    <div class="line">
                        <div class="left">{{ $t("pages.pay.server") }}:</div>
                        <div>{{ serverName }}</div>
                    </div>
                    <div class="line">
                        <div class="left">{{ $t('pages.pay.purchasing-character') }}:</div>
                        <div>{{ roleName }}</div>
                    </div>
                    <h3>{{ $t('pages.pay.item-information') }}</h3>
                    <!-- 商品名称(商品信息) -->
                    <div class="line">{{ productName }}</div>
                    <!-- 选择币种 -->
                    <div class="currency_line" @click="showCurrencyDrawer">
                        <h3>{{ $t('pages.pay.currency') }}</h3>
                        <div class="currency_name">{{ showCurrencyActive?.name }}</div>
                    </div>
                    <div class="price_line"><span>{{ $t("pages.pay.order-price") }}：</span>
                        <div class="price_num">{{ showCurrencyActive?.name }} {{ curProduct.price || 0 }}</div>
                    </div>
                    <h3>{{ $t("pages.pay.payment-mode") }}</h3>
                    <payPaymentMethods v-model:payMethodVal="payMethodVal" :pageInfo="{paymentMethods:paymentMethods,currency:showCurrencyActive?.name}"
                        :mycardItems="mycardItems" :curMycardItemId="curMycardItemId"
                        paymentType='mobile'
                        @setPayMethod="setPayMethodVal"
                        @selectMycardItem="chooseMycardItem" 
                        @setSubmitData="setSubmitData"
                        @setQrCodeUrl="setQrCodeUrl"
                        @setHrefJump="setHrefJump"
                        ref="paymentMethodsRef" />
                    

                </div>
                <!-- 支付按钮 -->
                <div class="order_f_bottom" v-if="!hideBtn">
                    <a-button class="order_f_payBtn" type="primary" :loading="orderLoading" :disabled="orderLoading"
                        @click="onSubmit" data-track-id="805">{{
                            $t('pages.pay.purchase-now') }}</a-button>
                </div>
            </div>
        </div>
    </div>

    <!-- adyen 支付 -->
    <PayAdyen :json="submitData" :is-webview="true" :app-order="appOrder"
        v-if="!(submitData && submitData.status == 'fail')"></PayAdyen>

    <!-- 币种选择弹层 -->
    <a-drawer v-model:visible="showCurrencyPop" class="currency_select_drawer" placement="bottom"
        :title="$t('pages.pay.currency')">
        <a-radio-group v-model:value="currencyActive">
            <a-radio v-for="item in currencyList" :key="item.code" :value="item.code">
                {{ item.name }}
            </a-radio>
        </a-radio-group>
        <template #footer>
            <a-button class="order_f_payBtn" type="primary" @click="currencyChange">{{
                $t('pay.role.comfirm') }}</a-button>
        </template>
    </a-drawer>
</template>

<script lang="ts" setup>
import {
    getWebviewCurrency,
    getWebviewChannelList,
    getWebviewProductPrice,
    postWebviewSubmit
} from "~/api/webview";
import { LanguageLabels } from '~/constants/common';
const { $jsbridge } = useNuxtApp() as any;
// 不使用布局
definePageMeta({
    layout: false
})

// 弹窗延迟时间
const duration = 3;
// 语言
const { locale, t } = useI18n();
// 路由
const route = useRoute();
// 页面内loading
const webviewLoading = ref<boolean>(true);
// 获取客户端参数
const projectId = ref(route.query.projectId); // app传入的项目id
const channelId = ref(route.query.channelId);
const productCode = ref(route.query.productCode);
const serverId = ref(route.query.serverId);
const serverName = ref(route.query.serverName);
const packageName = ref(route.query.packageName);
const uid = ref(route.query.uid);
const userName = ref(route.query.userName);
const roleId = ref(route.query.roleId);
const roleName = ref(route.query.roleName);
const productName = ref(route.query.productName);
const appOrderId = ref(route.query.appOrderId);
const language = ref(Object.keys(LanguageLabels).includes(route.query.language as string) ? route.query.language as string : 'en');
// 改变当前页面语言
locale.value = language.value;
// 改变cookie中的APP_LANG
const currentCookieLang: any = useCookie("APP_LANG", { maxAge: 86400 * 365 }); //获取cookie中的lang
currentCookieLang.value = locale.value;

// webview获取商品价格
const curProduct = ref<any>({
    price: 0,       // 价格
    id: undefined, // 商品ID
});

// 支付方式列表
const paymentMethods = ref<any[]>([]);
// 生单用支付方式KitId
const curKitId = ref<any>(undefined);
// 选中支付状态
const payMethodVal = ref(0);
// 提交loading
const orderLoading = ref<any>(false);
// 接口请求失败隐藏按钮
const hideBtn = ref<any>(false);
// 用于信用卡支付提交
const submitData = ref<any>({});
// 订单配置
const appOrder = ref<any>({
    projectId: undefined, // 项目ID
    serverId: serverId.value,  // 服务器ID
    roleId: roleId.value,  // 角色id
    kitId: undefined, // 生单参数
    uid: uid.value,  // 角色登录ID
    language: language,
    isTest: true,
    userName: userName.value,  // 用户名称
    currency: undefined,    // 币种
    productId: undefined,  //商品选择后的id
    orderId: undefined, // 订单号
    schemeId: undefined, // webview获取充值渠道列表接口获取到的方案id
    packageName: undefined, // 包名
    appOrderId: appOrderId.value || "",
});

// 从币种接口获取的项目ID
const projectIdFromCurrency = ref<any>('');
// 当前选择币种
const showCurrencyActive = ref<any>({});
const currencyActive = ref<any>('');
// 币种列表
const currencyList = ref<any[]>([]);
// 币种选择弹窗
const showCurrencyPop = ref<boolean>(false);
// 选择币种弹层
const showCurrencyDrawer = () => {
    showCurrencyPop.value = true;
}
// 选择币种
const currencyChange = async () => {
    try {
        const [channelList, productPrice]: any = await Promise.all([
            getWebviewChannelList(projectIdFromCurrency.value, currencyActive.value, language.value),
            getWebviewProductPrice(projectIdFromCurrency.value, productCode.value as string, currencyActive.value, language.value)
        ])
        if (channelList && channelList.code == 200 || channelList.code == 0) {
            let { data } = channelList;
            // 支付方式列表
            paymentMethods.value = data?.paymentMethods || [];
            appOrder.value.schemeId = data?.schemeId || ''
            curKitId.value = undefined
            payMethodVal.value = 0
        } else {
            hideBtn.value = true;
            message.error({
                content: () => channelList.message,
                class: 'c_message_big',
                duration,
            });
        }
        if (productPrice && productPrice.code == 200 || productPrice.code == 0) {
            let { data } = productPrice;
            // 获取商品价格与id
            curProduct.value = data || {};
            showCurrencyActive.value = currencyList.value.find(f => f.code == currencyActive.value)
            showCurrencyPop.value = false;
        } else {
            hideBtn.value = true;
            message.error({
                content: () => productPrice.message,
                class: 'c_message_big',
                duration,
            });
        }
    } catch (error) {
        console.log('error', error);
    }
}
// 选择支付方式
const setPayMethodVal = (item: any) => {
    let { id, KitId, Name } = item;
    payMethodVal.value = id;
    curKitId.value = KitId;
    // 埋点: 选择支付方式
    YKTrack.track('click', {
        params: {
            id: '804',
            value1: id, // 支付方式id
        }
    })
};

// 支持新的支付

// 支付宝二维码
const qrCodeUrl = ref("");
const setQrCodeUrl = (url: string) => {
    qrCodeUrl.value = url;
}
// 页面配置
const pageInfo = ref<any>({
    currency: undefined,
    paymentMethods:[],
});

// 支付组件
const paymentMethodsRef = ref<any>(null);

const setHrefJump = (url: string) => {
    const aDom = document.createElement('a');
    aDom.target = '_blank';
    aDom.href = url;
    aDom.click();
};

// adyen 支付赋值
const setSubmitData = (data: any) => {
    submitData.value = data;
}
// mycard 配置
const mycardItems = ref<any[]>([]);
const curMycardItemId = ref(0);
/**
 * 选择mycard 的商品
 * @param id 
 */
 const chooseMycardItem = (index: number, id: number) => {
    curMycardItemId.value = appOrder.value.productId = id;
    // console.log(curMycardItemId.value, appOrder.value.productId ,id)
    curProduct.value = mycardItems.value[index];
    appOrder.value.activityId = mycardItems.value[index].activityId || 0;
    // 埋点: 选择mycard商品
    YKTrack.track('click', {
        params: {
            id: '808',
            value1: id, // mycard商品id
        }
    })
}
// order status 弹窗关闭回调
// const orderStatusClose = (status: boolean) => {
//     orderStatusOpen.value = status;
//     // 如果是活动页刷新页面
//     if (_activityId > 0) {
//         refreshPageInfo()
//     }
// };
// 关闭弹层
const closeOrderFixed = () => {
    // 发送结果到客户端
    const data = { code: 10018, data: { orderId: '', payType: '' }, message: '支付取消' };
    $jsbridge.sendPayResult(data);
    console.log('调用jsbridge关闭');
    $jsbridge.close();
}

/**
 * 生单提交
 */
const onSubmit = async () => {
    appOrder.value.kitId = curKitId.value;
    appOrder.value.productId = curProduct.value.id;
    // projectId需要用币种列表获取的projectId
    appOrder.value.projectId = projectIdFromCurrency.value;
    // 币种由用户选择
    appOrder.value.currency = showCurrencyActive.value.code;
    appOrder.value.packageName = packageName.value;
    appOrder.value.appOrderId = appOrderId.value;
    // 没有选择支付方式
    if (!curKitId.value) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.not-selected-payment-type-message-error'),
            class: 'c_message_big',
            duration,
        });
        return false
    }
    // 请求接口
    orderLoading.value = true;
    // 判断是 adyen支付 提交弹窗logo
    if (payMethodVal.value == 60002) {
        submitData.value = {
            showLoading: true,
        }
    }
    // 6秒后关闭loading,避免接口请求时间过长
    setTimeout(() => {
        orderLoading.value = false;
    }, 6000);
    // 请求生单接口
    let result: any = await postWebviewSubmit(appOrder.value);
    orderLoading.value = false;

    if (result && result.code == 200 || result.code == 0) {
        let { data } = result;
        appOrder.value.orderId = data.orderId;

        // 设置订单重新跳转的 路径
        const cookieOrderPath = useCookie(`orderpath_${data.orderId}`, { maxAge: 60 * 60 * 24 });
        cookieOrderPath.value = route.path;  // /zh-Hant/pay/17
        
        // 使用支付组件 处理支付跳转
        paymentMethodsRef.value.handlePaymentRedirect(data, payMethodVal.value)
        // switch (payMethodVal.value) {
        //     // 60001: mycard   60003 Paypal  60007 EVONET 60008 街口支付
        //     case 60001:
        //     case 60003:
        //     case 60007:
        //     case 60008:
        //         $jsbridge.switchBarChange(1)
        //         window.location.href = data.transactionUrl;
        //         break;
        //     case 60002:  // adyen
        //         submitData.value = result.data;
        //         break;
        //     default:
        //         $jsbridge.switchBarChange(1)
        //         window.location.href = data.transactionUrl
        //         break;
        // }
        // 请求 显示源生菜单
        if(payMethodVal.value != 60002){
            $jsbridge.switchBarChange(1);
        }
        // 埋点: 生单接口请求结果 0:成功 1:失败
        YKTrack.track('server', {
            params: {
                id: '806',
                value1: 0,  // 请求结果
                value2: data.orderId, // 订单id
                value3: payMethodVal.value, // 支付方式
                value4: appOrder.value.productId, // 商品id
                value5: appOrder.value.projectId, // 游戏id
                value6: appOrder.value.serverId, // 区服id
                value7: appOrder.value.roleId, // 角色id
                value8: appOrder.value.schemeId, // 方案id
                value9: appOrder.value.uid,  // 用户id
                value10: locale.value, // 语言
                value11: appOrder.value.appOrderId, // app订单id
            }
        })
    } else {  //失败的情况
        message.error({
            content: () => result.message,
            class: 'c_message_big',
            duration,
        });
        if (payMethodVal.value == 60002) {
            submitData.value = {
                status: "fail",
            }
        }
        // 埋点: 生单接口请求结果 0:成功 1:失败
        YKTrack.track('server', {
            params: {
                id: '806',
                value1: 1,
                value2: '', // 订单id
                value3: payMethodVal.value, // 支付方式
                value4: appOrder.value.productId, // 商品id
                value5: appOrder.value.projectId, // 游戏id
                value6: appOrder.value.serverId, // 区服id
                value7: appOrder.value.roleId, // 角色id
                value8: appOrder.value.schemeId, // 方案id
                value9: appOrder.value.uid,  // 用户id
                value10: locale.value, // 语言
            }
        })
    }
}

onMounted(async () => {
    // 隐藏header
    $jsbridge.switchBarChange(4);
    localStorage.setItem('WEBVIEW_QUERY', JSON.stringify(route.query));
    try {
        // currencyList webview获取充值币种列表
        const webviewCurrency: any = await getWebviewCurrency(projectId.value as string, channelId.value as string, language.value);
        if (webviewCurrency && webviewCurrency.code == 200 || webviewCurrency.code == 0) {
            currencyList.value = webviewCurrency.data.currency || [];
            currencyActive.value = currencyList.value[0]?.code || '';
            showCurrencyActive.value = currencyList.value[0] || '';
            projectIdFromCurrency.value = webviewCurrency.data.projectId || '';
            // 结果页无法获取game_id,通过缓存获取
            localStorage.setItem('WEBVIEW_GAME_ID', webviewCurrency.data.projectId);
        } else {
            hideBtn.value = true;
            message.error({
                content: () => webviewCurrency.message,
                class: 'c_message_big',
                duration,
            });
        }
        // channelList 获取webview获取充值渠道列表
        // productPrice 获取webview获取商品价格
        const [channelList, productPrice]: any = await Promise.all([
            getWebviewChannelList(projectIdFromCurrency.value, currencyActive.value, language.value),
            getWebviewProductPrice(projectIdFromCurrency.value, productCode.value as string, currencyActive.value, language.value)
        ])
        webviewLoading.value = false;
        if (channelList && channelList.code == 200 || channelList.code == 0) {
            let { data } = channelList;
            // 支付方式列表
            paymentMethods.value = data?.paymentMethods || [];
            // 方案id
            appOrder.value.schemeId = data?.schemeId || ''
        } else {
            hideBtn.value = true;
            message.error({
                content: () => channelList.message,
                class: 'c_message_big',
                duration,
            });
        }
        if (productPrice && productPrice.code == 200 || productPrice.code == 0) {
            let { data } = productPrice;
            // 获取商品价格与id
            curProduct.value = data || {};
        } else {
            hideBtn.value = true;
            message.error({
                content: () => productPrice.message,
                class: 'c_message_big',
                duration,
            });
        }
    } catch (error) {
        hideBtn.value = true;
        webviewLoading.value = false;
        console.log('error', error);
    } finally {
        if (uid.value) {
            YKTrack.setUserId(uid.value);
        }
        if (roleId.value) {
            YKTrack.setRoleId(roleId.value);
        }
    }
    // 加载成功 回调
    $jsbridge.commonCallHandler("loadPaySuccess");
    // 修改 HTML 和BODY 样式，使得页面变透明
    document.body.style.backgroundColor = "transparent";
    document.documentElement.style.backgroundColor = "transparent"; // 修改 <html> 样式
})

</script>

<style lang="scss" scoped>
@import url(~/assets/styles/pay.scss);
.web_view {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: transparent;
}
</style>