
.account_hint {
    display: flex;
    min-height: 170px;
    width: 100%;

    .ac_bell_wrap {
        width: 50px;
        height: 100%;
        background: #ffc716;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .ac_hint_right {
        flex: 1;
        padding: 24px 40px;
        display: flex;
        justify-content: center;
        flex-direction: column;

        .h4 {
            color: #111;
            font-size: 20px;
            line-height: 32px;
        }

        .txt {
            font-size: 14px;
            color: #5f5f5f;
            margin: 8px 0 14px;
            line-height: 24px;
        }

        .ac_hint_btn_wrap {
            display: flex;

            .ac_hint_btn {
                line-height: 36px;
                width: 160px;
                color: #111;
                text-align: center;
                font-size: 14px;
                border-radius: 10px;
                border: 1px solid #d9d9d9;
                margin-right: 20px;
                cursor: pointer;
                transition: all 0.5s ease 0s;

                &.active {
                    background: rgba(255, 199, 22, 0.2);
                    border-color: transparent;

                    &:hover {
                        background: #ffe48b;
                        border-color: transparent;
                    }
                }

                &:hover {
                    border: 1px solid #111;
                }
            }
        }
    }
}

.ac_common_box {
    .line {
        padding: 0 40px;

        &:nth-child(odd) {
            background: #f4f6fc;
        }

        &:last-child {
            border-bottom: none;
        }
    }

    .line_item {
        border-bottom: 1px solid #ececf0;
        line-height: 24px;
        padding: 18px 0;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        align-items: center;

        .row {
            display: flex;
            flex: 1;
            min-width: 0; // 添加这一行
        }

        .row_1 {
            color: var(--base_color);
            width: 140px;
            flex-shrink: 0;
        }

        .row_2 {
            color: #111;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            min-width: 0; // 添加这一行
        }

        .row_right {
            flex-shrink: 0;
            display: flex;
            align-items: center;

            .ac_common_btn.disabled {
                cursor: not-allowed;
                filter: grayscale(1);
            }
            .success_txt {
                color: #14ac71;
                font-size: 14px;
                margin: 0 15px 0 5px;
            }
            .span_edit_btn {
                cursor: pointer;
                display: inline-block;
            }
        }
    }
}

.ac_info_box {
    width: 100%;
}

.ac_security {
    width: 48%;
    display: flex;
    flex-direction: column;
}

.ac_person {
    width: 48%;
    margin-right: 4%;

    .line_item {
        .row_1 {
            width: 100px;
        }

        .row_2 {
            color: #111;
            margin-right: auto;
        }

        .row_right {
            color: var(--base_color);
        }
    }
}

.progress_wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    padding: 20px 0;

    .progress_left {
        width: 150px;
        margin-right: 20px;
    }

    .progress_right {
        font-size: 14px;

        .item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &.cursor {
                cursor: pointer;
                &:hover {
                    font-weight: bold;
                }
            }
            &:last-child {
                margin-bottom: 0;
            }

            .txt {
                color: var(--base_color);
                margin-left: 8px;
            }
        }
    }
}




@media screen and (max-width: 1368px) {
}

@media screen and (max-width: 1280px) {
    .ac_security,
    .ac_person {
        width: 100%;
        margin-right: 0;
    }

    .ac_person {
        .line_item {
            .row_1 {
                width: 140px;
            }
        }
    }
}

@media screen and (max-width: 980px) {
    .ac_info_box {
        order: 2;
    }

    .ac_security {
        order: 1;
    }

    .ac_person {
        order: 3;
    }
}

@media screen and (max-width: 768px) {
    .ac_common_box {
        .line {
            padding: 0 20px;
        }
    }
}

@media screen and (max-width: 640px) {
    .ac_common_box {
        .line_item {
            font-size: 12px;
            padding: 10px 0;
            .row {
                flex-direction: column;
                line-height: 24px;
                padding: 6px 0;
            }

            .row_1 {
                width: auto;
            }
            .row_2 {
                width: 100%;
            }
        }
    }

    .account_hint {
        flex-direction: column;

        .ac_bell_wrap {
            width: 100%;
            height: 30px;
            .icon_bell {
                width: 18px;
                height: 20px;
            }
        }

        .ac_hint_right {
            padding: 10px 20px 0;

            .h4 {
                font-size: 16px;
                line-height: 24px;
            }

            .txt {
                margin: 4px auto;
            }

            .ac_hint_btn_wrap {
                flex-direction: column;

                .ac_hint_btn {
                    width: 100%;
                    max-width: 300px;
                    margin: 0 auto 12px;
                }
            }
        }
    }

    .progress_wrap {
        .progress_left {
            transform: scale(0.8);
            margin-right: 0;
        }

        .progress_right {
            font-size: 12px;
        }
    }
}