<template>
    <div class="account_wrap" ref="container">
        <div class="bg_circle bg_circle1"></div>
        <div class="bg_circle bg_circle2"></div>
        <div class="bg_circle bg_circle3"></div>
        <div class="cursor_circle" ref="cursorCircle"></div>
        <div class="login_wrap">
            <account-login-module
                v-if="$name === 'login'"
                :needRedirect="true"
            ></account-login-module>
            <account-register-module
                v-if="$name === 'register'"
                :needRedirect="true"
            ></account-register-module>
            <account-reset-password-module
                v-if="$name === 'resetpassword'"
                :needRedirect="true"
            ></account-reset-password-module>
        </div>
    </div>
</template>
<script lang="ts" setup>
// 登录态管理
import { useAuthStore } from "~/stores/auth";
const authStore = useAuthStore();
const route = useRoute();
const { locale } = useI18n();
const $name = route.params.name;

const cursorCircle = ref<HTMLElement | null>(null);
const container = ref<HTMLElement | null>(null);

const handleMouseMove = (e: MouseEvent) => {
    if (!container.value || !cursorCircle.value) return;

    const rect = container.value.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // 更新光晕的位置
    cursorCircle.value.style.left = `${mouseX}px`;
    cursorCircle.value.style.top = `${mouseY}px`;
};

const validNames = ["login", "register", "resetpassword"]; // 允许的 name 参数

onMounted(() => {
    if (!validNames.includes(route.params.name)) {
        navigateTo(`/${route.params.lang}/account/login`);
    } else if ($name === "login" && authStore.isLogin) {
        navigateTo(`/${locale.value}/account/profile`);
    } else {
        if (container.value) {
            container.value.addEventListener("mousemove", handleMouseMove);
        }
    }
});

onUnmounted(() => {
    if (container.value) {
        container.value.removeEventListener("mousemove", handleMouseMove);
    }
});
</script>
<style lang="scss" scoped>
.account_wrap {
    min-height: 900px;
    overflow: hidden;
    position: relative;
    padding: 0 20px;
}

.bg_circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.5;
    z-index: 1;
    top: 50%;
    left: 50%;
}

.bg_circle1 {
    width: 500px;
    height: 500px;
    background: radial-gradient(
        circle,
        rgb(170, 235, 255, 1) 0%,
        rgb(246, 246, 246, 0.1) 60%
    );
    transform: translate(-100%, -70%);
}

.bg_circle2 {
    width: 600px;
    height: 600px;
    background: radial-gradient(
        circle,
        rgb(242, 191, 255, 0.5) 0%,
        rgb(246, 246, 246, 0.1) 60%
    );
    transform: translate(-0%, -10%);
}

.bg_circle3 {
    width: 800px;
    height: 800px;
    background: radial-gradient(
        circle,
        rgb(70, 109, 246, 0.5) 0%,
        rgb(246, 246, 246, 0.1) 60%
    );
    transform: translate(-10%, -70%);
}

.cursor_circle {
    width: 1000px;
    height: 1000px;
    background: radial-gradient(
        circle,
        rgb(70, 109, 246, 0.3) 0%,
        rgb(246, 246, 246, 0.1) 50%
    );
    position: absolute;
    border-radius: 50%;
    opacity: 0.5;
    z-index: 2;
    pointer-events: none;
    transform: translate(-50%, -50%);
    transition: transform 0.1s ease-out;
}

.login_wrap {
    position: relative;
    z-index: 9;
    margin: 100px auto;
    max-width: 530px;
    width: 100%;
    background: #fff;
    padding:40px 30px 30px;
    border-radius: 20px;
}

@media screen and (max-width: 768px) {
    .login_wrap {
        padding: 20px;
    }
}
</style>
