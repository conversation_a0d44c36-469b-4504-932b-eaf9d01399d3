// import antdEnUS from 'ant-design-vue/es/locale-provider/ja_JP'
// import momentEU from 'moment/locale/ja'
import global from './ja/global'

import menu from './ja/menu'
import setting from './ja/setting'
import user from './ja/user'
import account from './ja/account'
import pages from './ja/pages'

const components = {
    // antLocale: antdEnUS,
    momentName: 'ja',
    // momentLocale: momentEU
}

export default {
    message: '-',
    'login.hint.txt': "Playbest公式アカウント以外のアドレスではログインできません。",
    'login.hint.txt2': "他のアカウント連携（Google、Facebook、AppleID）を用いてログインしたい場合は、先にゲーム内にてアカウント連携を行ってください。",
    'login.error.email': 'メールアドレスあるいはパスワードに誤りがあります。',
    'login.email.name': 'Playbest公式アカウントでログイン',
    'layouts.usermenu.dialog.title': 'Message',
    'layouts.usermenu.dialog.content': 'Are you sure you would like to logout?',
    'layouts.userLayout.title': 'Ant Design is the most influential web design specification in Xihu district',
    'pay.credit.card': 'クレジットカード',
    'pay.role.comfirm': '確認',
    'pay.how.get.roleId': 'キャラクターIDの確認方法?',
    'pay.comfirm.account': '注文内容を確認',
    'pay.comfirm.account.roleId': 'チャージを希望されるキャラクターIDもしくは公式アカウントをログインしてください',
    'page.fetch.fail': 'ページが見つかりませんでした。{ XX }ホーム{ YY }に戻る',
    'login.current.name': '最終ログイン時間',
    'login.role.name': 'キャラクター',
    'footer.policy.cookies': 'Cookieポリシー',
    'footer.policy.refund': '払い戻しポリシー',
    'footer.policy.website': '利用規約',
    'footer.policy.privacy': 'プライバシーポリシー',
    'footer.policy.website2':'ゲーム利用規約',
    'footer.policy.privacy2':'プライバシーポリシー',
    'ux.txt.agree': '全て受入',
    'api.login.lose': 'ログイン状態が無効となりました。再度ログインしてください。',
    'api.login.request.fail': 'データの取得に失敗しました。しばらく経ってから再度お試しください。',
    'common.policy.cookies': 'Cookiesを使用して、より良いアクセス体験を与えております。一部のCookiesは当サイトの運営にとって非常に重要なもので、一部のはユーザーがウェブサイトをどのように使用しているのかを理解しており、改善できるようにするのに役立つものです。「全て受入」をクリックすることで、Cookiesポリシーとプライバシーポリシーにご同意ください。',
    'common.comming.soon':'お楽しみに',
    'pay.payment.mycard.select':'Mycardの支払い方式を選択し、商品をお選びください。',
    'pay.payment.mycard.hint':'Mycardの商品を選択してください',
    'pay.order.not.paid':'まだ決済は完了していません',
    'pay.order.comfirming.loading':'注文確認中',
    'pay.order.comfirming.txt':'ご注文を確認中です、しばらくすると注文リストに状況が表示されます。 チャージに問題があった場合は「カスタマーサービス」にお問い合わせください。',
    'pay.role.login.hint':'現在、このゲームではキャラクターIDを使用したチャージのみ行えます。',
    'cookies.policy.agree.all':'すべて同意する',
    'cookies.policy.disagree.all':'すべて拒否する',
    'cookies.policy.manage':'同意の優先度設定を管理する',
    'cookies.policy.confirm.my':'選択内容を確認',
    'cookies.policy.function.txt':'訪問者数と通信環境などが統計できるようになり、弊社サイトのパフォーマンスを改善する際に活用いたします。Cookieにより、ユーザーの選択や好みに応じてコンテンツをパーソナライズし、お客様の名前を表示したり、好みを記憶することができます。',
    'cookies.policy.function.title':'機能性Cookie',
    'cookies.policy.analysis.title':'アナリティクス用Cookie',
    'cookies.policy.analysis.txt':'訪問者数と通信環境などが統計できるようになり、弊社サイトのパフォーマンスを改善する際に活用いたします。人気のページやあまり見られていないページを統計したり、訪問者がサイト内をどう移動するかを計測し、導線を整えたりする目的で使用します。',
    'cookies.policy.base.title':'必要なCookie',
    'cookies.policy.base.txt':'これらの Cookieは、本ウェブサイトが正常に機能するために必要なものであり、弊社のシステムからは無効化することはできません。',
    'cookies.policy.base.status':'常にON',
    'cookies.policy.center':'クッキー管理センター',
    'common.get.more':"もっと見る",
    'pay.order.status.alipay.hint':'「Alipay」のQRコードで支払う',
    'pay.maintenance.hint': 'ページは現在メンテナンス中です。支払い機能は一時的にご利用いただけません。',
    'pay.apple.only.hint': 'この支払い方法はAppleデバイスでのみご利用いただけます。他の支払い方法をお選びください。',
    'pay.order.status.processing': '注文を処理しています。少々お待ちください',
    'login.search.noData': '検索結果が見つかりません',
    'login.choose.games':'ゲームを選ぶ',
    ...components,
    ...global,
    ...menu,
    ...setting,
    ...user,
    ...account,
    ...pages
}
