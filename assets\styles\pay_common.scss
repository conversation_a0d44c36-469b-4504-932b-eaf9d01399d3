.smallFont {
    font-size: 11px;
}

.pay_method {
    .ant-radio-inner {
        border-width: 2px;
        border-color: #3A58A9 !important;
    }

    .ant-radio-checked .ant-radio-inner::after {
        background-color: #3A58A9 !important;
    }
}

.change_select {
    .ant-input-affix-wrapper {
        width: auto;
        border: none !important;
        box-shadow: none !important;

        &:hover {
            border: none !important;
            box-shadow: none !important;
        }
    }

    .ant-select-selection-item {
        display: block !important;
    }
}


.pay_server_select,
.pay_role_select {
    font-size: 16px;
    flex: 1;
    color: rgba(0, 0, 0, 0.7);

    .ant-select-selector {
        font-size: 16px;
        border: none !important;
        box-shadow: none !important;

        &:hover,
        input:focus {
            border: none !important;
            box-shadow: none !important;
        }
    }

    .ant-input {
        font-size: 16px;
    }
}

.pay_role_select {
    min-width: 240px !important;
    width: auto !important;
}

.roleIdGuideWrap {
    max-width: 530px;
    width: 100%;
    border-radius: 16px;
    background: #FFF;

    .title {
        line-height: 60px;
        color: #111;
        text-align: center;
        font-size: 20px;
        font-weight: 500;
        border-bottom: 1px solid #ECECF0;
    }

    .roleIdGuideContent {
        font-size: 14px;
        padding: 20px 0;

        img {
            width: 100%;
        }

        p {
            padding: 0 0 10px 0;
        }
    }
}

.roleIdGuideWrap_pop {
    .ant-modal-content {
        padding: 0 20px !important;
    }
}

.pay_desc_wrap_html {
    em {
        font-style: italic;
    }

    strong {
        font-weight: bold;
    }
}

.pay_guide_pop_wrap {
    max-width: 600px;
    width: 50%!important;
    background: none;

    .ant-modal-content {
        background: none;
        box-shadow: none;
    }

    .ant-modal-body {
        padding: 0;
        background: none;
    }

    .pay_guide_pop_main {
        img {
            width: 100%;
        }
        .mb_img {
            display: none;
        }
    }

    .pay_guide_pop_close {
        cursor:pointer;
        position: absolute;
        bottom: -80px;
        left: 50%;
        width: 40px;
        height: 40px;
        transform: translate(-50%, 0);
        z-index: 999;
        background: url(~/assets/images/common/activity_pop_close.png) top center/100% 100% no-repeat;
    }
}

@media screen and (max-width: 1280px) {

    .pay_server_select,
    .pay_role_select {
        font-size: 14px;

        .ant-select-selector {
            font-size: 14px;
        }

        .ant-input {
            font-size: 14px;
        }
    }
}

.order_status_pop {
    .ant-modal-content {
        padding: 0 !important;
    }
}

@media screen and (max-width: 640px) {
    .pay_guide_pop_wrap {
        width: 80%!important;
        .pay_guide_pop_main {
            .mb_img {
                display: block;
            }
            .pc_img {
                display: none;
            }
        }
    }
}