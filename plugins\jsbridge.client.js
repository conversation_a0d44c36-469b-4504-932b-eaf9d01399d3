let ua, isAndroid, isIOS;

if (process.client) {
  ua = navigator.userAgent;
  // Android终端
  isAndroid = ua.indexOf("Android") > -1 || ua.indexOf("Adr") > -1;
  // IOS 终端
  isIOS = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
}
/**
 * Android  与安卓交互时：
 *      1、不调用这个函数安卓无法调用 H5 注册的事件函数；
 *      2、但是 H5 可以正常调用安卓注册的事件函数；
 *      3、还必须在 setupWebViewJavascriptBridge 中执行 bridge.init 方法，否则：
 *          ①、安卓依然无法调用 H5 注册的事件函数
 *          ①、H5 正常调用安卓事件函数后的回调函数无法正常执行
 *
 * @param {*} callback
 */
const andoirFunction = (callback) => {
  if (window.WebViewJavascriptBridge) {
    callback(window.WebViewJavascriptBridge);
  } else {
    document.addEventListener(
      "WebViewJavascriptBridgeReady",
      function () {
        callback(window.WebViewJavascriptBridge);
      },
      false
    );
  }
};
/**
 * IOS 与 IOS 交互时，使用这个函数即可，别的操作都不需要执行
 * @param {*} callback
 */
const iosFuntion = (callback) => {
  if (window.WebViewJavascriptBridge) {
    return callback(window.WebViewJavascriptBridge);
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback);
  }
  window.WVJBCallbacks = [callback];
  var WVJBIframe = document.createElement("iframe");
  WVJBIframe.style.display = "none";
  WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
  document.documentElement.appendChild(WVJBIframe);
  setTimeout(function () {
    document.documentElement.removeChild(WVJBIframe);
  }, 0);
};

/**
 * 注册 setupWebViewJavascriptBridge 方法
 *  之所以不将上面两个方法融合成一个方法，是因为放在一起，那么就只有 iosFuntion 中相关的方法体生效
 */
window.setupWebViewJavascriptBridge = isIOS ? iosFuntion : andoirFunction;

/**
 * 这里如果不做判断是不是安卓，而是直接就执行下面的方法，就会导致
 *      1、IOS 无法调用 H5 这边注册的事件函数
 *      2、H5 可以正常调用 IOS 这边的事件函数，并且 H5 的回调函数可以正常执行
 */
if (isAndroid) {
  /**
   * 与安卓交互时，不调用这个函数会导致：
   *      1、H5 可以正常调用 安卓这边的事件函数，但是无法再调用到 H5 的回调函数
   *
   * 前提 setupWebViewJavascriptBridge 这个函数使用的是 andoirFunction 这个，否则还是会导致上面 1 的现象出现
   */
  window.setupWebViewJavascriptBridge(function (bridge) {
    // 注册 H5 界面的默认接收函数（与安卓交互时，不注册这个事件无法接收回调函数）
    bridge.init(function (msg, responseCallback) {
      // alert("这里是返回值", msg);
      // responseCallback("JS 返回给原生的消息内容");
    });
  });
}

export default defineNuxtPlugin((nuxtApp) => {
  const jsbridge = {
    close() {
      // 关闭当前 webview
      window.setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler("close");
      });
    },
    back() {
      // 返回上一页
      window.setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler("back");
      });
    },
    sendPayResult(data) {
      // 发送结果到客户端
      window.setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler("sendPayResult", data, () => {
          alert("JS 传递给原生的消息", "发送成功");
        });
      });
    },
    switchBarChange(num) {
      // 显示带向前向后的工具导航栏
      /**
       * num:
       * 1 = toolBar 带向前向后的工具导航
       * 2 = titleBar 文字导航
       * 3 = transparent 只有关闭
       * 4 = none 隐藏
       */
      window.setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler("switchBarType", num, () => {
          alert("JS 传递给原生的消息", "发送成功");
        });
      });
    },
    /**
     * 通用的调用方法，用于与原生端进行交互
     * @param {string} methodName - 要调用的方法名
     * @param {Object} data - 要传递给原生端的数据
     * @returns {void} - 无返回值
     */
    commonCallHandler(methodName, data, responseCallback) {
      // 通用的调用方法
      window.setupWebViewJavascriptBridge((bridge) => {
        bridge.callHandler(methodName, data, (callback) => {
          if (responseCallback) {
            responseCallback(callback);
          }
        });
      })
    },
    /**
     * 常用的通讯方式 比commonCallHandler更高级
     * @param {Object} data - 要传递给原生端的数据
     */
    gsInvokeNative(data, responseCallback) {
      jsbridge.commonCallHandler('gsInvokeNative', data, responseCallback)
    },
    /**
     * 登出
     * 登出 游戏
     */
    logout() {
      jsbridge.commonCallHandler('logout')
    },
  };

  // 将 jsbridge 注入到应用中
  nuxtApp.provide("jsbridge", jsbridge);
});
