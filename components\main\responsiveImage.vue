<template>
    <div class="responsive-image-wrapper" :class="props.wrapperClass">
        <div class="placeholder" :style="styleObj"></div>
        <img :bind="$attrs" :src="props.src" :alt="props.alt" class="responsive-image"  />
    </div>
</template>

<script lang="ts">

export function useAssets() {

    let useAssetsImage;
    // 直接通过NUXT_VITE_NODE_OPTIONS获取base配置，从而直接替换静态资源地址，一次解决无需遍历
    try {
        const basePath = computed(() => {
            try {

                if (process.env.VITE_NODE_ENV == 'production') {
                    const nuxtConfig = __NUXT__.config;
                    const nuxtConfigAssetsDir = `${nuxtConfig.app.baseURL}${nuxtConfig.app.buildAssetsDir}`
                    return nuxtConfigAssetsDir;
                }
                const base = JSON.parse(process.env.NUXT_VITE_NODE_OPTIONS as string||'{}')?.base || __buildAssetsURL?.();
                return base;
            } catch (e) {
                console.log("🚀 ~ basePath ~ e:", e)
                // 这里报错后降级通过上边的方式获取
                throw "未找到静态资源地址"
            }
        });
        useAssetsImage = (src: string): string | undefined => {

            return src.replace('~', basePath.value);
        }
    } catch (e) {

        // 这种方式要获取全部的静态资源文件，比较耗时
        const images = computed<Record<string, { default: string }>>(() => import.meta.glob('~/assets/**/*.(jpg|png|jpeg|svg)', { eager: true }));

        useAssetsImage = (src: string): string | undefined => {
            for (const path in images.value) {
                const image = images.value[path].default;

                const originSrc = src.replace('~/assets/', '');

                if (path.endsWith(`assets/${originSrc}`)) {
                    return image
                }
            }
            return undefined
        }
    }

    return {
        useAssetsImage,
    }
}

</script>

<script lang="ts" setup>
// const { useAssetsImage } = useAssets()

const props = defineProps(['src', 'alt', "style", "scale", 'wrapperClass', 'class']);


const styleObj = computed(() => {

    return { paddingTop: props.scale }

})

</script>

<style scoped lang="scss">
.responsive-image-wrapper {
    position: relative;
    width: 100%;
}

.placeholder {
    width: 100%;
}

.responsive-image {
    position: absolute;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    border-radius: 12px;

}
</style>