/**
 * 从Nuxt应用程序中获取并使用$axios实例。
 * @param options 请求配置选项。
 * @returns 返回封装后的axios请求。
 */

import URLS from "~/api/uri";
// 服务器获取请求
import { useAsyncDataCustom } from "~/utils/useAsyncDataCustom"; // 调整导入路径
// 服务器获取
import { useFetchCustom } from "~/utils/useFetchCustom"; // 调整导入路径
import { $axios } from "~/utils/axiosCustiom";

const PAYMENT_SITE_ID = process.env.VITE_CMS_PAYMENT_SITE_ID;

/**
 * 异步获取项目列表数据。
 * @returns 返回一个Promise，解析为项目列表数据。
 */
/**
 * @function 拉取首页banner列表
 * @method post
 * @returns
 */
export const getMainBanners = (locale: string = "") => {
    const url = URLS.Main.getMainBanners;
    const identifier = 'main_page_banner';

    return useAsyncDataCustom(identifier, {
        method: "POST",
        data: {
            'site_id': PAYMENT_SITE_ID,
            locale,
            identifier
        },
        url,
    });
};

/**
 * @function 拉取游戏列表
 * @method post
 * @returns
 */
export const getGameList = (locale: string = "") => {
    const url = URLS.Main.getMainBanners;
    const identifier = 'games_list';

    return useAsyncDataCustom(identifier, {
        method: "POST",
        data: {
            'site_id': PAYMENT_SITE_ID,
            locale,
            identifier
        },
        url,
    });
};

/**
 * @function 获取联系信息
 * @method post
 * @returns
 */
export const getConcatUsChannelList = (locale: string = "") => {
    const url = URLS.Main.getMainBanners;
    const identifier = 'concat_us_channels';

    return useAsyncDataCustom(identifier, {
        method: "POST",
        data: {
            'site_id': PAYMENT_SITE_ID,
            locale,
            identifier
        },
        url,
    });
};

/**
 * @function 获取关于我们信息
 * @method post
 * @returns
 */
export const getAboutUsDescList = (locale: string = "") => {
    const url = URLS.Main.getMainBanners;
    const identifier = 'about_us_desc';

    return useAsyncDataCustom(identifier, {
        method: "POST",
        data: {
            'site_id': PAYMENT_SITE_ID,
            locale,
            identifier
        },
        url,
    });
};