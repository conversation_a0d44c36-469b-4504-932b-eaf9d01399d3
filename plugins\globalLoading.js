import NProgress from "nprogress";
// import "nprogress/nprogress.css";
import "~/assets/styles/global.scss";
export default defineNuxtPlugin((nuxtApp) => {
  // 判断是否是webview
  const isWebview = useRoute().path.indexOf("/webview") > -1;
  // 控制加载
  const commonLoading = useState("commonLoading", () =>
    isWebview ? false : true
  );
  nuxtApp.hook("page:finish", () => {
    //console.log("page:finish")
    commonLoading.value = false;
    setTimeout(() => {
      NProgress.done();
    }, 500);
  });
  nuxtApp.hook("page:start", () => {
    // console.log("page:start");
    commonLoading.value = true;
    NProgress.start();
  });
  nuxtApp.hook("page:error", () => {
    console.log("page:error");
  });
  nuxtApp.hook("app:mounted", (e) => {
    console.log("app:mounted");
  });
});
