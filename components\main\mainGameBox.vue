<template>
    <div class="game-box-wrapper" :class="{ [size]: size, alignCenter: props.data?.length < 3 }">
        <div v-for="game of props.data" class="game-box" @click.stop='jumpToSite(game.link)'>
            <div class="game-logo">
                <img :src="game.logo" class="game-logo-icon" />
            </div>
            <div class="game-figure">
                <img :src="game.figure" class="game-figure-icon" />
            </div>
            <div class="game-painting">
                <img :src="game.painting" class="game-painting-icon" />
            </div>
            <div class="game-detail">
                <div class="game-name">{{ game.name }}</div>
                <div class="game-desc">{{ game.desc }}</div>
                <div class="game-channels">
                    <div class="game-channel" v-for="channel of game.channels" :key="channel.title"
                        @click.stop="jumpToSite(channel.link)">
                        <img :src="channel.icon" class="game-channel-icon">
                        <span class="game-channel-title">{{ channel.title }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>

const props = defineProps({ 'data': Object, size: { type: String, default: 'small' } });

function jumpToSite(url: string) {
    if (url) {
        // window.location.href = url;
        window.open(url, '_blank')
    }
}

</script>

<style lang="scss" scoped>
.game-box-wrapper {

    --offset-x: 2.5vw;
    --space-gap: 20px;
    --gap-w: calc(var(--space-gap) + var(--offset-x));
    --col-count: 3;
    --wrapper-width: 1444px;

    &.alignCenter {
        justify-content: center;
    }

    &.small {
        --offset-x: 2.5vw;
        --space-gap: 20px;
        --gap-w: calc(var(--space-gap) + var(--offset-x));
        --col-count: 3;
        --item-max: 426px;
        --wrapper-width: 1444px;


        .game-channels {
            bottom: 2vw;
        }
    }

    &.large {
        --offset-x: 2.9vw;
        --space-gap: 50px;
        --gap-w: calc((var(--space-gap) + var(--offset-x)));
        --col-count: 2;
        --item-max: 482px;
        --wrapper-width: 1200px;

        .game-detail {
            // top: 7.84vw;
            left: 1.92vw;
        }

        .game-painting {
            // right: var(--space-gap);
        }
    }

    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: var(--gap-w);
    row-gap: var(--space-gap);
    width: 100%;
    max-width: var(--wrapper-width);
    box-sizing: border-box;
    padding: 0 var(--gap-w);
    margin: 0 auto;

    .game-box {
        position: relative;
        cursor: pointer;
        width: calc((100% - var(--gap-w)*(var(--col-count) - 1))/var(--col-count));
        max-width: var(--item-max);
        overflow: visible;

        &::after {
            content: '';
            display: block;
            position: absolute;
            z-index: 1;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            border-radius: inherit;
            border-radius: 20px;
        }
    }

    .game-logo {
        position: absolute;
        z-index: 5;
        top: 6.8%;
        left: 6.4%;
        width: 58.2%;
        padding-top: 22.7%;
        transform-origin: 0 0;
        transition: transform 0.5s ease;

        .game-logo-icon {
            position: absolute;
            top: 0;
            left: 0;
        }
    }

    .game-figure {
        width: 100%;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
    }

    .game-painting {
        position: absolute;
        z-index: 10;
        bottom: 0;
        // right: calc(-1*var(--space-gap));
        right: -10%;
        width: 82.6%;
        padding-top: 82.6%;
        height: 0;
        transform-origin: 100% 100%;
        transition: transform 0.5s ease;

        .game-painting-icon {
            position: absolute;
            top: 0;
            left: 0;
        }

    }

    .game-detail {
        position: absolute;
        z-index: 20;
        top: 24%;
        bottom: 0;
        left: 1.72vw;
        color: #fff;
        opacity: 0;
        pointer-events: none;
        transition: all 0.5s;

        .game-name {
            font-size: 24px;
            height: 60px;
            // max-width: 164px;
            min-width: 140px;
            margin-bottom: 8px;
            padding-bottom: 2px;
            width: 80%;
            font-weight: 700;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: normal;
        }

        .game-desc {
            font-size: 14px;
            max-width: 10.6vw;
            min-width: 140px;
            width: 10.6vw;
            height: 72px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
        }

    }

    .game-channels {
        position: absolute;
        bottom: 3.2vw;
        margin-top: 20px;
        display: flex;
        flex-wrap: nowrap;
        overflow: hidden;
    }

    .game-channel {
        width: 45px;
        height: 45px;
        margin-right: 11px;
        border-radius: 50%;
        background: rgba(232, 236, 250, 1);
        padding: 5px;
        border: none;

        .game-channel-icon {
            border: none;
            width: 100%;
            height: 100%;
        }
    }

    .game-box:hover {
        .game-logo {
            transform: scale(0.638);
        }

        .game-painting {
            transform: scale(0.812) translate(-10px);
        }

        .game-detail {
            opacity: 1;
            pointer-events: all;
        }

        &::after {
            opacity: 1;
            background-color: rgba(0, 0, 0, .2);
        }
    }

    .game-logo-icon,
    .game-figure-icon,
    .game-painting-icon {
        width: 100%;
        height: 100%;
    }
}


@media screen and (max-width:820px) {

    .game-box-wrapper {

        &.small {
            --offset-x: 2.9vw;
            --space-gap: 20px;
            --gap-w: calc(var(--space-gap) + var(--offset-x));
            --col-count: 3;
            --item-max: 426px;
            --wrapper-width: 1444px;


            .game-channels {
                bottom: 40px;
            }
        }

        &.large {
            --space-gap: 20px;
        }

        .game-box::after {
            opacity: 1;
            background-color: rgba(0, 0, 0, .3);
        }


        .game-logo {
            transform: scale(0.638);
        }

        .game-painting {
            transform: scale(0.812) translate(-10px);
        }


        .game-box {
            width: 100%;
            max-width: 100%;
        }


        &.large .game-detail,
        .game-detail {
            top: 25%;
            left: 20px;
            opacity: 1;
            pointer-events: all;

            .game-name {
                font-size: 18px;
                height: 44px;
            }

            .game-desc {
                font-size: 14px;
            }
        }

        .game-channels {
            bottom: 80px;
        }

        .game-channel {
            width: 34px;
            height: 34px;
        }
    }
}

@media screen and (max-width:480px) {


    .game-box-wrapper {

        .game-desc {
            font-size: 14px;
        }

        .game-channels {
            bottom: 40px;
        }


        .game-channel {
            font-size: 0;
            // width: 24px;
            // height: 24px;
        }
    }

}
</style>