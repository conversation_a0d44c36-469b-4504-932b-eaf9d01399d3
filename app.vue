<template>
  <div class="wrap" :style="diyWrapStyle">
    <div class="index_wrap" v-if="commonLoading">
      <div class="c_loading"></div>
    </div>
    <!-- 路由出口 -->
    <NuxtLayout>
      <NuxtPage></NuxtPage>
    </NuxtLayout>
    <!-- <NuxtWelcome /> -->
  </div>


</template>
<script lang="ts" setup>
import "ant-design-vue/dist/antd.css";
import ogImg from '~/assets/images/common/google_share_img.jpg';
const commonLoading = useState('commonLoading', () => true)
const diyWrapStyle = useState('diyWrapStyle', () => '')
// 路由
const route = useRoute();
const { t: $t } = useI18n();

let keywords = $t('pages.meta.keywords');
let description = $t('pages.meta.description');


// console.log("import.meta.env", import.meta.env.VITE_NODE_ENV)

if (import.meta.env.VITE_NODE_ENV == "production") {
  // 配置 seo
  useSeoMeta({
    description,
    keywords,
    ogTitle: 'PLAYBEST',
    ogDescription: description,
    ogSiteName: "PLAYBEST",
    ogImage:ogImg ,
    twitterCard: 'summary_large_image',
  })
  //  google统计
  useHead({
    script: [
      {
        src: 'https://www.googletagmanager.com/gtag/js?id=G-CY1Y3CGF09',
        async: true
      },
      {
        innerHTML: `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-CY1Y3CGF09');
      `,
        tagPosition: 'bodyClose'
      }
    ]
  })
} else {
  useHead({
    title: "",
    meta: [
      { name: "robots", content: "noindex" },
      { name: "googlebot", content: "noindex" },
    ],
  });
}

setTimeout(() => {
  commonLoading.value = false;
}, 6000)

// 判断是否已经加载过YKTrack,热更时可能会重复加载
if (process.client) {
  document.body.className = "common_body";
  window.YKTrack = null;
  if(!window.YKTrack){
  // 通关ua判断是web/mobile
  const ua = navigator.userAgent;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
  // https://payment.playbest.net
  // https://payment.funtales.com
  // 判断路由是playbest还是funtales
  const isFuntales = route.path.includes('funtales');
  const appId = isFuntales ? '30200366' : '30200365';
  let _roleId: any = '';
  if (route.params?.id) {
    _roleId = useCookie(`roleId_item${route.params.id}`)?.value || ''
  }

  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = '/js/yktrack-web-1.0.0.iife.min.js';
  script.onload = function () {
    YKTrack!.init({
      dsn: `https://sdkcore.playbest.net/batch/${appId}`, // 上报地址 game_app_id:30200365 playbest支付中心; 30200366 funtales支付中心
      appId: appId, // 新项目管理系统的游戏项目ID
      gameAppId: appId, // 海外网页支付的项目ID
      gameId: '', // 海外网页支付各游戏业务使用的项目ID
      roleId: _roleId, // 玩家账号ID下选择的角色ID
      osType: isMobile ? 'mobile' : 'web', // 操作系统类型
      userId: localStorage.getItem('user') || '', // 用户ID
      loginId: '',
      loginName: '',
      eventId: 802,
      appCode: isFuntales ? 'funtales' : 'playbest',
      appVersion: '1.0.0',
      noBash64: true,
      debug: false,
      singlePage: true,
      cacheWaitingTime: 3000,
      cacheMaxLength: 10,
      pv: true,
      performance: true,
      behavior: true,
      intersection: true,
      error: true,
      beforeSendData(data: any) {
        let game_id: string | null = '';
        if (route.path.includes('/pay') && route.params.id) {
          // 常规页面通过url路径获取game_id
          game_id = route.params.id;
        } else if (route.path.includes('/webview') && route.query.projectId) {
          // webview页面上报projectId,数分那映射game_id
          game_id = route.query.projectId;
        } else if (route.path.includes('/result_webview') && localStorage.getItem('WEBVIEW_GAME_ID')) {
          // result_webview 通过webview页存下的storage上报projectId,数分那映射game_id
          game_id = localStorage.getItem('WEBVIEW_GAME_ID');
        }
        return data.map((m: any) => {
          m.game_id = game_id;
          return m;
        });
        },
      });
    };
    document.head.appendChild(script);
  }
}
</script>
<style scoped>
.wrap {
  width: 100%;
  overflow-x: hidden;
  background: #f6f6f6;
}

.index_wrap {
  position: fixed;
  left: 50%;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  top: 100px;
  z-index: 1004;
  margin-left: -75px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
