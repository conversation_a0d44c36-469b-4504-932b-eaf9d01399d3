import URLS from '~/api/uri';
// 钩子函数 无法放在模块顶层
// const axios = (options:any)=>{
//     const { $axios } = useNuxtApp() as any;
//     return $axios(options);
// }
import { $axios } from '~/utils/axiosCustiom';
// 服务器获取请求
import { useAsyncDataCustom } from "~/utils/useAsyncDataCustom"; // 调整导入路径
// 服务器获取
import { useFetchCustom } from "~/utils/useFetchCustom"; // 调整导入路径


interface OrderListParams {
    project_id: number;  //项目ID
    role_id: string | undefined;  //角色ID
    page?: number;  // 页码1
    page_size?: number; //每页数量，默认为20  
    order_status: number;  //订单状态  // 1  待支付   2  关闭  3  等待发货   4  完毕
}
/**
 * 获取订单列表
 * @param {Object} param0 包含订单查询条件的对象
 * @param {number} param0.project_id 项目ID
 * @param {number} param0.role_id 角色ID
 * @param {number} [param0.page=1] 页码，默认为1
 * @param {number} [param0.page_size=20] 每页数量，默认为20
 * @param {string} param0.order_status 订单状态
 * @returns 返回一个Promise对象，解析后包含订单列表信息
 */
const getOrderList = ({project_id,role_id,page=1,page_size=20,order_status}:OrderListParams) => {

    // 构建请求URL并发送GET请求
    return $axios({
        url:`${URLS.Api.getOrderList}`,
        method: 'GET',
        params:{
            project_id,
            role_id,
            page_size,
            page,
            order_status
        }
    });
};

interface postCloseOrderParams {
    roleId: string | undefined;  //角色ID
    orderId: number;  //订单ID
}

/**
 * 关闭订单的接口调用
 * 
 * @param {postCloseOrderParams} {roleId, orderId} - 调用参数对象，包含以下字段：
 *    - roleId: string | undefined - 角色ID，可能未定义
 *    - orderId: number - 订单ID
 * @returns 返回一个Promise，封装了axios的请求结果
 */
const postCloseOrder = ({roleId,orderId}:postCloseOrderParams) => {
    // 使用axios发起POST请求，关闭订单
    return $axios({
        url:`${URLS.Api.postCloseOrder}`,
        method: 'POST',
        data:{
            roleId,
            orderId,
        }
    });
};

export { 
    getOrderList,
    type OrderListParams,
    postCloseOrder,
}