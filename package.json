{"name": "yoka-center-ssr", "version": "1.0.0", "description": "", "type": "module", "scripts": {"build": "nuxt build", "build:release": "nuxt build --dotenv .env.release", "build:test": "nuxt build --dotenv .env.test", "build:dev": "nuxt build --dotenv .env.development --host 0.0.0.0", "dev": "nuxt dev --host 0.0.0.0 --port 3000", "dev:prod": "nuxt dev  --dotenv .env.production", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "packageManager": "yarn@1.22.22", "dependencies": {"@adyen/adyen-web": "^5.61.0", "@ant-design-vue/nuxt": "^1.4.2", "@nuxtjs/composition-api": "^0.34.0", "@nuxtjs/i18n": "^8.3.0", "ant-design-vue": "3.2.20", "axios": "^1.6.8", "js-base64": "~3.7.7", "less": "^4.2.0", "moment": "^2.29.2", "nprogress": "^0.2.0", "nuxt": "~3.14.0", "pinia": "~2.1.7", "pnpm": "^8.15.4", "qrcode": "^1.5.4", "reset-css": "^5.0.2", "store": "^2.0.12", "validator": "^13.12.0", "vite-plugin-compression": "~0.5.1", "vue": "~3.4.21", "vue-router": "~4.3.0"}, "devDependencies": {"@nuxtjs/eslint-config": "^12.0.0", "@nuxtjs/eslint-module": "^4.1.0", "autoprefixer": "^10.4.19", "eslint": "^9.2.0", "postcss": "^8.4.38", "rollup-plugin-visualizer": "^5.12.0", "sass": "~1.69.0", "sass-loader": "^13.3.0", "vite-plugin-remove-console": "^2.2.0"}}