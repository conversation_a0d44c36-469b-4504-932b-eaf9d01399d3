<template>
    <div>
        <div class="order_banner">
            <img class="img banner_pc" :src="banner.pc" alt="" />
            <img class="img banner_mobile" :src="banner.mb" alt="" />
        </div>
    </div>
    <div class="error_status">
        <div v-if="errerData">
            <div class="icon">
                <div class="c_status_network_error"></div>
            </div>
            <div class="txt">{{ errerData }}</div>
        </div>
        <div v-else>
            <div class="icon">
                <div class="c_loading"></div>
            </div>
            <div class="txt">{{ $t('pay.order.comfirming.loading') }}</div>
            <div class="txt small">{{ $t('pay.order.comfirming.txt') }}</div>
        </div>
        <div class="btn c_btn_a" @click="handleClick">{{ $t("pages.pay.view-order") }}</div>
    </div>
</template>
<script lang="ts" setup>
import { useAuthStore } from '~/stores/auth.js';
const authStore = useAuthStore();
import { useUserStore } from "~/stores/user";
const { banner_pc, banner_mb } = useUserStore();
const banner = ref({
    pc: banner_pc,
    mb: banner_mb,
})

// 语言
const { locale, t } = useI18n();
// 事件委托
const handleClick = () => {
    navigateTo(`/${locale.value}/my/list`);
};

const errerData = ref("");
const route = useRoute();
if (route.query) {
    // console.log(route.query.data)
    if (route.query.data) {
        errerData.value = route.query.data;
    }
    if (route.query.from && route.query.from === "webview") {
        definePageMeta({
            layout: false
        })
    }
}
onMounted(() => {
    // authStore.hideLoading();
});

</script>
<style lang="scss" scoped>
.error_status {
    padding-bottom: 40px;

    .txt {
        margin: 0 auto;
        font-size: 20px;
        line-height: 50px;
        text-align: center;
        padding: 0 20px;

        &.small {
            font-size: 16px;
            color: #666;
        }
    }

    .btn {
        width: 240px;
        font-size: 14px;
        flex-shrink: 0;
        border-radius: 64px;
        background: #2C5AD7;
        text-align: center;
        line-height: 50px;
        color: #fff;
        margin: 0 auto;
        margin-top: 20px;
        cursor: pointer;
    }

    :global(.go_to_home) {
        margin: 0 4px;
        color: #3A58A9;
        cursor: pointer;
        text-decoration: none;


    }

    :global(.go_to_home:hover) {
        text-decoration: underline;
    }

    .icon {
        width: 100%;
        max-width: 400px;
        margin: 30px auto 0;
    }
}

.order_banner {
    width: 100%;
    min-height: 15.6vw;

    .img {
        width: 100%;
    }

}
</style>
