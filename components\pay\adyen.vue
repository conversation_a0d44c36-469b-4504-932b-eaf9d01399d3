<template>
    <!-- Adyen相关代码 -->
    <a-modal v-model:visible="showAdyenPop" class="adyen_pop_Wrap white_box" :maskClosable="false" :footer="null"
        :destroyOnClose="true" :keyboard="true">
        <div class="c_loading" v-if="showLoading"></div>
        <div id="dropin-container"></div>
    </a-modal>

    <!-- 订单状态组件，使用json数据绑定和事件监听器 -->
    <a-modal v-model:visible="showResultPop" class="adyen_pop_Wrap" :footer="null" :destroyOnClose="true"
        :closable="!isWebview" :maskClosable="!isWebview" :keyboard="true">
        <order-status :json="appOrder" @setClose="orderStatusClose" :is-webview="isWebview"></order-status>
    </a-modal>
</template>
<script lang="ts" setup>
import AdyenCheckout from '@adyen/adyen-web'
import '@adyen/adyen-web/dist/adyen.css'
// 使用多语言支持
const { locale, t } = useI18n();
const showResultPop = ref<boolean>(false);
const showAdyenPop = ref<boolean>(false);
const showLoading = ref<boolean>(false);
// 定义组件属性
const $prop = defineProps({
    json: {  // 配置数据数据
        type: Object,
        default: {},
    },
    appOrder: {
        type: Object,
        default: {},
    },
    isWebview: { // 判断是否是webview用于传递至orderStatus
        type: Boolean,
        default: false
    }
})

const $emit = defineEmits(['setClose']);

// 初始化Adyen Drop-in组件的异步函数
const dropInAdyen = async (data: any) => {
    console.log(data);
    showLoading.value = true;
    let _environment = process.env.VITE_NODE_ENV == 'production' || process.env.VITE_NODE_ENV == 'release' ? 'live' : 'test'; // 环境设置，测试或正式
    
    // GOOGLE 配置  环境变量不同
    // if (data.paymentClass == 'google') {
    //     _environment = process.env.VITE_NODE_ENV == 'production' ? "PRODUCTION" : "TEST";
    // }
    const googlePayConfiguration = {
        // amount: {
        //     value: 100,
        //     currency: "USD"
        // },
        // countryCode: "US",
        //Set this to PRODUCTION when you are ready to accept live payments
        environment: _environment,
    };
    // Adyen结账配置对象
    const configuration = {
        locale: locale.value || 'en-GB', // 语言设置
        translations: { // 自定义翻译
            'en-GB': {
                'paymentMethods.moreMethodsButton': 'More payment methods',
                payButton: 'Pay',
                storeDetails: 'Save for my next payment',
            },
        },
        environment: _environment, // 环境设置，测试或正式
        clientKey: data.content.clientKey + '', // 客户密钥，用于客户端认证
        analytics: {
            enabled: true, // 是否启用数据分析
        },
        session: { // 会话信息
            id: data.content.id + '', // 会话ID
            sessionData: data.content.sessionData + '', // 会话数据
        },
        onPaymentCompleted: (result: any, component: any) => { // 支付完成回调
            console.info(result, component)
            // 处理不同的支付结果
            if (result.resultCode === 'Authorised') {
                // 支付成功时的处理
                showResultPop.value = true;
                showAdyenPop.value = false;
            } else {
                // 其他结果处理
            }
        },
        onError: (error: any, component: any) => { // 错误处理回调
            console.error(error.name, error.message, error.stack, component)
        },
        // 支付方式的具体配置
        paymentMethodsConfiguration: {
            // googlepay: googlePayConfiguration,
            card: { // 银行卡支付配置
                hasHolderName: false,
                holderNameRequired: false,
                billingAddressRequired: false,
                storePaymentMethodMode: "enabled",
                enableStoreDetails: true, // 是否保存支付信息以便下次使用
                // 自定义保存卡信息的提示
                storeDetails: {
                    label: "Save my card details for next visit"
                }
            },
        },
    }

    // 创建AdyenCheckout实例
    const checkout = await AdyenCheckout(configuration)
    // 创建并挂载Drop-in组件
    const dropinComponent = checkout.create('dropin', {
        onReady: () => {
            // 组件加载完成后的操作
            console.log('Credit Card Component is ready');
            showLoading.value = false;
        },
        instantPaymentTypes: ['googlepay','applepay'],
    }).mount('#dropin-container')
    console.log('dropinComponent', dropinComponent)
};

// 订单状态关闭函数
const orderStatusClose = (status: boolean) => {
    showResultPop.value = false;
    showAdyenPop.value = false;
    $emit("setClose", false);
}

// 页面加载时执行
onMounted(() => {
    // 监听属性变化
    watch(() => $prop.json, (newValue, oldValue) => {
        // 判断是否已登录并获取页面
        if (newValue.content && newValue.content.clientKey) {
            dropInAdyen(newValue);
            showAdyenPop.value = true;
        }
        if (newValue.showLoading) {
            showLoading.value = true;
            showAdyenPop.value = true;
        } else {
            //showLoading.value = false;
        }
    })
})

</script>
<style lang="scss">
.adyen_pop_Wrap {
    max-width: 520px;
    width: 100% !important;

    .ant-modal-content {
        background: none;
        box-shadow: none;
    }

    .ant-modal-body {
        padding: 0;
    }
    &.white_box .ant-modal-body{
        padding: 60px 20px 20px;
        background: #fff;
        border-radius: 20px;
    }
}

.c_loading {
    margin: 100px auto;
}

.adyen_pop_Wrap .ant-modal-close-x {
    background: #fff;
    border-radius: 20px;
    height: 50px;
}

.adyen-checkout__payment-method:last-child {
    padding-bottom: 20px;
}
.adyen-checkout__dropin .adyen-checkout__applepay__button {
    -webkit-appearance: -apple-pay-button!important;
    appearance: -apple-pay-button!important;
}
</style>