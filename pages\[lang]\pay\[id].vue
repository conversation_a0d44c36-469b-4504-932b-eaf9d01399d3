<template>
    <div>
        <div class="banner">
            <img class="img_pc" :src="pageInfo.pageHeaderPcImg" alt="">
            <img class="img_mobile" :src="pageInfo.pageHeaderMbImg" alt="">
        </div>
        <div class="content">
            <!-- 切换服务器，地区区域 -->
            <div class="role_wrap c_wrap">
                <div class="change_wrap">
                    <div class="header_box">
                        <div class="header_main"><img :src="pageInfo.schemeIcon"
                                :alt="schemesInfo.name && schemesInfo.name[locale] || 404"
                                :title="schemesInfo.name && schemesInfo.name[locale] || 404"></div>
                        <div class="right">
                            <h3>{{ schemesInfo.name && schemesInfo.name[locale] || 404 }}</h3>
                            <a-dropdown class="region_select">
                                <a-button @click.prevent>
                                    {{ curSchemes.name && curSchemes.name[locale] }}
                                    <CaretDownOutlined />
                                </a-button>
                                <template #overlay>
                                    <a-menu @click="SchemesChange">
                                        <a-menu-item :key="index" v-for="item, index in schemesInfo.schemes">
                                            {{ item.name[locale] }}
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </div>
                    </div>

                    <div class="change_role">
                        <template v-if="(!isLogin || LoginType === 'role') && !roleLogin && LoginType !== 'pb'">
                            <!-- 区服选择 -->
                            <div class="change_select">
                                <div class="region_select">
                                    <!-- <div class="a_select_title">{{ $t("pages.pay.server") }}</div> -->
                                    <client-only>
                                        <a-select class="pay_server_select" v-model:value="curServer.label"
                                            placement="bottomLeft" show-search
                                            :placeholder="$t('pages.pay.change-server')" style="width: 200px"
                                            :options="serverOptions" :filter-option="filterOption"
                                            @change="chooseServer">
                                        </a-select>
                                    </client-only>
                                </div>
                            </div>
                            <!-- 角色直冲 -->
                            <div class="change_select">
                                <div class="region_select" style="padding-right: 0;">
                                    <!-- <div class="a_select_title">{{ $t('pages.pay.please-enter-the-role-id') }}</div> -->
                                    <client-only>
                                        <a-input class="pay_role_select" v-model:value="curRoleId"
                                            :placeholder="$t('pages.pay.please-enter-the-role-id')">
                                            <template #suffix>
                                                <a-tooltip :title="$t('pay.how.get.roleId')"
                                                    overlayClassName="smallFont"
                                                    v-if="schemesInfo && schemesInfo.role && schemesInfo.role[locale] && !roleIdGuideStatus">
                                                    <QuestionCircleOutlined style="color: rgba(0, 0, 0, 0.45)"
                                                        data-track-id="809" @click="roleIdGuideStatus = true" />
                                                </a-tooltip>
                                            </template>
                                        </a-input>
                                    </client-only>
                                    <!-- <div class="recently_login"><span>·</span>{{ $t('pages.pay.recently-login')}}</div> -->
                                </div>
                                <a-button class="pay_role_login" type="primary" :loading="roleLoginLoading"
                                    :disabled="roleLoginLoading" @click="getRoleDetail" data-track-id="1303">{{
                                        $t('pay.role.comfirm')
                                    }}</a-button>
                            </div>
                        </template>
                        <template v-else-if="(!isLogin || LoginType === 'role') && roleLogin && LoginType !== 'pb'">
                            <!-- 区服选择 -->
                            <div class="change_select">
                                <div class="txt a_select_title">{{ $t("pages.pay.server") }}</div>
                                <div class="txt_black right_line">{{ curServer.name }}</div>
                            </div>
                            <!-- 角色直冲 -->
                            <div class="change_select">
                                <div class="txt a_select_title">{{ $t('pages.pay.purchasing-character') }}</div>
                                <div class="txt_black">{{ curRoleId }}</div>
                                <div class="icon_change_role" @click="setRoleLogout"></div>
                            </div>
                        </template>

                        <!-- 登录后 角色选择 -->
                        <div class="change_select" v-if="isLogin && LoginType != 'role'">
                            <div class="region_select">
                                <!-- <div class="a_select_title">{{ $t("pages.pay.character-information") }}</div> -->
                                <client-only>
                                    <a-select class="pay_role_select" :disabled="noPbRole" v-model:value="curRole"
                                        placement="bottomLeft" show-search
                                        :placeholder="$t('pages.pay.purchasing-character')" style="width: 200px"
                                        :options="rolesList" :filter-option="filterRoles" @change="chooseRole">
                                    </a-select>
                                </client-only>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="role_login_hint" v-if="LoginType === 'role'">
                    <exclamation-circle-outlined style="color:#999; margin-right:4px" />
                    <span class="txt">{{ $t('pay.role.login.hint') }}</span>
                </div>
            </div>

            <!-- 商品区 -->
            <div class="goods_list_wrap c_wrap">
                <!-- 滚动提示 -->
                <div class="notice_wrap"
                    v-if="pageInfo.pageAnn && pageInfo.pageAnn[locale] && pageInfo.pageAnn[locale].ann">
                    <div class="icon_notice"></div>
                    <div class="notice_txt_wrap">
                        <div class="notice_txt_main" :class="{ animation: pageInfo.pageAnn[locale].ann.length > 1 }"
                            :style="{ animationDuration: pageInfo.pageAnn[locale].ann.length * 3 + 's' }">
                            <div class="notice_txt_item" v-for="item, index in pageInfo.pageAnn[locale].ann"
                                :key="index">{{ item }}
                            </div>
                            <div class="notice_txt_item" v-for="item, index in pageInfo.pageAnn[locale].ann"
                                :key="index">{{ item }}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 商品 -->
                <div class="goods_list_box" :class="{ disabled: payMethodVal == 60001 }">
                    <!-- 当玩家选择mycard的时候，不让其选择 -->
                    <div class="goods_list_cover" v-if="payMethodVal == 60001"></div>
                    <!-- 商品 tab切换 -->
                    <div class="tab_wrap">
                        <div class="tab_box">
                            <template v-if="pageInfo.categories && pageInfo.categories.length < 2">
                                <div class="li active">{{ pageInfo.categories[0].Name[locale] }}</div>
                            </template>
                            <template v-else>
                                <div class="li" :class="{ active: curCategoryId == -1 }"
                                    @click="changeProductsType(-1)">{{
                                        $t("pages.pay.all") }}</div>
                                <div class="li" v-for="item, index in pageInfo.categories" :key="index"
                                    :class="{ active: curCategoryId == item.Id }" @click="changeProductsType(item.Id)">
                                    {{ item.Name[locale] }}</div>
                            </template>
                        </div>
                    </div>
                    <div class="goods_list">
                        <div v-if="loading" class="order_list_empty">
                            <div class="c_loading"></div>
                        </div>
                        <!-- 商品 列表 -->
                        <template v-for="item, index in pageInfo.products" :key="index"
                            v-else-if="pageInfo.products && pageInfo.products.length > 0">
                            <div class="item_box" v-if="item.categoryId == curCategoryId || curCategoryId == -1">
                                <div class="item"
                                    :class="{ active: curProduct.id == item.id, gray: item.activityMaxNum && parseInt(item.activityMaxNum) != 0 && parseInt(item.activityNum) >= parseInt(item.activityMaxNum) }">
                                    <div class="click_box_pc" @click="chooseProduct(item)"></div>
                                    <div class="click_box_m" @click="chooseProduct_m(item)"></div>
                                    <div class="imgBox">
                                        <img class="img" :src="item.logo" />
                                        <div class="activity_goods_num" v-if="parseInt(item.activityMaxNum) > 0">
                                            <div class="label_txt">{{ item.activityNum || 0 }}/{{ item.activityMaxNum }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cont">
                                        <div class="h3"
                                            :class="{ two_line: !(item.description && item.description[locale]) }">{{
                                                item.name[locale]
                                            }}</div>
                                        <div class="desc" v-if="item.description && item.description[locale]">{{
                                            item.description[locale] }}</div>
                                    </div>
                                    <div class="countDown_wrap" v-if="item.activityCountdown >= 0">
                                        <div class="icon_clock"></div>
                                        <div class="num">
                                            <div class="num_txt">{{ formatTimeSecond(item.activityCountdown) }}</div>
                                        </div>
                                    </div>
                                    <div class="goods_btn_wrap">
                                        <div class="activity_goods_label" v-if="item.activityIcon">
                                            <img :src="item.activityIcon[locale]" />
                                        </div>
                                        <div class="goods_btn">
                                            <span class="dollar">{{ pageInfo.currency }}</span>
                                            <span class="price">{{ item.price }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <div v-if="!loading && ((pageInfo.products && pageInfo.products.length == 0) || curProducts.length == 0)"
                            class="order_list_empty">
                            <div class="c_content_empty">
                                <div class="c_status_empty"></div>
                            </div>
                            <div>{{ $t('pages.pay.empty-desc') }}</div>
                        </div>
                        <!-- <div class="item_box">
                            <div class="item active">
                                <div class="imgBox">
                                    <img class="img"
                                        src="https://payadmin-test-1256526072.cos.ap-shanghai.myqcloud.com/d160769b-22e8-4757-9efb-5580439b9dbd.jpg" />
                                </div>
                                <div class="h3">1980仙玉+赠送280仙玉</div>
                                <div class="goods_btn">
                                    <span class="dollar">USD</span>
                                    <span class="price">899,899,390.99</span>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>

                <!-- PC  支付方式-->
                <div class="pay_method_wrap">
                    <h2 class="c_pay_h2">{{ $t("pages.pay.payment-mode") }}</h2>
                    <payPaymentMethods v-model:payMethodVal="payMethodVal" :pageInfo="pageInfo"
                        :mycardItems="mycardItems" :curMycardItemId="curMycardItemId" @setPayMethod="setPayMethodVal"
                        @selectMycardItem="chooseMycardItem" @orderStatusClose="orderStatusClose"
                        @setSubmitData="setSubmitData" @setQrCodeUrl="setQrCodeUrl" @setHrefJump="setHrefJump"
                        ref="paymentMethodsRef" />
                </div>
            </div>
            <div ref="payDescRef" class="pay_desc_wrap"></div>
            <!-- 支付区域 -->
            <div class="pay_ctrl_wrap c_wrap" :class="{ fixed: isFixedPayment }">
                <div class="pay_box">
                    <div class="pb_left">
                        <div class="goods_desc">
                            <div class="name">{{ $t("pages.pay.item-information") }}：</div>
                            <div class="txt">{{ curProduct.name && curProduct.name[locale] }}</div>
                        </div>
                        <!-- <div class="goods_desc">
                        <div class="name">商品数量：</div>
                        <div class="txt">99</div>
                    </div> -->
                    </div>
                    <div class="pb_middle" @click="showPayDesc" data-track-id="902">
                        <div class="txt">{{ $t("pages.pay.payment-mode") }}：</div>
                        <div class="imgBox">
                            <div class="img" v-for="item, index in payMethodImgArray" :key="index">
                                <img :src="item.src" :alt="item.name" />
                            </div>
                        </div>
                        <caret-down-filled style="margin-left: 8px;" />
                    </div>
                    <div class="pb_right">
                        <div class="price_txt">{{ $t("pages.pay.order-price") }}：</div>
                        <div class="price_label">{{ pageInfo.currency }} </div>
                        <div class="price">{{ curProduct.price || 0 }}</div>
                        <a-button class="pb_payBtn" type="primary" :loading="orderLoading" :disabled="orderLoading"
                            @click="onSubmit" :data-track-id="isFixedPayment ? '901' : '805'">{{
                                $t('pages.pay.purchase-now') }}</a-button>
                    </div>
                </div>
            </div>
            <!-- 充值说明 -->
            <div class="c_wrap">
                <h2 class="c_pay_h2">{{ $t('pages.pay.recharge-instructions') }}</h2>
                <div class="c_pay_p pay_desc_wrap_html" v-html="pageInfo.pageDesc && pageInfo.pageDesc[locale]"></div>
            </div>
            <!-- test -->
            <!-- <div class="c_wrap">{{ appOrder }}</div> -->
        </div>
    </div>


    <!-- 手机版支持弹窗 -->
    <div class="order_fixed_model" :class="{ show: orderFixedShow }" @click="closeOrderFixed"></div>
    <div class="order_fixed_wrap" :class="{ show: orderFixedShow }">
        <div class="order_fixed_box">
            <CloseOutlined class="order_f_close" @click="closeOrderFixed" />
            <div class="title">{{ $t('pay.comfirm.account') }}</div>
            <!-- 订单主体 -->
            <div class="order_f_main" ref="orderFixedMainRef">
                <h3>{{ $t('pages.pay.character-information') }}</h3>
                <div class="line">
                    <div class="left">{{ $t("pages.pay.server") }}:</div>
                    <div>{{ LoginType === 'role' ? curServer.name : (isLogin ? curRole.serverName : curServer.name) }}
                    </div>
                </div>
                <div class="line">
                    <div class="left">{{ $t('pages.pay.purchasing-character') }}:</div>
                    <div>{{ LoginType === 'role' ? curRoleId : (isLogin ? curRole.roleName : curRoleId) }}</div>
                </div>
                <h3>{{ $t('pages.pay.selected-product') }}</h3>
                <div class="line">{{ curProduct.name && curProduct.name[locale] }}</div>
                <div v-if="curProduct.description && curProduct.description[locale]">
                    <h3>{{ $t('pages.pay.item-information') }}</h3>
                    <div class="line gray">{{ curProduct.description && curProduct.description[locale] }}</div>
                </div>
                <div class="price_line"><span>{{ $t("pages.pay.order-price") }}：</span>
                    <div class="price_num">{{ pageInfo.currency }} {{ curProduct.price || 0 }}</div>
                </div>
                <h3>{{ $t('pages.pay.payment-mode') }}</h3>
                <payPaymentMethods v-model:payMethodVal="payMethodVal" :pageInfo="pageInfo" :mycardItems="mycardItems"
                    :curMycardItemId="curMycardItemId" paymentType='mobile' @setPayMethod="setPayMethodVal"
                    @selectMycardItem="chooseMycardItem" @orderStatusClose="orderStatusClose"
                    @setSubmitData="setSubmitData" @setQrCodeUrl="setQrCodeUrl" @setHrefJump="setHrefJump"
                    ref="paymentMethodsRef" />
            </div>
            <!-- 支付按钮 -->
            <div class="order_f_bottom">
                <a-button class="order_f_payBtn" data-track-id="805" type="primary" :loading="orderLoading"
                    :disabled="orderLoading" @click="onSubmit">{{
                        $t('pages.pay.purchase-now') }}</a-button>
            </div>
        </div>
    </div>

    <!-- 提交订单状态 -->
    <a-modal v-model:visible="orderStatusOpen" class="order_status_pop" :footer="null"
        :afterClose="() => { orderStatusClose(false) }" :destroyOnClose="true" :keyboard="true">
        <order-status :json="appOrder" @setClose="orderStatusClose" :isAlipay="payMethodVal == 60010"
            :qrCode="qrCodeUrl"></order-status>
    </a-modal>

    <!-- 查找roleId 引导提示 -->
    <a-modal v-model:visible="roleIdGuideStatus" class="roleIdGuideWrap_pop" :footer="null"
        :afterClose="() => { roleIdGuideStatusClose(false) }" :destroyOnClose="true" :keyboard="true">
        <div class="roleIdGuideWrap">
            <div class="title">{{ $t('pages.pay.how-to-find-my-ID') }}</div>
            <div class="roleIdGuideContent" v-html="schemesInfo && schemesInfo.role && schemesInfo.role[locale]"></div>
        </div>
    </a-modal>

    <!-- 活动引导弹窗 -->
    <a-modal v-model:visible="isShowGuidePop" class="pay_guide_pop_wrap" :closable="false" :footer="null"
        :keyboard="true">
        <div class="pay_guide_pop_main">
            <img class="pc_img" :src="pageInfo.activityDisplay && pageInfo.activityDisplay[locale].pc" />
            <img class="mb_img" :src="pageInfo.activityDisplay && pageInfo.activityDisplay[locale].mb" />
        </div>
        <div class="pay_guide_pop_close" @click="() => { showGuidePop = false; }"></div>
    </a-modal>
    <!-- adyen 支付 -->
    <PayAdyen :json="submitData" :app-order="appOrder" @setClose="orderStatusClose"
        v-if="!(submitData && submitData.status == 'fail')"></PayAdyen>
</template>
<script lang="ts" setup>
import { getRole, getServerList, getProjectList, getRoleList, getPageInfo, postSubmit, getMycardItem } from "~/api/pay";
import { formatTimeSecond } from "~/utils/tools";
import { useUserStore } from '~/stores/user';
import { useAuthStore } from '~/stores/auth';
import ogImg from '~/assets/images/common/google_share_img.jpg';

// 路由
const route:any = useRoute();
const router = useRouter();
// 全局状态
const userStore = useUserStore();
const authStore = useAuthStore();
const isLogin = computed(() => authStore.isLogin);
const isShowGuidePop: Ref<boolean> = computed(() => showGuidePop.value && !authStore.loginModal);

// 语言
const { locale, t } = useI18n();

if (import.meta.env.VITE_NODE_ENV == "production") {

    const keywords = t('pages.payment.meta.keywords');
    const description = t('pages.payment.meta.description');

    // 配置 seo
    useSeoMeta({
        description,
        keywords,
        ogTitle: 'PLAYBEST',
        ogDescription: description,
        ogSiteName: "PLAYBEST",
        ogImage: ogImg,
        twitterCard: 'summary_large_image',
    })
}

// 支付组件
const paymentMethodsRef = ref<any>(null);

// adyen 支付赋值
const setSubmitData = (data: any) => {
    submitData.value = data;
}

// 选中支付状态
const payMethodVal = ref(0);

// 支付方式图片
const payMethodImgArray = ref<any>([]);
// 支付组件 滚动定位
const payDescRef = ref<any>(null);
const isFixedPayment = ref(false);

// 是否是pb角色
const noPbRole = ref<boolean>(false);

const showGuidePop = ref(false);


// 当前页面服务器列表和详情
const schemesInfo = ref<any>({});
// 当前选中的 国家服列表
const curSchemes = ref<any>({});
// 项目列表
const projects = ref<any>([]);
// 区服列表
const servers = ref<any[]>([]);
// 区服列表 : 当前选中列表
const curServer = ref<any>({});
// 页面基础信息
const pageInfo = ref<any>({});
// loading 配置
const loading = ref(true);
const roleLoginLoading = ref(false);
// 当前选中商品
const curProduct = ref<any>({});
// 角色列表
const rolesList = ref<any[]>([]);
// 当前角色
const curRole = ref<any>({});
// 角色直冲的 角色ID
const curRoleId = ref("");
// 角色直冲 登录态
const roleLogin = ref(false);

// 订单提交后获取状态弹窗
const orderStatusOpen = ref(false);

// 用户查找 roleId引导 弹窗
const roleIdGuideStatus = ref(false);

// 弹窗延迟时间
const duration = 3;

//当前页面 登录状态关系
const LoginType = ref<string>("both");

// mycard 配置
const mycardItems = ref<any[]>([]);
const curMycardItemId = ref(0);

// 支付宝二维码
const qrCodeUrl = ref("");
const setQrCodeUrl = (url: string) => {
    qrCodeUrl.value = url;
}

//判断是否包含活动页面
let _activityId = 0;


// 订单配置
const appOrder = ref<any>({
    projectId: route.params.id, // 获取角色信息参数
    orderId: undefined, // 订单号
    serverId: curServer.value.id || undefined,  // 服务器ID
    // roleId: roleInfo.roleId || undefined,
    roleId: undefined,  // 角色id
    kitId: undefined, // 生单参数
    SchemeId: curSchemes.value.id || undefined, //方案ID
    productId: undefined,  //商品选择后的id
    uid: undefined,  // 角色登录ID
    language: locale.value || 'en',
    // isTest: true,
    userName: undefined,  // 可填可不填
    channelId: "", //渠道传参
    activityId: 0, //活动ID
});

// order status 弹窗关闭回调
const orderStatusClose = (status: boolean) => {
    orderStatusOpen.value = status;
    // 如果是活动页刷新页面
    if (_activityId > 0) {
        refreshPageInfo()
    }
};
// 关闭用户引导 id
const roleIdGuideStatusClose = (status: boolean) => {
    roleIdGuideStatus.value = status;
    // 延迟关闭
};

// 点击支付跳转展示所有支付
const showPayDesc = () => {
    // console.log("payDescRef.value.offsetTop", payDescRef.value.offsetTop)
    // console.log("window.innerHeight", window.innerHeight)
    // console.log("window.scrollY", window.scrollY)
    const scrollTop = document.documentElement.scrollTop;
    const eleTop = payDescRef.value.getBoundingClientRect().top;
    const top = eleTop + scrollTop - window.innerHeight + 140;
    console.log(top);
    if (top > 0) {
        window.scrollTo(0, top);
    }
};


// 判断是否有客户端的传参,是否需要免登录
let _serverId: any = null;
let _roleId: any = null;
if (route.query) {
    let { channelId, serverId, roleId } = route.query;
    if (channelId) appOrder.value.channelId = channelId;
    _serverId = serverId;
    _roleId = roleId;
}
// 帮助用户进行角色登录
const helpPlayerRoleLogin = async () => {
    if (_serverId && _roleId) {
        curRoleId.value = _roleId;
        if (LoginType.value == "pb") {
            return;
        }
        console.log("helpPlayerRoleLogin", _serverId, _roleId)
        for (let i = 0; i < servers.value.length; i++) {
            const ele = servers.value[i];
            if (ele.id == _serverId) {
                curServer.value = ele;
                appOrder.value.serverId = _serverId;
                break;
            }
        }
        if (curServer.value.id) {
            getRoleDetail();
        }
    }
};


// 获取项目列表
try {
    const {
        data: getJsonResult,
        pending,
        error,
        refresh,
    } = await getProjectList();
    console.log("getProjectList", getJsonResult.value);
    let result = getJsonResult.value;
    if (result) {
        if (result.code == 200 || result.code == 0) {
            let { data } = result;
            projects.value = data.projects;
            // 判断项目名
            const Schemes = data.projects.filter((item: any) => item.id + '' === route.params.id);
            // console.log("Schemes",Schemes)
            schemesInfo.value = Schemes[0] || [];
            LoginType.value = Schemes[0]?.LoginType ?? 'both';
            if (Schemes[0]) {
                curSchemes.value = Schemes[0].schemes[0];
                appOrder.value.SchemeId = curSchemes.value.id;
            } else {
                // 页面加载错误
                message.error({
                    content: () => t("pay.maintenance.hint"),
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration,
                    onClose: () => {
                        navigateTo(`/${locale.value}/home`);
                    }
                });
                authStore.setLoginModal(false);
            };
        } else {
            message.error({
                content: result.message,
                class: 'c_message_big',
                style: {
                    marginTop: '20vh',
                },
                duration: 3,
                onClose: () => {
                    if (result.code == 500 && result.message == "record not found") {
                        navigateTo("/404");
                    }
                    if (result.code == 401) {
                        authStore.setLoginModal(true);
                    }
                }
            });
            console.error(result.message);
        }
    } else {
        // 错误弹窗
        const errorContent = error == null ? "404 request failed！" : error.value && error.value!.toString() || error.toString();
        message.error({
            content: errorContent,
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration: 3,
        });
        console.error(errorContent);
    }
    // console.log("aaaaaa",curSchemes.value)
} catch (error) {
    console.error(error);
}

// 获取服务器列表
try {
    const {
        data: resultServerList,
        pending,
        error,
        refresh,
    } = await getServerList(route.params.id as string);
    let result = resultServerList.value;
    if (result && result.code == 200 || result.code == 0) {
        let { data } = result;
        servers.value = data.servers || {};
    } else {
        message.error({
            content: result.message,
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration: 3,
        });
        if (result.code == 500) {
            navigateTo("/404");
        } else if (result.code == 401) {
            authStore.setLoginModal(true);
        }
    }
    // console.log(resultServerList);
} catch (err) {
    console.error(err)
}

/**
 * 获取商品信息
 * @param id   schemseId 通过 Projectlist接口获取
 */
let popSwitch = true;
//清洗数据
const formatData = (data: any) => {
    data.products?.forEach((item: any) => {
        if (parseInt(item.activityCountdown) === 0) {
            item.activityCountdown = -2;
        } else {
            item.activityCountdown = parseInt(item.activityCountdown);
        }
    });
    return data || [];
}
const getPageInfoData = async (id: number, roleId: string = "") => {
    console.log("roleId", roleId)
    console.log("appOrder.value.roleId", appOrder.value.roleId)
    try {
        loading.value = true;
        let result: any = await getPageInfo(id, roleId);
        loading.value = false;
        // console.log(result);
        if (result && result.code == 200 || result.code == 0) {
            let { data } = result;
            pageInfo.value = formatData(data) || [];

            // 获取用户之前的支付方式
            console.log(appOrder.value.projectId, id.toString())
            let userPayment: any = userStore.getUserPayment(route.params.id as string, id.toString());
            if (userPayment) {
                console.log("userPayment", userPayment)
                userPayment = JSON.parse(userPayment);
                //setPayMethodVal(userPayment);
                if (parseInt(userPayment.id) != 60001) {
                    nextTick(() => {
                        document.getElementById(`paymentId${userPayment.id}`)?.click();
                    });
                }
            }
            // 赋值给全局 活动ID，表示这个页面里有活动
            _activityId = data.activityId ? parseInt(data.activityId) : 0;
            // 判断是否有活动弹窗
            if (data.activityDisplay && data.activityDisplay[locale.value] && (data.activityDisplay[locale.value].pc || data.activityDisplay[locale.value].mb)) {
                let popupTiming = parseInt(data.popupTiming);  //弹窗 1 每次都打开  2 每天打开一次  3不展示
                if (popupTiming == 1) {
                    if (popSwitch) {
                        popSwitch = false;
                        showGuidePop.value = true;
                    }
                } else if (popupTiming == 2) {   // 判断是否之前弹出过
                    const popCookie = useCookie(`pay_guide_pop_${route.params.id}`, { maxAge: 86400 });
                    if (popCookie.value != "pop") {
                        showGuidePop.value = true;
                        popCookie.value = "pop";
                    }
                }
            }
            // 开启活动倒计时
            startCountdowns(pageInfo);
        } else {
            if (result.code == 400) {
                // 页面加载错误
                message.error({
                    content: () => t(`pay.maintenance.hint`),
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration,
                    onClose: () => {
                        navigateTo(`/${locale.value}/home`);
                    }
                });
                authStore.setLoginModal(false);
            } else {
                message.error({
                    content: result.message,
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration: 3,
                });
            }
        }
    } catch (err) {
        console.error(err)
    }
}

// 刷新活动商品
const refreshPageInfo = async () => {
    if (_activityId > 0) {
        try {
            let result: any = await getPageInfo(curSchemes.value.id, appOrder.value.roleId);
            // 清除倒计时
            clearCountDown(pageInfo);
            // console.log(result);
            if (result && result.code == 200 || result.code == 0) {
                let { data } = result;
                pageInfo.value = formatData(data) || [];
                // 开启活动倒计时
                startCountdowns(pageInfo);
            } else {
                message.error({
                    content: result.message,
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration: 3,
                });
                if (result.code == 401) {
                    authStore.setLoginModal(true);
                }
            }
        } catch (err) {
            console.log(err);
        }
    }
}

// 获取角色列表
const getRoleListData = async (projectId: string) => {
    try {
        let result: any = await getRoleList(projectId);
        // console.log(result);
        if (result && result.code == 200 || result.code == 0) {
            let { roles } = result.data;
            if (roles && roles.length > 0) {
                roles.forEach((ele: any, index: number) => {
                    ele.label = `${ele.roleName} (${ele.serverName})`;
                    ele.value = index;
                });
                rolesList.value = roles;
                // 初始化选中第一个
                chooseRole(0);
                // curRole.value = roles[0];
                //console.log("=========", rolesList.value)
            }
            noPbRole.value = false;
        } else {
            message.error({
                content: result.message,
                class: 'c_message_big',
                style: {
                    marginTop: '20vh',
                },
                duration: 3,
                onClose: () => {
                    if (result.code == 401) {
                        authStore.setLoginModal(true);
                    } else if (result.code == 500) {
                        navigateTo("/404");
                    }
                }
            });
            // 没有pb角色的时候显示
            if (result.reason && result.reason == 'PROJECT_DOMAIN_NO_ROLE') {
                noPbRole.value = true;
                rolesList.value = [{ label: result.message, value: 0 }];
                // 初始化选中第一个
                chooseRole(0);
            } else {
                noPbRole.value = false;
            }
        }
    } catch (err) {
        console.error(err)
    }
}

// 登陆  登出 数据reset
const resetData = () => {
    roleLogin.value = false;
    curRoleId.value = "";
    curRole.value = {};
    rolesList.value = [];
    appOrder.value.roleId = undefined;

    if (_activityId > 0) {
        refreshPageInfo()
    }
};
// 登录后获取数据
const loginGetData = async (isLogin: boolean, isFirst?: boolean) => {
    if (!isFirst) {
        resetData();
    }
    // 如果登录了 且不是 LoginType 为 role 的情况
    if (isLogin && LoginType.value != "role") {
        authStore.getPBAuthToken(true); //重新设置PB登录token 未authToken
        await getRoleListData(route.params.id as string);
    } else { //退出登录的情况
        let { serverId, roleId } = authStore.getRoleAuthToken(route.params.id as string);
        console.log("serverId, roleId", serverId, roleId)
        console.log("_serverId, _roleId", _serverId, _roleId)
        // 从cookie 中获取 角色登录态
        if (!_serverId) _serverId = serverId;
        if (!_roleId) _roleId = roleId;
        // 判断是否是客户端传参打开，如果是就不弹窗
        if (_serverId && _roleId && LoginType.value != "pb" || LoginType.value == "role") {
            helpPlayerRoleLogin();
        } else {
            authStore.setLoginModal(true);  //设置弹窗显示
        }
    }
};


/**
 * 获取my card 参数
 * @param SchemeId 
 */
const setMycardItem = async (SchemeId: number) => {
    try {
        let roleId = 0;
        if (_activityId > 0) { roleId = appOrder.value.roleId }
        let result: any = await getMycardItem(SchemeId, roleId);

        // 关闭倒计时
        clearCountDown(mycardItems);
        // console.log(result);
        if (result && result.code == 200 || result.code == 0) {
            let { data } = result;
            mycardItems.value = data.items || [];
            // 开启活动倒计时
            startCountdowns(mycardItems);
            // console.log(data)
        } else {
            message.error({
                content: result.message,
                class: 'c_message_big',
                style: {
                    marginTop: '20vh',
                },
                duration: 3,
            });
            if (result.code == 401) {
                authStore.setLoginModal(true);
            }
        }
    } catch (err) {
        console.error(err)
    }
}
/**
 * 选择mycard 的商品
 * @param id 
 */
const chooseMycardItem = (index: number, id: number) => {
    curMycardItemId.value = appOrder.value.productId = id;
    // console.log(curMycardItemId.value, appOrder.value.productId ,id)
    curProduct.value = mycardItems.value[index];
    appOrder.value.activityId = mycardItems.value[index].activityId || 0;
    // 埋点: 选择mycard商品
    YKTrack?.track('click', {
        params: {
            id: '808',
            value1: id, // mycard商品id
        }
    })
}
/**
 * 切换国家服
 * @param param0 对应切换的key
 */
const SchemesChange = async ({ key }: any) => {
    curSchemes.value = schemesInfo.value.schemes[key];
    appOrder.value.SchemeId = curSchemes.value.id;
    if (curSchemes.value.id) {
        // 清空商品信息
        curProduct.value = {};
        appOrder.value.productId = undefined;
        curCategoryId.value = -1;
        mycardItems.value = [];
        setPayMethodVal({ id: 0, KitId: undefined });
        payMethodImgArray.value = [];
        await getPageInfoData(curSchemes.value.id, appOrder.value.roleId);
    }
    // 埋点: 选择方案
    YKTrack?.track('select', {
        params: {
            id: '802',
            value1: curSchemes.value.id, // 方案id
        }
    })
    console.log(curSchemes.value)
};

// 切换区服
const serverOptions = ref((() => {
    if (servers.value.length > 0) {
        return servers.value.map((item: any, index) => {
            let { id, name } = item;
            return {
                value: index,
                label: name,
                id,
                name,
            };
        });
    } else {
        return [];
    }
})());
// 切换区服: 切换服务器
const chooseServer = (index: any) => {
    console.log(`selected ${curServer.value.id}`);
    curServer.value = servers.value[index];
    // 这里Uid 对应
    appOrder.value.serverId = curServer.value.id || undefined;
    // 埋点: 选择区服
    YKTrack?.track('select', {
        params: {
            id: '1304',
            value1: curServer.value.id, // 区服id
        }
    })
};
// 切换区服: 搜索筛选
const filterOption = (input: string, option: any) => {
    return option.name?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 选择角色 
const chooseRole = (index: any) => {
    const { serverId, uid, uuid, roleName } = rolesList.value[index];
    curRole.value = rolesList.value[index];
    appOrder.value.serverId = serverId || undefined;
    appOrder.value.roleId = uid || undefined;
    appOrder.value.uid = uuid || undefined;
    appOrder.value.userName = roleName || undefined;
    // 更新当前角色信息
    getPageInfoData(curSchemes.value.id, uid || "");
};
// 切换角色：搜索赛选
const filterRoles = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


// 切换商品tab 类型：当前商品类型
const curCategoryId = ref<any>(-1);
// 当前tab筛选后 goods 是哪些？
const curProducts = computed(() => {
    if (pageInfo.value && pageInfo.value.products && pageInfo.value.products.length > 0) {
        return pageInfo.value.products.filter((item: any) => {
            if (curCategoryId.value == -1) {
                return item;
            } else if (item.categoryId == curCategoryId.value) {
                return item;
            }
        })
    } else {
        return [];
    }

});
// 切换商品tab 类型
const changeProductsType = (id: any) => {
    curCategoryId.value = id;
};
const orderFixedMainRef = ref<any>(null);
// 选择支付方式
const setPayMethodVal = (item: any, event?: any) => {
    //console.log(id);
    //console.log(curProduct.value.id)
    let { id, KitId, Name } = item;
    if (id == 60001) {  // mycard 支付的时候  获取mycard 数据
        if (mycardItems.value.length < 1) {
            setMycardItem(curSchemes.value.id);
        } else {
            curProduct.value = {};
            curMycardItemId.value = 0;
            appOrder.value.productId = undefined;
        }
        // mycard 手机端 滚动提示
        if (window.innerWidth <= 768) {
            const ele = orderFixedMainRef.value;
            ele.scrollTop += 80;
            console.log(ele.scrollTop)
        }

    } else if (payMethodVal.value == 60001) {  // 判断之前是否是mycard 支付，如果是，则清空mycard 选中的商品
        if (curMycardItemId.value != 0) {
            curProduct.value = {};
            curMycardItemId.value = 0;
            appOrder.value.productId = undefined;
        }
    } else {
        curMycardItemId.value = 0;
        // 给商品重新赋值
        appOrder.value.productId = curProduct.value.id;
    }
    payMethodVal.value = id;
    appOrder.value.kitId = KitId;
    // 通过获取当前事件，拿到对应图片
    if (event) {
        // 获取点击事件的目标元素，即按钮元素
        const parentNode = event.currentTarget;
        const imgUrlArray: any[] = []
        // 查找按钮内所有的图片元素
        const images = parentNode.querySelectorAll('img');
        images.forEach((img: any) => {
            imgUrlArray.push({
                src: img.src,
                name: Name
            });
            console.log(imgUrlArray); // 打印每个图片的 src 属性
        });
        payMethodImgArray.value = imgUrlArray;
    }
    if (item.id) {
        // 埋点: 选择支付方式
        YKTrack?.track('click', {
            params: {
                id: '804',
                value1: item.id, // 支付方式id
            }
        })
    }
};

//选择商品
const chooseProduct = (item: any) => {
    // 判断活动商品玩家是否达到最大购买限额
    if (item.activityNum && parseInt(item.activityNum) != 0 && parseInt(item.activityNum) == parseInt(item.activityMaxNum)) {
        return;
    }
    if (item.id) {
        appOrder.value.productId = item.id;
        appOrder.value.activityId = item.activityId ? parseInt(item.activityId) : 0;
    };
    curProduct.value = item;
    // 埋点: 选择商品
    YKTrack?.track('click', {
        params: {
            id: '803',
            value1: item.id, // 商品id
        }
    })
};
//手机端 选择商品后 弹窗弹出
const orderFixedShow = ref(false);
// 保存滚动位置
let scrollPosition = 0;
const chooseProduct_m = (item: any) => {
    // 判断活动商品玩家是否达到最大购买限额
    if (item.activityNum && parseInt(item.activityNum) != 0 && parseInt(item.activityNum) == parseInt(item.activityMaxNum)) {
        return;
    }
    if (item.id) {
        appOrder.value.productId = item.id;
        appOrder.value.activityId = item.activityId ? parseInt(item.activityId) : 0;
    };
    curProduct.value = item;
    orderFixedShow.value = true;
    // 让背景不滚动
    scrollPosition = window.scrollY;
    document.body.style.top = `-${scrollPosition}px`;
    document.body.style.position = 'fixed';
    // document.body.className = "modal_open";
    // document.getElementsByTagName("html")[0].className = 'body_fixed';
    // 埋点: 选择商品
    YKTrack?.track('click', {
        params: {
            id: '803',
            value1: item.id, // 商品id
        }
    })
};
const closeOrderFixed = () => {
    orderFixedShow.value = false;
    // 去掉背景锁定
    if (document.body.style.position == 'fixed') {
        document.body.style.position = '';
        document.body.style.top = '';
        document.documentElement.style.scrollBehavior = 'auto';
        window.scrollTo(0, scrollPosition);
        document.documentElement.style.scrollBehavior = 'smooth';
        // 判断是手机版下 选择 mycard 特殊处理
        if (payMethodVal.value == 60001) {
            payMethodVal.value = 0;
            appOrder.value.kitId = undefined;
        }
    }
    // document.body.classList.remove('modal_open');
    // document.getElementsByTagName("html")[0].classList.remove('body_fixed');
}
// 确定 角色ID
const getRoleDetail = async () => {
    // 区服没有选择
    if (!curServer.value.id) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.change-server'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }
    // 角色ID 没有输入
    if (!curRoleId.value) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.please-enter-the-role-id'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }
    roleLoginLoading.value = true;
    try {
        let param = {
            roleId: curRoleId.value,
            serverId: curServer.value.id,
            projectId: route.params.id,
        }
        let result: any = await getRole(param);
        roleLoginLoading.value = false;
        if (result && result.code == 200 || result.code == 0) {
            let { roleName, token, uid, uuid } = result.data;
            appOrder.value.roleId = uid || undefined;
            appOrder.value.uid = uuid || undefined;
            appOrder.value.userName = roleName;
            appOrder.value.serverId = curServer.value.id;
            // authStore.setAuthToken(token);
            authStore.setRoleAuthToken(route.params.id as string, token, curServer.value.id, curRoleId.value)
            // console.log(curServer.value)
            // 角色登录态
            roleLogin.value = true;

            //判断是否是活动页，并刷新内容
            refreshPageInfo();
            // 全局设置角色id
            YKTrack?.setRoleId(curRoleId.value);
            // 埋点: 角色登录结果 0:成功 1: 失败
            YKTrack?.track('server', {
                params: {
                    id: '1305',
                    value1: 0,
                }
            })
        } else {
            message.error({
                content: () => result.message,
                class: 'c_message_big',
                style: {
                    marginTop: '20vh',
                },
                duration,
            });
            if (result.code == 401) {
                authStore.setLoginModal(true);
            }
            // 埋点: 角色登录结果 0:成功 1: 失败
            YKTrack?.track('server', {
                params: {
                    id: '1305',
                    value1: 1,
                }
            });
        }
    } catch (err) {
        roleLoginLoading.value = false;
        console.error(err);
        // 埋点: 角色登录结果 0:成功 1: 失败
        YKTrack?.track('server', {
            params: {
                id: '1305',
                value1: 1,
            }
        })
    }
};
// 退出角色绑定
const setRoleLogout = () => {
    roleLogin.value = false;
    _serverId = null;
    _roleId = null;
    appOrder.value.roleId = undefined;
    curRoleId.value = "";
    authStore.logoutRole(route.params.id as string);

    if (_activityId > 0) {
        refreshPageInfo()
    }
    YKTrack?.setRoleId('')
}

// 生成订单
const orderLoading = ref<any>(false);
const submitData = ref<any>({});
const onSubmit = async () => {
    let { projectId, serverId, roleId, kitId, SchemeId, productId, uid } = appOrder.value;
    const paymentId = payMethodVal.value;
    // 未登录状态
    if (!isLogin.value && !roleLogin.value) {
        if (LoginType.value == 'role') {
            let errorMessage = t('pay.comfirm.account.roleId');
            // 错误弹窗
            message.error({
                content: () => errorMessage,
                class: 'c_message_big',
                style: {
                    marginTop: '20vh',
                },
                duration,
                onClose: () => {

                },
            });
        } else {
            authStore.setLoginModal(true);  //设置弹窗显示
        }
        return false
    }
    // 没有登录 或者 选择角色
    if (!roleId) {
        let errorMessage = t('pay.comfirm.account.roleId');
        if (isLogin.value && LoginType.value != 'role') {
            errorMessage = t('pages.pay.no-role-has-been-selected-yet');
        }
        // 错误弹窗
        message.error({
            content: () => errorMessage,
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }
    // 没有选择商品
    if (!productId) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.not-selected-shop-message-error'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }
    // 没有选择支付方式
    if (paymentId == 60001 && curMycardItemId.value == 0) {
        // 错误弹窗
        message.error({
            content: () => t('pay.payment.mycard.hint'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    } else if (!kitId || !paymentId) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.not-selected-payment-type-message-error'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }

    // 没有选择服务器
    if (serverId == undefined || SchemeId == undefined) {
        // 错误弹窗
        message.error({
            content: () => t('pages.pay.change-server'),
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        return false
    }
    // 保存用户行为 记住之前的支付选择
    userStore.setUserScheme(projectId, SchemeId);
    userStore.setUserPayment(projectId, SchemeId, { id: paymentId, kitId })
    // 请求接口
    orderLoading.value = true;
    let parameter = appOrder.value;
    // 判断是 adyen支付 提交弹窗logo
    if (paymentId == 60002) {
        submitData.value = {
            showLoading: true,
        }
    }

    let result: any = await postSubmit(parameter);
    orderLoading.value = false;
    // 删除弹窗
    closeOrderFixed();
    if (result && result.code == 200 || result.code == 0) {
        let { data } = result;
        console.log('data.orderId', data.orderId)
        appOrder.value.orderId = data.orderId;
        // 结构体配置
        const obj = {
            orderId: data.orderId,
            kitId: parameter.kitId,
            productId: parameter.productId,
            serverId: parameter.serverId,
            SchemeId: parameter.SchemeId,
        }

        // 设置订单重新跳转的 路径
        const cookieOrderPath = useCookie(`orderpath_${data.orderId}`, { maxAge: 60 * 60 * 24 });
        cookieOrderPath.value = route.path;  ///zh-Hant/pay/17
        // 使用支付组件 处理支付跳转
        paymentMethodsRef.value.handlePaymentRedirect(data, paymentId)

        // 埋点: 生单接口请求结果 0:成功 1:失败
        YKTrack?.track('server', {
            params: {
                id: '806',
                value1: 0,  // 请求结果
                value2: data.orderId, // 订单id
                value3: paymentId, // 支付方式
                value4: productId, // 商品id
                value5: projectId, // 游戏id
                value6: serverId, // 区服id
                value7: roleId, // 角色id
                value8: SchemeId, // 方案id
                value9: uid,  // 用户id
                value10: locale.value, // 语言
            }
        })
    } else {  //失败的情况
        message.error({
            content: () => result.message,
            class: 'c_message_big',
            style: {
                marginTop: '20vh',
            },
            duration,
        });
        if (paymentId == 60002) {
            submitData.value = {
                status: "fail",
            }
        }
        // 埋点: 生单接口请求结果 0:成功 1:失败
        YKTrack?.track('server', {
            params: {
                id: '806',
                value1: 1,
                value2: '', // 订单id
                value3: paymentId, // 支付方式
                value4: productId, // 商品id
                value5: projectId, // 游戏id
                value6: serverId, // 区服id
                value7: roleId, // 角色id
                value8: SchemeId, // 方案id
                value9: uid,  // 用户id
                value10: locale.value, // 语言
            }
        })
    }
};

const setHrefJump = (url: string) => {
    const aDom = document.createElement('a');
    aDom.target = '_blank';
    aDom.href = url;
    aDom.click();
};

const cookiesPolicyBottom = useState("cookiesPolicyBottom", () => 0)
// 滚动锁定
const handleScroll = () => {
    const paymentTop = payDescRef.value.getBoundingClientRect().top;
    // const paymentTop = payDescRef.value.offsetTop;
    // console.log("window.scrollY", window.scrollY)
    // console.log("window.innerHeight", window.innerHeight)
    // console.log("paymentTop", paymentTop)
    if (paymentTop <= window.innerHeight - 104) {
        isFixedPayment.value = false;
        cookiesPolicyBottom.value = 0;
    } else {
        isFixedPayment.value = true;
        if (window.innerWidth > 768) {
            cookiesPolicyBottom.value = 70;
        }
    }
}



const startCountdowns = (obj: any) => {
    console.log(obj.value)
    let array = obj.value.products ?? obj.value;
    array.forEach((item: any, index: number) => {
        if (item.activityCountdown > 0) {
            console.log("item.intervalId", item.intervalId)
            if (item.intervalId) {
                clearInterval(item.intervalId);
                item.intervalId = null;
            }
            const intervalId = setInterval(() => {
                // 使用 Vue.set 或 this.$set 来更新倒计时值
                // 确保是正确的响应式数据在更新
                item.activityCountdown--;
                // item.activityCountdown = item.activityCountdown - 10000;
                if (item.activityCountdown <= 0) {
                    clearInterval(intervalId);
                    item.activityCountdown = 0; // 倒计时结束
                }
            }, 1000);
            // 存储 intervalId
            item.intervalId = intervalId;
        }
    });
};
const clearCountDown = (obj: any) => {
    let array = obj.value.products ?? obj.value;
    array?.forEach((item: any) => {
        if (item.intervalId) {
            clearInterval(item.intervalId);
        }
    });
}

// vue加载
onMounted(async () => {
    // 获取页面信息  包括玩家之前选择
    try {
        let schemeId = null;
        const userSchemeId: string | null = userStore.getUserScheme(appOrder.value.projectId);
        if (userSchemeId) {
            const schemesObj = schemesInfo.value.schemes;
            for (let i = 0; i < schemesObj.length; i++) {
                if (schemesObj[i].id == userSchemeId) {
                    schemeId = userSchemeId;
                    curSchemes.value = schemesObj[i];
                    break;
                }
            }
        }
        schemeId = schemeId ? schemeId : curSchemes.value.id;
        if (schemeId) {
            // 清空商品信息
            appOrder.value.SchemeId = curSchemes.value.id = schemeId;
            curProduct.value = {};
            appOrder.value.productId = undefined;
            curCategoryId.value = -1;
            mycardItems.value = [];
            setPayMethodVal({ id: 0, KitId: undefined });
            await getPageInfoData(schemeId);
        }
        // 埋点: 选择方案
        YKTrack?.track('select', {
            params: {
                id: '802',
                value1: schemeId, // 方案id
            }
        })
        // 埋点: 充值页面加载结果 0:成功 1:失败
        YKTrack?.track('view', {
            params: {
                id: '801',
                value1: 0,
            }
        })
    } catch (err) {
        console.log(err);
        // 埋点: 充值页面加载结果 0:成功 1:失败
        YKTrack?.track('view', {
            params: {
                id: '801',
                value1: 1,
            }
        })
    }
    console.log("id pay onmounted")
    loginGetData(isLogin.value, true);
    // 如果是app打开，则帮助用户登录
    // if (LoginType.value == "role" || !isLogin.value) {
    //     helpPlayerRoleLogin();
    // }
    watch(() => isLogin.value, (newValue, oldValue) => {
        // console.log("isLogin===========================", newValue);
        //判断是否登录，账密登录后获取页面
        loginGetData(newValue);
    })

    handleScroll();
    window.addEventListener('scroll', handleScroll, { passive: true });

    //获取my card信息
    // setMycardItem(25)
    // console.log(route.name)
});

onBeforeUnmount(() => {
    // 清除倒计时
    clearCountDown(pageInfo);
    clearCountDown(mycardItems);
    console.log("removeEventListener")
    window.removeEventListener('scroll', handleScroll);
    cookiesPolicyBottom.value = 0;
});

</script>

<style lang="scss">
@import url(~/assets/styles/pay_common.scss);
</style>
<style lang="scss" scoped>
@import url(~/assets/styles/pay.scss);
</style>