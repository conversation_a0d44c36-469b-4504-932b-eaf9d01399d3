* {
    box-sizing: border-box;
}

.order_wrap {
    max-width: 530px;
    width: 100%;
    flex-shrink: 0;
    border-radius: 16px;
    background: #FFF;
    margin: 0 auto;
    --btn_line-height: 48px;
    --btn_border-radius: 30px
}

.title {
    line-height: 50px;
    color: #111;
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    border-bottom: 1px solid #ECECF0;
}

.order_content {
    padding: 0 30px 30px;
}


.order_detail {
    border-radius: 16px;
    border: 1px solid rgba(26, 26, 26, 0.1);
    background: rgba(234, 234, 248, 0.3);
    padding: 20px;
    line-height: 24px;
    font-size: 16px;
    color: rgba(1, 1, 1, 0.7);

    &.small {
        margin-top: 30px;
        font-size: 14px;
        line-height: 30px;
        padding: 10px 20px;
    }

    .order_detail_line {
        display: flex;
        padding: 8px 0;
        align-items: center;

        .left {
            margin-right: 16px;
            color: rgba(1, 1, 1, 0.5);
            flex-shrink: 1;
            white-space: nowrap;
        }
    }

    .order_detail_h5 {
        font-size: 16px;
        line-height: 24px;
        color: #111;
        font-weight: 500;
        text-align: center;
    }

    .alipay_price {
        font-size: 20px;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: var(--base_color);
    }

    .qrcode_wrap {
        text-align: center;
        position: relative;

        .qrcode_img {
            width: 200px;
            height: 200px;
            margin-top: 10px;
        }
        .logo {
            width: 30px;
            height: 30px;
            box-shadow: 0 0 6px #666;
            position: absolute;
            z-index: 10;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

.order_btn {
    line-height: var(--btn_line-height);
    cursor: pointer;
    color: #FFF;
    text-align: center;
    font-size: 20px;
    border-radius: var(--btn_border-radius);
    background: var(--base_color);
    border: 1px solid var(--base_color);
    margin-top: 30px;
}

.order_btn_line {
    margin-top: 40px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row-reverse;

    .order_btn_empty {
        line-height: var(--btn_line-height);
        color: rgba(1, 1, 1, 0.7);
        border: 1px solid rgba(1, 1, 1, 0.7);
        padding: 0 20px;
        border-radius: var(--btn_border-radius);
        cursor: pointer;
        margin: 0 20px 0 0;
    }

    .order_btn {
        flex: 1;
        margin: 0;
    }
}


.order_pay_time {
    color: #111;
    text-align: center;
    font-size: 18px;
    line-height: 40px;
    padding: 16px 10px;
    font-weight: bold;
}

.order_payment_txt {
    display: block;
    color: var(--base_color);
    text-align: center;
    font-size: 16px;
    padding: 12px 0;
    line-height: 24px;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

.order_hint {
    font-size: 12px;
    line-height: 20px;
    padding: 0px 20px 0;
    color: rgba(1, 1, 1, 0.4);

    p {
        margin-bottom: 0;
    }
}

.icon_order_status {
    margin: 40px auto 10px;
    width: 74px;
    height: 74px;

    &.success {
        background: url(~/assets/images/common/order_success.png) top center/100% 100% no-repeat;
    }

    &.fail {
        background: url(~/assets/images/common/order_fail.png) top center/100% 100% no-repeat;
    }
}

.order_status_txt {
    line-height: 40px;
    color: rgba(1, 1, 1, 0.8);
    text-align: center;
    font-size: 24px;
}

.order_status_hint_txt {
    line-height: 40px;
    font-size: 14px;
    color: rgba(1, 1, 1, 0.5);
    text-align: center;
}

.error_status {
    padding: 30px 0;

    .txt {
        margin: 0 auto;
        font-size: 20px;
        line-height: 50px;
        text-align: center;
    }

    .icon {
        width: 50%;
        max-width: 400px;
        padding-top: 30px;
        margin: 0 auto 0;
    }
}

@media screen and (max-width: 640px) {
    .title {
        line-height: 40px;
        font-size: 16px;
    }

    .icon_order_status {
        margin: 20px auto 0;
        width: 60px;
        height: 60px;
    }

    .order_status_txt {
        line-height: 36px;
        font-size: 18px;
    }

    .order_status_hint_txt {
        line-height: 32px;
    }

    .order_detail {
        border-radius: 12px;
        padding: 14px;
        line-height: 40px;
        font-size: 14px;

        &.small {
            margin-top: 10px;
            font-size: 12px;
            line-height: 30px;
            padding: 10px 20px;
        }

        .order_detail_line {

            .left {
                margin-right: 14px;
            }
        }
    }

    .order_content {
        padding: 0 20px 20px;
    }

    .order_wrap {
        --btn_line-height: 40px;
        --btn_border-radius: 20px;
    }

    .order_btn {
        font-size: 14px;
    }

    .order_btn_line {
        margin-top: 20px;

        .order_btn_empty {
            font-size: 12px;
        }
    }

    .order_pay_time {
        font-size: 14px;
    }

    .order_payment_txt {
        font-size: 12px;
    }
}