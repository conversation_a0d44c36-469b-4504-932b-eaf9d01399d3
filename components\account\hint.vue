<template>
    <!-- 提示 -->
    <div
        class="account_hint ac_common_box ac_c_mb20 ac_c_r20"
        v-if="!userInfo.email && showEmailHint"
    >
        <div class="ac_bell_wrap">
            <div class="icon_bell"></div>
        </div>
        <div class="ac_hint_right">
            <div class="h4">{{ $t('account.profile.emailVerificationAvailable') }}</div>
            <div class="txt">
                {{ $t('account.profile.emailVerificationImportance') }}
            </div>
            <div class="ac_hint_btn_wrap">
                <div class="ac_hint_btn active" @click="sendOperation(4)">{{ $t('account.profile.startEmailVerification') }}</div>
                <div class="ac_hint_btn" @click="showEmailHint = false">
                    {{ $t('account.profile.skipVerification') }}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 验证邮箱和手机 -->
    <accountCheckEmailAndPhone :operation="operation" @refreshUserInfo="refreshUserInfo"  @closeEdit="closeEdit"></accountCheckEmailAndPhone>
</template>
<script setup lang="ts">
import { useAccountStore } from '~/stores/account'
const accountStore = useAccountStore()
const userInfo = computed(() => accountStore.userInfo) as any;

const $emit = defineEmits(['sendOperation']);

// 验证的操作行为
const operation = ref<number>(0);

const refreshUserInfo = inject('refreshAccountUserInfo') as () => void;
// 关闭编辑
const closeEdit = () => {
    operation.value = 0;
};
// 发送操作指令
const sendOperation = (num: number) => {
    // $emit('sendOperation', operation);
    console.log(num);
    operation.value = num;
}

// 邮箱提示
const showEmailHint = ref<boolean>(true);
</script>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
@import url(~/assets/styles/account/profile.scss);
</style>
