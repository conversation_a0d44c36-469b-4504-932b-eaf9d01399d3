import type { postUpdateUserInfo } from "./login";

// api 接口
const API_URL = process.env.VITE_API_URL;
const CLIENT_URL = process.env.VITE_API_CLIENT_URL;
// 登录接口
const LOGIN_URL = process.env.VITE_LOGIN_URL;
// cms 接口
const CMS_URL = process.env.VITE_CMS_URL;
// cms 接口
const VITE_PBSDK_URL = process.env.VITE_PBSDK_URL;

interface jsonType {
    [key: string]: string;
}

const Login: jsonType = {
    /**
     *  功能: 统一登录接口
     *  method: post
     *  param: true
     *  pathParam: false
     */
    login: `${LOGIN_URL}/sdk.php/web/account_login`,
    /**
     *  功能: 统一登录接口
     *  method: post
     *  param: true
     *  pathParam: false
     */
    login_v2: `${LOGIN_URL}/sdk/v2/user/login`,
    /**
     *  功能: 获取个人信息
     *  method: get
     *  param: true
     *  pathParam: false
     */
    getUserInfo: `${LOGIN_URL}/sdk/v2/user/info`,
    /**
     *  功能: 账密登录
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postAccountLogin: `${LOGIN_URL}/sdk/v2/user/login`,
    /**
     *  功能: 用户名注册
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postRegister: `${LOGIN_URL}/sdk/v2/user/register`,
    /**
     *  功能: 头像选择列表
     *  method: get
     *  param: true
     *  pathParam: false
     */
    getAvatarList: `${LOGIN_URL}/sdk/v2/user/avatar_list`,
    /**
     *  功能: 头像选择列表
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postUpdateUserInfo: `${LOGIN_URL}/sdk/v2/user/update_info`,
    /**
     *  功能: 修改昵称
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postUpdateNickname: `${LOGIN_URL}/sdk/v2/user/update_nickname`,
    /**
     *  功能: 改密、绑定、换绑、解绑接口1
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postAuthStep1: `${LOGIN_URL}/sdk/v2/user/operation_step1`,
    /**
     *  功能: 改密、绑定、换绑、解绑接口2
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postAuthStep2: `${LOGIN_URL}/sdk/v2/user/operation_step2`,
    /**
     *  功能: 改密、绑定、换绑、解绑接口3
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postAuthStep3: `${LOGIN_URL}/sdk/v2/user/operation_step3`,
    /**
     *  功能: 改密、绑定、换绑、解绑接口4
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postAuthStep4: `${LOGIN_URL}/sdk/v2/user/operation_step4`,
    /**
     *  功能: 查询账号的安全验证方式
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postResetPasswordMethod: `${LOGIN_URL}/sdk/v2/user/query_security_verify_method`,
    /**
     *  功能: 【无需登录】改密、绑定、换绑、解绑接口1
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postNoLoginStep1: `${LOGIN_URL}/sdk/v2/user/no_login_operation_step1`,
    /**
     *  功能: 【无需登录】改密、绑定、换绑、解绑接口2
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postNoLoginStep2: `${LOGIN_URL}/sdk/v2/user/no_login_operation_step2`,
    /**
     *  功能: 【无需登录】改密、绑定、换绑、解绑接口3
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postNoLoginStep3: `${LOGIN_URL}/sdk/v2/user/no_login_operation_step3`,
    /**
     *  功能: 【无需登录】改密、绑定、换绑、解绑接口4
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postNoLoginStep4: `${LOGIN_URL}/sdk/v2/user/no_login_operation_step4`,
    /*
    * 取消注销
    * */
    cancelLogout: `${LOGIN_URL}/sdk/v2/user/cancel_logout`,
    
    /**
     *  功能: 获取三方登录配置列表
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postThirdConfigList: `${LOGIN_URL}/sdk/v2/user/third_config_list`,
};

const Api: jsonType = {
    /**
     *  功能: 获取角色列表
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getRoleList: `${API_URL}/v1/getRoleList`,
    /**
     *  功能: 获取角色信息
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getRole: `${API_URL}/v1/getRole`,

    /**
     *  功能: 生单
     *  method: post
     *  param: true
     *  pathParam: true
     */
    postSubmit: `${API_URL}/v1/submit`,

    /**
     *  功能: 获取充值页面配置
     *  method: get
     *  param: true
     *  pathParam: true
     */
    getPageInfo: `${API_URL}/v1/pageInfo`,

    /**
     *  功能: 拉取首页列表
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getProjectList: `${API_URL}/v1/projectList`,
    /**
     *  功能: 区服列表
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getServerList: `${API_URL}/v1/serverList`,
    /**
     *  功能: 订单列表
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getOrderList: `${API_URL}/v1/orders`,
    /**
     *  功能: 获取订单详情
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getOrderDetail: `${API_URL}/v1/order`,
    /**
     *  功能: 获取订单详情(用于webview)
     *  method: get
     *  param: false
     *  pathParam: false
     */
    getWebviewOrderDetail: `${API_URL}/v1/webview_order`,
    /**
     *  功能: 关闭订单
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postCloseOrder: `${API_URL}/v1/closeOrder`,
    /**
     *  功能: 获取mycard商品
     *  method: post
     *  param: true
     *  pathParam: false
     */
    getMycardItem: `${API_URL}/v1/getMycardItem`,
    /**
     *  功能: webview获取充值渠道列表
     *  method: get
     *  param: false
     *  pathParam: true
     */
    getWebviewChannelList: `${API_URL}/v1/webview_channel`,
    /**
     *  功能: webview获取商品价格
     *  method: get
     *  param: false
     *  pathParam: true
     */
    getWebviewProductPrice: `${API_URL}/v1/webview_product`,
    /**
     *  功能: webview生单
     *  method: post
     *  param: true
     *  pathParam: false
     */
    postWebviewSubmit: `${API_URL}/v1/webview_submit`,
    /**
     *  功能: webview获取充值币种列表
     *  method: get
     *  param: false
     *  pathParam: true
     */
    getWebviewCurrency: `${API_URL}/v1/webview_currency`,
    /**
     *  功能: 获取用户 活动商品的购买数量
     *  method: get
     *  param: true
     *  pathParam: false
     */
    getActivityRemainingBuy: `${API_URL}/v1/remaining_purchase_count`,
};

const Support: jsonType = {
    /**
     *  功能: cmd获取广告位
     *  site_id	integer	非必须 网站ID
     *  identifier	string	非必须 广告位标识
     *  locale	string	非必须 多语言标识
     */
    ad: `${CMS_URL}/wapi/ad/list`,
};

const AccountUpdate: jsonType = {
    /**
     *  邮箱验证码登录
     *  https://yapi.yokaverse.com/project/79/interface/api/6746
     */
    emailLogin: `${VITE_PBSDK_URL}/sdk/v2/user/email_login`,
    /**
     *  发送邮箱登录验证码
     *  https://yapi.yokaverse.com/project/79/interface/api/6782
     */
    sendEmailLoginCode: `${VITE_PBSDK_URL}/sdk/v2/user/send_email_login_code`,
    /**
     *  待升级账号列表
     *  https://yapi.yokaverse.com/project/79/interface/api/7014
     */
    toUpgradeAccount: `${VITE_PBSDK_URL}/sdk/v2/user/to_upgrade_account`,
    /**
     *  设置邮箱账号和密码
     *  https://yapi.yokaverse.com/project/79/interface/api/6970
     */
    boundAccount: `${VITE_PBSDK_URL}/sdk/v2/user/bound_account`,
};

const Main: jsonType = {
    /**
     *  功能: cmd获取banner
     *  site_id	integer	非必须 网站ID
     *  identifier	string	非必须 广告位标识
     *  locale	string	非必须 多语言标识
     */
    getMainBanners: `${CMS_URL}/wapi/ad/list`,
}

export default { API_URL, LOGIN_URL, Login, Api, Support ,Main ,AccountUpdate};
