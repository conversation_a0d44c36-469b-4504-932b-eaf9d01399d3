<template>
    <a-modal
        v-model:visible="showEdit"
        :footer="null"
        class="ac_header_choice_pop"
        @onCancel="closeEdit"
        @onOk="closeEdit"
        :destroyOnClose="true"
        :keyboard="true"
        :afterClose="() => { closeEdit() }"
    >
        <!-- 编辑昵称 -->
        <div
            class="ac_profile_edit ac_common_box ac_c_mb20 ac_c_r20"
            v-if="$props.type == 'nickname'"
        >
            <div class="h3">{{ $t('account.editNickname.title') }}</div>
            <div class="ac_profile_edit_form">
                <p class="ac_profile_edit_desc">
                    {{ $t('account.editNickname.changeLimit') }}
                </p>
                <p class="ac_profile_edit_desc">
                    <span class="bold">{{ $t('account.editNickname.currentNickname') }}：</span
                    >{{ userInfo.nick_name || $t('account.editNickname.notSet') }}
                </p>
                <div class="input_box_wrap">
                    <div class="input_box">
                        <a-input
                            class="input"
                            tabindex="0"
                            v-model:value="oNickName"
                            :placeholder="$t('account.editNickname.enterNewNickname')"
                            :maxlength="64"
                            allow-clear
                        >
                        </a-input>
                    </div>
                </div>
                <div class="login_error_hint">{{ oErrorHint }}</div>
                <p class="ac_profile_edit_desc">{{ $t('account.editNickname.nicknameLength') }}</p>
                <p class="ac_profile_edit_desc" style="margin-bottom: 20px;">
                    {{ $t('account.editNickname.privacyWarning') }}
                </p>
                <div class="ac_pop_btn_line">
                    <div
                        class="c_small_btn empty"
                        @click="closeEdit"
                    >
                        {{ $t('account.common.cancel') }}
                    </div>
                    <a-button
                        class="c_small_btn blue"
                        :loading="submitLoading"
                        :disabled="submitLoading"
                        type="primary"
                        @click="submitUpdateNickName"
                        >{{ $t('account.common.confirm') }}</a-button
                    >
                </div>
            </div>
        </div>
        <!-- 个人信息 -->
        <div
            v-if="$props.type == 'personalInfo'"
            class="ac_profile_edit ac_common_box ac_c_mb20 ac_c_r20 ac_profile_edit_all"
        >
            <div class="h3">{{ $t('account.personalInfo.title') }}</div>
            <div class="ac_profile_edit_form">
                <div class="input_box">
                    <a-input
                        class="input"
                        tabindex="0"
                        v-model:value="first_name"
                        :placeholder="$t('account.personalInfo.enterFirstName')"
                        :maxlength="64"
                        allow-clear
                    >
                    </a-input>
                </div>
                <div class="input_box">
                    <a-input
                        class="input"
                        tabindex="1"
                        v-model:value="last_name"
                        :placeholder="$t('account.personalInfo.enterLastName')"
                        :maxlength="64"
                        allow-clear
                    >
                    </a-input>
                </div>
                <div class="input_box">
                    <a-dropdown class="gender_dropdown">
                        <template #overlay>
                            <a-menu @click="handleMenuClick">
                                <a-menu-item
                                    v-for="item in genderList"
                                    :key="item.key"
                                    >{{ item.value }}</a-menu-item
                                >
                            </a-menu>
                        </template>
                        <a-button>
                            {{ genderName || $t('account.personalInfo.selectGender') }}
                            <DownOutlined />
                        </a-button>
                    </a-dropdown>
                </div>
                <div class="input_box" :class="{'dn': !userInfo.is_edit_birth}">
                    <a-date-picker
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                        v-model:value="birth_date"
                        :inputReadOnly="true"
                        :placeholder="$t('account.personalInfo.selectBirthday')"
                        :disabled-date="disabledDate"
                    />
                </div>
                <div class="ac_profile_edit_desc" v-if="userInfo.is_edit_birth">
                    {{ $t('account.personalInfo.birthdayWarning') }}
                </div>
                <div class="login_error_hint">{{ oErrorHint }}</div>
                <a-button
                    class="c_small_btn blue save_btn"
                    :loading="submitLoading"
                    :disabled="submitLoading"
                    type="primary"
                    @click="submitUpdatePersonalInfo"
                    >{{ $t('account.common.save') }}</a-button
                >
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { postUpdateNickname, postUpdateUserInfo } from "~/api/login";
import { useAccountStore } from "~/stores/account";
import moment from "moment";
import { useI18n } from "vue-i18n";
const { locale } = useI18n();

const { t } = useI18n();
// 获取用户信息
const accountStore = useAccountStore();
const userInfo:any = computed(() => accountStore.userInfo);
// 控制弹窗
const showEdit = ref<boolean>(false);
// 昵称
const oNickName = ref<string>("");
// 错误提示
const oErrorHint = ref<string>("");
// 提交状态
const submitLoading = ref<boolean>(false);
// 名
const first_name = ref<string>("");
// 姓
const last_name = ref<string>("");
// 性别
const genderName = ref<string>("");
const gender = ref<number>(0); // 0 未选择 1 男 2 女 3 保密 4 其他
// 生日
const birth_date = ref<string>("");

// 刷新页面
const $emit = defineEmits(["refreshUserInfo", "closeEdit"]);

const $props = defineProps({
    userInfo: {
        type: Object,
        default: () => {},
    },
    type: {
        type: String,
        default: "",
    },
});

// 生日选择限制
const disabledDate = (current: any) => {
    return current && current > moment().endOf("day");
};

// 修改昵称
const updateNickName = async () => {
    try {
        oErrorHint.value = "";
        const res: any = await postUpdateNickname(oNickName.value);
        console.log(res);
        if (res.code == 200 || res.code == 0) {
            // oErrorHint.value = "修改成功";
            $emit("refreshUserInfo"); // 刷新页面
            closeEdit();
        } else {
            oErrorHint.value = res.message;
            if (res.code == 401) {
                message.error({
                    content: res.message,
                    class: "c_message_big",
                    style: {
                        marginTop: "20vh",
                    },
                    duration: 3,
                    onClose: () => {
                        navigateTo(`/${locale.value}/account/login`);
                    },
                });
            }
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};
// 提交修改昵称
const submitUpdateNickName = async () => {
    oNickName.value = oNickName.value.trim();
    if (!oNickName.value) {
        oErrorHint.value = t('account.nickname.empty');
        return;
    }
    if(oNickName.value.length < 3){
        oErrorHint.value = t('account.nickname.tooShort');
        return;
    }
    await updateNickName();
};

// 性别选择
const genderList = [
    {
        key: "1",
        value: t('account.personalInfo.male'),
    },
    {
        key: "2",
        value: t('account.personalInfo.female'),
    },
    {
        key: "3",
        value: t('account.personalInfo.secret'),
    },
    {
        key: "4",
        value: t('account.personalInfo.other'),
    },
];
const handleMenuClick = (e: any) => {
    genderName.value = genderList[e.key - 1].value || "";
    gender.value = e.key;
};

// 提交修改个人信息
const submitUpdatePersonalInfo = async () => {
    if (!first_name.value) {
        oErrorHint.value = t('account.personalInfo.enterFirstName');
        return;
    }
    if (!last_name.value) {
        oErrorHint.value = t('account.personalInfo.enterLastName');
        return;
    }
    if (!gender.value) {
        oErrorHint.value = t('account.personalInfo.selectGender');
        return;
    }
    if (!birth_date.value) {
        oErrorHint.value = t('account.personalInfo.selectBirthday');
        return;
    }
    oErrorHint.value = "";
    try {
        submitLoading.value = true;
        const res: any = await postUpdateUserInfo({
            first_name: first_name.value,
            last_name: last_name.value,
            gender: gender.value as 1 | 2 | 3 | 4,
            // birth_date: birth_date.value,
            birth_date: moment(birth_date.value).format("YYYY-MM-DD"),
        });
        submitLoading.value = false;
        if (res.code == 200 || res.code == 0) {
            $emit("refreshUserInfo"); // 刷新页面
            closeEdit();
        } else {
            oErrorHint.value = res.message;
            if (res.code == 401) {
                message.error({
                    content: res.message,
                    class: "c_message_big",
                    style: {
                        marginTop: "20vh",
                    },
                    duration: 3,
                    onClose: () => {
                        navigateTo(`/${locale.value}/account/login`);
                    },
                });
            }
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};

// 关闭弹窗
const closeEdit = () => {
    showEdit.value = false;
    oNickName.value = "";
    first_name.value = "";
    last_name.value = "";
    oErrorHint.value = "";
    $emit("closeEdit");
};

onMounted(() => {
    watch(
        () => $props.type,
        (newVal) => {
            if (newVal == "nickname") {
                showEdit.value = true;
            } else if (newVal == "personalInfo") {
                first_name.value = userInfo.value.first_name || "";
                last_name.value = userInfo.value.last_name || "";
                gender.value = userInfo.value.gender || 0;
                genderName.value = userInfo.value.genderName || "";
                birth_date.value = userInfo.value.birth_date; // 使用 moment 将 YY-MM-DD 转化成毫秒
                console.log(moment(userInfo.value.birth_date).toDate());
                showEdit.value = true;
            } else {
                showEdit.value = false;
            }
        },
    );
});
</script>
<style lang="scss">
.ac_profile_edit_all {
    .ant-btn,
    .ant-picker {
        border: none;
        box-shadow: none;
    }
    .ant-picker {
        width: 100%;
    }
}
</style>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
.ac_profile_edit {
    background: #fff;
    width: 100%;
    max-width: 530px;
    .save_btn {
        width: 100%;
        line-height: 50px;
        height: 50px;
        border-radius: 25px;
    }
}
.ac_profile_edit_desc {
    font-size: 12px;
    margin-bottom: 6px;
    color: #5f5f5f;
    span.bold {
        color: #333;
        font-weight: bold;
    }
}
.ac_profile_edit_form {
    padding: 20px 40px;
}

.input_box_wrap {
    margin: 30px 0 0;
}
.input_box {
    height: 50px;
    border: 1px solid var(--gray_color);
    border-radius: 10px;
    margin-bottom: 16px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    &:last-child {
        margin-bottom: 4px;
    }
    .input {
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        color: #111;
        border: none;
        flex: 1;

        &.ant-input-status-error {
            border-color: #ff4d4f;
        }

        &::placeholder {
            color: #999;
        }

        &:focus {
            border: none;
            background: #f0f0f0;
        }
    }
}
.gender_dropdown {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media screen and (max-width: 768px) {
    .ac_profile_edit_form {
        padding: 20px;
    }
    .input_box {
        padding: 0 10px;
    }
}
</style>