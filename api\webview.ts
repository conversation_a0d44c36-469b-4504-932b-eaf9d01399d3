/**
 * 从Nuxt应用程序中获取并使用$axios实例。
 * @param options 请求配置选项。
 * @returns 返回封装后的axios请求。
 */
import URLS from "~/api/uri";
import { $axios } from "~/utils/axiosCustiom";

/**
 * @function webview获取充值币种列表
 * @method get
 * @param projectId app传入的项目id
 * @param channelId 渠道id
 * @param language 语言
 * @returns 返回一个Promise，解析为充值渠道列表
 */
const getWebviewCurrency = (projectId: string, channelId: string, language: string) => {
    return $axios({
        method: "GET",
        headers: {
            'Accept-Language': language
        },
        url: `${URLS.Api.getWebviewCurrency}/${projectId}/${channelId}/${language}`,
    });
};

/**
 * @function webview获取充值渠道列表
 * @method get
 * @param projectId 充值中心的项目id
 * @param currency 币种
 * @returns 返回一个Promise，解析为充值渠道列表
 */
const getWebviewChannelList = (projectId: string, currency: string, language: string) => {
    return $axios({
        method: "GET",
        headers: {
            'Accept-Language': language
        },
        url: `${URLS.Api.getWebviewChannelList}/${projectId}/${currency}`,
    });
};

/**
 * @function webview获取商品价格
 * @method get
 * @param projectId 充值中心的项目id
 * @param productCode 商品编码
 * @param currency 币种
 * @returns 返回一个Promise，解析为商品价格,展示到界面
 */
const getWebviewProductPrice = (projectId: number, productCode: string, currency: string, language: string) => {
    return $axios({
        method: "GET",
        headers: {
            'Accept-Language': language
        },
        url: `${URLS.Api.getWebviewProductPrice}/${projectId}/${productCode}/${currency}`,
    });
};

/**
 * @function webview生单
 * @method post
 * @param projectId 项目id
 * @param kitId 套件id
 * @param productId 商品id
 * @param currency 币种
 * @param serverId 服id
 * @param uid 用户id
 * @param roleId 角色id
 * @param language 语言
 * @param isTest 是否测试
 * @param userName 用户名
 * @returns
 */
const postWebviewSubmit = (data: any) => {
    return $axios({
        method: "POST",
        headers: {
            'Accept-Language': data?.language || 'en'
        },
        url: `${URLS.Api.postWebviewSubmit}`,
        data
    });
};

export {
    getWebviewCurrency,
    getWebviewChannelList,
    getWebviewProductPrice,
    postWebviewSubmit,
};