// import antdEnUS from 'ant-design-vue/es/locale-provider/en_US'
// import momentEU from 'moment/locale/eu'
import global from './id/global'

import menu from './id/menu'
import setting from './id/setting'
import user from './id/user'
import account from './id/account'
import pages from './id/pages'

const components = {
    //antLocale: antdEnUS,
    momentName: 'eu',
    // momentLocale: momentEU
}

export default {
    message: '-',
    'login.hint.txt':"Website ini hanya mendukung login dengan akun email official.",
    'login.hint.txt2':"Untuk akun login pihak ketiga lainnya (seperti login Google, Facebook, Apple, dll.), silakan pergi ke dalam game terlebih dahulu dan binding akun email Anda.",
    'login.error.email':'Email atau Password Akun salah, silakan masukkan kembali',
    'login.email.name':'Login Email Top Up',
    'layouts.usermenu.dialog.title': 'Message',
    'layouts.usermenu.dialog.content': 'Are you sure you would like to logout?',
    'layouts.userLayout.title': 'Ant Design is the most influential web design specification in Xihu district',
    'pay.credit.card':'Kartu Kredit',
    'pay.role.comfirm':'Yakin',
    'pay.how.get.roleId':'Bagaimana cara menemukan ID karakter?',
    'pay.comfirm.account':'Konfirmasi Pesanan',
    'pay.comfirm.account.roleId':'Silakan pastikan ID karakter atau login akun yang akan di TopUp',
    'page.fetch.fail':'Info halaman gagal dimuat, silakan kembali ke { XX }Beranda{ YY }',
    'login.current.name':'Login Terakhir',
    'login.role.name':'Karakter',
    'footer.policy.cookies':'Kebijakan Cookies',
    'footer.policy.refund':'Kebijakan Pengembalian Dana',
    'footer.policy.website':'Syarat Penggunaan Situs Web',
    'footer.policy.privacy':'Kebijakan Privasi Online',
    'footer.policy.website2':'Perjanjian Layanan Game',
    'footer.policy.privacy2':'Kebijakan Privasi',
    'ux.txt.agree':'Terima Semua',
    'api.login.lose':'Sesi login kedaluwarsa, silakan login kembali',
    'api.login.request.fail':'Permintaan data gagal, silakan coba lagi nanti',
    'common.policy.cookies':'Kami menggunakan cookie untuk memberikan pengalaman kunjungan yang lebih baik kepada Anda. Beberapa cookie merupakan cookie yang penting untuk kelayakan pengoperasian situs web ini; Cookie lain membantu kami memahami cara Anda dalam penggunaan situs web, sehingga kami dapat meningkatkannya. Klik "Terima Semua" untuk menyetujui Kebijakan Cookie dan Kebijakan Privasi kami.',
    'common.comming.soon':'Mohon nantikan',
    'pay.payment.mycard.select':'Silakan pilih produk di bawah ini untuk metode pembayaran Mycard',
    'pay.payment.mycard.hint':'Silakan pilih produk Mycard',
    'pay.order.not.paid':'Pesanan belum selesai dibayar',
    'pay.order.comfirming.loading':'Pesanan sedang dikonfirmasi',
    'pay.order.comfirming.txt':'Pesanan sedang dikonfirmasi, silakan periksa status pesanan di daftar pesanan nanti. Jika mengalami masalah isi ulang, silakan "hubungi layanan pelanggan"',
    'pay.role.login.hint':'Saat ini, game hanya mendukung pengisian ulang menggunakan ID karakter.',
    'cookies.policy.agree.all':'Terima semua cookie yang opsional',
    'cookies.policy.disagree.all':'Tolak semua cookie yang opsional',
    'cookies.policy.manage':'Kelola cookie',
    'cookies.policy.confirm.my':'Konfirmasi pilihan saya',
    'cookies.policy.function.txt':'Cookie ini membantu kami menganalisis penggunaan situs web Anda untuk mengevaluasi dan meningkatkan kinerja kami. Mereka juga dapat digunakan untuk memberikan pengalaman pelanggan yang lebih baik. Misalnya: mengingat detail login Anda, atau memberikan informasi kepada kami tentang penggunaan situs web kami.',
    'cookies.policy.function.title':'Cookie fungsional',
    'cookies.policy.analysis.title':'Cookie analitik',
    'cookies.policy.analysis.txt':'Cookie ini membantu kami memahami interaksi pengunjung dengan situs web. Kami dapat mengetahui jumlah kunjungan, sumber lalu lintas, dan waktu yang dihabiskan pengunjung di situs web, yang dapat digunakan untuk mengukur dan mengoptimalkan penggunaan situs web.',
    'cookies.policy.base.title':'Cookie yang diperlukan',
    'cookies.policy.base.txt':'Cookie ini diperlukan untuk operasi normal situs web, memungkinkan Anda untuk menggunakan fungsi situs web secara normal, dan tidak akan dinonaktifkan dari sistem kami.',
    'cookies.policy.base.status':'Selalu aktif',
    'cookies.policy.center':'Pusat Pengelolaan Cookie',
    'common.get.more':"Pelajari Lebih Lanjut",
    'pay.order.status.alipay.hint':'Alipay Scan-to-Pay',
    'pay.maintenance.hint': 'Halaman sedang dalam perbaikan. Fitur pembayaran sementara tidak dapat digunakan.',
    'pay.apple.only.hint': 'Metode pembayaran ini hanya didukung pada perangkat Apple. Silakan pilih metode pembayaran lain.',
    'pay.order.status.processing': 'Pesanan sedang diproses. Silakan coba lagi nanti.',
    'login.search.noData': 'Tidak ditemukan hasil',
    'login.choose.games':'Memilih permainan',
    ...components,
    ...global,
    ...menu,
    ...setting,
    ...user,
    ...account,
    ...pages
}
