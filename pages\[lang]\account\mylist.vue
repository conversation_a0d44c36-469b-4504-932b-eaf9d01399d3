<template>
    <div class="account_wrap">
        <accountSidebar :index="1" ref="accountSidebarRef"></accountSidebar>
        <!-- 右边区域 -->
        <div class="account_right">
            <div class="h2">{{ $t('account.orders.myOrders') }}</div>
            <!-- 提示 -->
            <accountHint></accountHint>
            <!-- 我的列表 -->
            <accountMylist></accountMylist>
        </div>
    </div>
</template>

<script setup lang="ts">
const { t } = useI18n();
const accountSidebarRef = ref<any>(null);


// 刷新用户信息
const refreshUserInfo = () => {
    accountSidebarRef.value.getUserInfoFun();
};
provide('refreshAccountUserInfo', refreshUserInfo);
// 配置首页样式
const diyWrapStyle = useState("diyWrapStyle", () => "");
onMounted(() => {
    diyWrapStyle.value = "background:#fff;";
});
</script>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
</style>
