// @ts-ignore
// import { useAsyncData } from "#imports"; // 根据你的 Nuxt 设置调整导入路径

// 登录态管理
import { useAuthStore } from "~/stores/auth";
import { showLog } from "~/constants/show_log";

interface CustomAsyncDataOptions
    extends Omit<Parameters<typeof useAsyncData>[1], "default"> {
    method?: "GET" | "POST" | "PUT" | "DELETE";
    url: string;
    data?: any;
    headers?: Record<string, string>;
    body?: any;
    params?: any;
    query?: any;
    lazy?: boolean;
    timeout?: number;
    interceptors?: {
        request?: (config: any) => any;
        response?: (response: any) => any;
        error?: (error: any) => any;
    };
}
/**
 * `useCustomAsyncData` 是一个自定义 Hook，它扩展了 `useAsyncData` 并支持请求和响应拦截器。
 *
 * 示例用法：
 *
 * ```typescript
 * import { useCustomAsyncData } from './useCustomAsyncData';
 *
 * const { data, pending, error, refresh } = useCustomAsyncData('posts', {
 *   url: '/api/posts',
 *   method: 'GET',
 *   headers: { Authorization: '++++ my-token' },
 *   data:{
 *       name:'hello world'
 *   },
 *   interceptors: {
 *     request: (config) => {
 *       console.info('Request Interceptor:', config);
 *       config.headers.Authorization = ' ++++++ your-token';
 *       return config;
 *     },
 *     response: (response) => {
 *       console.info('Response Interceptor:', response);
 *       return response;
 *     },
 *     error:(err) =>{
 *       console.info('Error Interceptor:', err);
 *     }
 *   },
 * });
 * ```
 *
 * @param key - 缓存数据的唯一标识符。
 * @param options - 自定义选项，包括请求方法、URL、数据、拦截器等。
 */
export async function useAsyncDataCustom<T>(
    key: string,
    options: CustomAsyncDataOptions
) {
    const $serverLog: any = useNuxtApp().$serverLog;
    const {
        method = "GET",
        url,
        data,
        headers,
        interceptors,
        timeout,
        lazy = false,
    } = options;

    // 使用 useAsyncData 进行数据获取
    return await useAsyncData(key, async () => {
        const authStore = useAuthStore();
        const auth_token = useCookie("auth_token", { path: "/", domain: process.env.VITE_COOKIE_DOMAIN });
        const Authorization = authStore.auth_token ? `Bearer ${authStore.auth_token}`: (auth_token.value? `Bearer ${auth_token.value}`: "");
        let config: Omit<CustomAsyncDataOptions, "url"> = {
            method,
            timeout: timeout || 10000,
            headers: {
                "Content-Type": "application/json",
                Authorization,
                "Accept-Language": authStore.appLang || "en",
                ...headers,
            },
            lazy,
        };

        // 应用请求拦截器
        if (interceptors?.request) {
            config = interceptors.request(config);
        }
        // 处理参数
        if (data) {
            if (method.toUpperCase() === "GET") {
                config.query = data;
            } else {
                config.body = JSON.stringify(data);
                config.params = data;
            }
        }
        const startTime = Date.now();
        try {
            // 请求日志
            $serverLog.log(
                `[Request] ${method.toUpperCase()} ${url} ${data? '[params] ' + JSON.stringify(data):''} [Headers] ${JSON.stringify(config.headers) || config.headers} [Date] ${new Date().toLocaleString()}`,
                
            );
        } catch (e) {
            $serverLog.error(e);
        }

        try {
            const response: any = await $fetch(url, config);

            // 应用响应拦截器
            if (interceptors?.response) {
                return interceptors.response(response);
            }

            // 响应日志
            const endTime = Date.now();
            const duration = endTime - startTime;
            try {
                // 请求日志
                $serverLog.log(`[Response Duration] ${url}  ${duration}ms`);
                // 错误情况
                if (!(response.code == 200 || response.code == 0)) {
                    $serverLog.warn(
                        `[Response Data]`,
                        JSON.stringify(response) || response
                    );
                }
            } catch (e) {
                $serverLog.error(e);
            }
            // 返回值
            if(showLog[url]){
                $serverLog.log(`[Response Data] ${url}： ${response? JSON.stringify(response) || response:''}`);
            }
            return response;
        } catch (error) {
            // 应用错误拦截器
            if (interceptors?.error) {
                return interceptors.error(error);
            }

            // 错误日志
            $serverLog.error(
                `[Response Error] ${method.toUpperCase()} ${url} - Error:`,
                error
            );
            throw error;
        }
    });
}
