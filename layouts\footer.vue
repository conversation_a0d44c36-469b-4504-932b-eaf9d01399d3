<template>
    <div class="pay-layout-footer">
        <div class="pay-layout-footer-content">
            <div class="footer-content-box">
                <div class="pay-layout-footer-links">
                    <a href="javascript:;">
                        <img src="~/assets/images/common/play_best_white.png" alt="PLAY BEST" />
                    </a>
                </div>
                <div class="select_lang">
                    <!-- select language -->
                    <select-lang></select-lang>
                </div>
            </div>
            <ul class="pay-layout-footer-nav clearfix">
                <li>
                    <a href="javascript:;" @click="goPolicy('cookies')" data-track-id="201">{{
                        $t('footer.policy.cookies') }}</a>
                </li>
                <li>
                    <a href="javascript:;" @click="goPolicy('refund')" data-track-id="202">{{ $t('footer.policy.refund')
                        }}</a>
                </li>
                <li>
                    <a href="javascript:;" @click="goPolicy('website')" data-track-id="203">{{
                        $t('footer.policy.website2') }}</a>
                </li>
                <li>
                    <a href="javascript:;" @click="goPolicy('privacy')" data-track-id="204">{{
                        $t('footer.policy.privacy2') }}</a>
                </li>
            </ul>
            <div class="pay-layout-footer-copyright">© 2024 Playbest All Rights Reserved.</div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const { locale } = useI18n();
const goPolicy = (label: string) => {
    navigateTo(`/${locale.value}/policy/${label}`)
}
</script>

<style lang="scss" scoped>
.pay-layout-footer {
    position: relative;
    z-index: 99;
    width: 100%;
    background: #000000;
    border-radius: 0;
}

.pay-layout-footer-content {
    padding: 40px 20px;
    box-sizing: border-box;
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
}

.footer-content-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .select_lang {
        color: #fff;
        position: absolute;
        right: 0;
    }

    .pay-layout-footer-links {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        &>a {
            cursor: default;
            transition: all 0.3s;
        }

        img {
            max-width: 200px;
        }
    }
}


.pay-layout-footer-line {
    margin-top: 30px;
    width: 100%;
    height: 1px;
    background: #242424;
    border-radius: 0px 0px 0px 0px;
}

.pay-layout-footer-nav {
    padding: 0;
    margin: 30px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    li {
        padding: 0 20px;
        line-height: 16px;
        border-right: 1px solid rgba(194, 194, 205, 0.3);
        flex-shrink: 0;
        margin-bottom: 14px;

        &:last-child {
            border-right: none;
        }
    }

    a {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
    }
}

.pay-layout-footer-copyright {
    color: #B8B8B8;
    text-align: center;
    font-size: 14px;
    line-height: 40px;
    padding-top: 10px;
}

@media screen and (max-width: 768px) {
    .footer-content-box {
        flex-direction: column-reverse;

        .select_lang {
            margin: 0 20px 20px;
            position: static;
        }
    }

    .pay-layout-footer-nav {

        li {
            border-right: none;
            padding: 0 10px;
        }
    }
}
</style>