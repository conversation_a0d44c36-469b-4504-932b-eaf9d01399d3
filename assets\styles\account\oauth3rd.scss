
// 第三方登录
.oauth2-3rd {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #ECECF0;
    //padding: 23px 52px 0;
    //margin: 0 62px;
    padding: 30px 26px 0;
    margin: 0 31px;

    .third_media_item {
        width: 60px;
        height: 60px;
        cursor: pointer;

        &.google {
            background: url("~/assets/images/3rd/google.png") no-repeat center center/100% 100%;
        }

        &.apple {
            background: url("~/assets/images/3rd/apple.png") no-repeat center center/100% 100%;
        }

        &.facebook {
            background: url("~/assets/images/3rd/facebook.png") no-repeat center center/100% 100%;
        }

        &.line {
            background: url("~/assets/images/3rd/line.png") no-repeat center center/100% 100%;
        }
    }
}


.third_media {
    .h3 {
        color: #111;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-weight: bold;
        line-height: 54px;
        border-bottom: 1px solid var(--gray_color);
    }

    .choose_media {
        padding: 25px 0 0;

        .input_box {
            height: 50px;
            border-radius: 25px;
            margin-bottom: 16px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            background: #F9F9FA;

            .input {
                height: 40px;
                line-height: 40px;
                padding: 0 10px;
                color: #111;
                border: none;
                flex: 1;
                background: #F9F9FA;

                &.ant-input-status-error {
                    border-color: #ff4d4f;
                }

                &::placeholder {
                    color: #999;
                }

                &:focus {
                    border: none;
                    background: #f0f0f0;
                }
            }

        }
    }

    .choose_media_list_wrap {
        margin-right: -10px; // 添加负边距
        padding-right: 10px; // 添加内边距来补偿内容区域
        height: 290px;
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 4px;
            background: #eee;
            border-radius: 4px;
            position: absolute;
            right: 10px;
            top: 0;
            bottom: 0;
        }

        &::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 4px;
        }
    }

    .choose_media_list {
        border-radius: 12px;
        border: 1px solid var(--gray_color);
        min-height: 290px;


        .choose_media_item {
            padding: 4px;
            border-bottom: 1px solid var(--gray_color);

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .choose_media_item_li {
        padding: 10px 20px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
            background: #EAEAF8;
            border-radius: 12px;

            .txt {
                color: #3A58A9;
            }
        }

        img {
            width: 40px;
            height: 40px;
            border-radius: 6px;
        }

        .txt {
            margin-left: 24px;
            font-size: 18px;
            color: #111;
            overflow: hidden;
            overflow-wrap: break-word;
        }
    }
}


.third_media_empty {
    margin: 0 auto;
    text-align: center;
    padding: 20px 0;

    img {
        width: 270px;
        height: 200px;
        display: block;
        margin: 0 auto;
    }

    .empty_txt {
        font-size: 18px;
        color: #111;
        margin-top: -40px;
    }
}
.third_media_close {
    position: absolute;
    left: 24px;
    top: 16px;
    width: 40px;
    height: 40px;
    cursor: pointer;
}

@media screen and (max-width: 768px) {

    // 第三方登录
    .oauth2-3rd {
        padding: 30px 0 0;

        .third_media_item {
            width: 50px;
            height: 50px;
            cursor: pointer;
        }
    }

    .third_media {
        .choose_media_item_li {
            padding: 10px;

            .txt {
                margin-left: 14px;
                font-size: 16px;
            }
        }
    }

}