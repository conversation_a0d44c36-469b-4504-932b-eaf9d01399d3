import URLS from "~/api/uri";
import {$axios} from "~/utils/axiosCustiom";

let accessToken = '';
/**
 * 邮箱验证码登录
 * @returns Promise<String?>
 */
const setAccountUpdateToken = (token: string) => {
    accessToken = token;
};
/**
 * 邮箱验证码登录
 * @returns Promise<String?>
 */
const emailLogin = (app_id: Number, game_id: string, email: string, code: string, lang: string) => {
    return $axios({
        url: URLS.AccountUpdate.emailLogin,
        method: 'POST',
        data: {
            "app_id": app_id,
            "game_id": game_id,
            "email": email,
            "code": code,
        },
        headers: {
            'Accept-Language': lang
        },
    }).then(res => {
        if (res.code === 0) {
            accessToken = res.data.token;
        }
        return res;
    });
};
/**
 * 发送邮箱登录验证码
 * @returns Promise<bool>
 */
const sendEmailLoginCode = (app_id: Number, game_id: string, email: string, lang: string) => {
    return $axios({
        url: URLS.AccountUpdate.sendEmailLoginCode,
        method: 'POST',
        data: {
            "app_id": app_id,
            "game_id": game_id,
            "email": email,
        },
        headers: {
            'Accept-Language': lang
        },
    });
};
/**
 * 待升级账号列表
 * @returns 返回一个Promise<dynamic>
 */
const toUpgradeAccount = (app_id: Number, game_id: string, game_name: string, channel_id: string, lang: string) => {
    return $axios({
        url: URLS.AccountUpdate.toUpgradeAccount,
        method: 'POST',
        data: {
            "app_id": app_id,
            "game_id": game_id,
            "game_name": game_name,
            "channel_id": channel_id,
        },
        headers: {
            "Authorization": `Bearer ${accessToken}`,
            'Accept-Language': lang
        },
    });
};
/**
 * 设置邮箱账号和密码
 * @returns 返回一个Promise
 */
const boundAccount = (app_id: Number, game_id: string, username: string, password: string, confirm_password: string, lang: string) => {
    return $axios({
        url: URLS.AccountUpdate.boundAccount,
        method: 'POST',
        data: {
            "app_id": app_id,
            "game_id": game_id,
            "username": username,
            "password": password,
            "confirm_password": confirm_password,
        },
        headers: {
            "Authorization": `Bearer ${accessToken}`,
            'Accept-Language': lang
        },
    });
};

export {
    setAccountUpdateToken,
    emailLogin,
    sendEmailLoginCode,
    toUpgradeAccount,
    boundAccount,
};