<template>
    <div class="result_webview">
        <div class="order_fixed_model show"></div>
        <div class="order_fixed_wrap show" style="display: block; height: 90%;">
            <div class="order_result_wrap order_fixed_box">
                <CloseOutlined class="order_f_close" @click="close" />
                <order-status ref="order_status" :json="orderDetail" :other-page="true"
                    :is-webview="true"></order-status>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// 不使用布局
definePageMeta({
    layout: false
})
// 多语言问题
const { locale, t } = useI18n();
const route = useRoute();
const orderDetail = useState(() => ({}))
const order_status = ref<any>(null)
const { $jsbridge } = useNuxtApp() as any;

onMounted(() => {
    $jsbridge.switchBarChange(4);
    const { roleId, uid, orderId } = route.query;
    orderDetail.value = {
        roleId, uid, orderId
    }
})

// 调用order-status中的closed方法,判断订单支付状态
const close = () => {
    order_status.value?.closed()
}
</script>

<style lang="scss" scoped>
@import url('~/assets/styles/pay.scss');
@import url('~/assets/styles/components/order.scss');

.result_webview {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: transparent;

    .wrap_normal {
        height: 100%;
    }
}
</style>