// https://nuxt.com/docs/api/configuration/nuxt-config

// 环境变量

import { loadEnv } from "vite";
import removeConsole from "vite-plugin-remove-console"; // 移除console.log
import { visualizer } from "rollup-plugin-visualizer"; //打包分析工具
import viteCompression from "vite-plugin-compression"; // 打包压缩gzip
interface VITE_ENV_CONFIG {
    VITE_HOST: string;
    VITE_BASE_URL: string;
}
// import "./common.css";
// const envScript = process.env.npm_lifecycle_script?.split(" ") || [];
// const envName = envScript[envScript.length - 1]; // 通过启动命令区分环境
// const envData = loadEnv(envName, "") as unknown as VITE_ENV_CONFIG;
// 加载环境变量
const envName = process.env.NODE_ENV;
const envData = loadEnv(envName as string, process.cwd());
// console.log("process.env", process.env);
console.log("envName", envName);
// console.log("VITE_API_URL", process.env.VITE_API_URL,process.env.API_URL,process.env.NUXT_API_URL);
console.log("envData", envData);
// const env = import.meta.env;
// console.log("VITE_API_URL",env.VITE_API_URL);
// console.log(import.meta.env.VITE_APP_BASE_API)

export default defineNuxtConfig({
  devtools: {
      enabled: true,

      timeline: {
          enabled: true,
      },
  },

  // SEO sitemap.xml 添加
  // sitemap: {
  //     hostname: "https://www.example.com",
  //     gzip: true,
  //     routes: async () => {
  //         // 语言列表
  //         const languages = [
  //             "en",
  //             "zh-Hans",
  //             "zh-Hant",
  //             "id",
  //             "th",
  //             "ja",
  //             "ko",
  //         ];

  //         // 生成多语言 URL
  //         const routes: any[] = [];
  //         languages.forEach((lang) => {
  //             routes.push({
  //                 url: `/${lang}/home`,
  //             });
  //         });
  //         console.log("routes", routes);
  //         return routes;
  //     },
  // },

  app: {
      buildAssetsDir: "static", //修改站点资产的文件夹名称，默认是_nuxt
      head: {
          title: "PLAYBEST",
          charset: "utf-8",
          meta: [
              {
                  name: "viewport",
                  content:
                      "width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1",
              },
              {
                  name: "theme-color", // safari 浏览器配置
                  content: "#000",
              },
              {
                  name: "theme-color", // safari 浏览器配置
                  content: "#000",
              },
          ],
          // 根据环境动态添加 vconsole
          script: [
              // 添加 Promise.allSettled polyfill
              {
                  innerHTML: `
                  if (!Promise.allSettled) {
                      const rejectHandler = reason => ({ status: 'rejected', reason });

                      const resolveHandler = value => ({ status: 'fulfilled', value });

                      Promise.allSettled = function (promises) {
                          const convertedPromises = promises.map(p => Promise.resolve(p).then(resolveHandler, rejectHandler));
                          return Promise.all(convertedPromises);
                      };
                  }
              `,
                  type: "text/javascript",
              },
              // 添加 globalThis polyfill
              {
                  innerHTML: `
                      if (typeof globalThis === 'undefined') {
                          window.globalThis = window;
                      }
                  `,
                  type: "text/javascript",
              },
          ],
      },
  },

  // 客户端请求代理
  nitro: {
      devProxy: {
          "/useApi": {
              // target: 'http://*************:8000/',
              target: "https://payment-test.playbest.net/",
              changeOrigin: true,
              prependPath: true,
          },
      },
  },

  // 该配置用于服务端请求转发
  routeRules: {
      "/useApi/**": {
          // proxy: "http://*************:8000/**",
          proxy: "https://payment-test.playbest.net/**",
      },
      // "https://cms-api-test.playbest.net/**": {
      //     proxy: "http://cms-api-http-port:8000/**",
      // },
      "https://cms-api.playbest.net/**": {
          proxy: "http://cms-api-http-port:8000/**",
      },
      "https://payment.playbest.net/v1/**": {
          proxy: "http://yopay-backend-interface-http-port:8020/v1/**",
      },
      // 路由配置
      // "/": { prerender: true },
      "/:lang/home": { swr: 120 }, //将缓存标头添加到服务器响应中，并将其缓存在服务器或反向代理上，以获得可配置的 TTL（生存时间）。Nitro 的 node-server 预设能够缓存完整的响应。当 TTL 过期时，将发送缓存的响应，同时在后台重新生成页面。如果使用 true，则添加不带 MaxAge
      "/:lang/pay/:id": { isr: 300 },
      "/:lang/my/list": { ssr: false },
      "/404": { prerender: true },
      "/fail_order_loading": { prerender: true },
      "/:lang/account/profile": { ssr: false },
      "/:lang/account/mylist": { ssr: false },
      "/:lang/account/security": { ssr: false },
      "/:lang/main": { swr: 120 },
      "/:lang/about/company": { swr: 120 },
      "/:lang/game/list": { swr: 120 },
  },

  vite: {
      define: {
          "process.env": { ...process.env, ...envData }, //重制环境变量
      },
      resolve: {
          alias: {
              "@": "/",
          },
      },
      build: {
          target: ["es2015", "chrome52", "safari11"], // Vite 的目标设置为 ES5 兼容
          sourcemap: false, // 输出.map文件
          cssTarget: "chrome52",
          rollupOptions: {
              output: {
                  format: "es",
                  manualChunks(id) {
                      // 判断是否为第三方依赖，将其拆分到 vendor 中
                      if (id.includes("node_modules")) {
                          // 这里代码可以优化一下，但是我懒，我相信你一定可以的！
                          if (
                              id.includes("ant-design-vue") ||
                              id.includes("antd")
                          ) {
                              return "ant-design-vue";
                          }
                      } else {
                          // 翻译
                          if (
                              id.includes("locales/lang/") ||
                              id.includes("locales/index")
                          ) {
                              return "locales";
                          }
                      }
                  },
              },
          },
      },
      plugins: [
          removeConsole({ custom: ["console.log()", "debugger"] }),
          // 打包分析
          visualizer({
              open: import.meta.env.VITE_NODE_ENV == "development", // 注意这里要设置为true，否则无效
              gzipSize: true, // 分析图生成的文件名
              brotliSize: true, // 收集 brotli 大小并将其显示
              filename: "analysis.html", // 分析图生成的文件名
          }),
          // GZIP 打包压缩
          viteCompression({
              algorithm: "gzip", // 使用 gzip 算法
              ext: ".gz", // 生成 .gz 文件
              threshold: 1024, // 只压缩大于 1KB 的文件
              deleteOriginFile: false, // 不删除原始文件
              // 可以添加更多的选项
          }),
      ],
      // loader 全部配置 scss 公共变量
      // css: {
      //   preprocessorOptions: {
      //     scss: {
      //       additionalData: `@import "~/assets/styles/global.scss";`
      //     }
      //   }
      // }
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
      analyze: true,
      transpile: ["sass", "sass-loader"], // 如果遇到命名空间冲突或需要转译的问题，可能需要将它们添加到 transpile
      // vite: {
      //   css: {
      //     preprocessorOptions: {
      //       scss: {
      //         additionalData: `@import "~@/assets/styles/variables.scss";`, // 如果你有全局变量文件
      //       },
      //     },
      //   },
      // },
  },

  modules: ["@ant-design-vue/nuxt", "@nuxtjs/i18n"],

  // routeRules: {
  //   '/test': '/en/haha', // 将根路径 '/' 重定向到 '/home'
  // },
  i18n: {
      // locales: [
      //   { name: "English", code: "en", iso: "en-US", dir: "ltr" },
      //   { name: "español", code: "es", iso: "es-LA", dir: "ltr" },
      //   { name: "En français", code: "fr", iso: "fr-FR", dir: "ltr" },
      //   { name: "العربية", code: "ar", iso: "ar-EG", dir: "rtl" },
      // ],
      // defaultLocale: "en",
      detectBrowserLanguage: false,
      // 👇 Reference the Vue I18n config file
      vueI18n: "./i18n.config.js",
  },

  antd: {
      // Options
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
      // 如果你的全局 SCSS 文件位于 src/assets 目录下
      "~/assets/styles/global.scss",
      "~/assets/styles/yc_captcha.scss",
      // "ant-design-vue/dist/antd.css",
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: ["~/plugins/jsbridge.client.js"],

  // Auto import components: https://go.nuxtjs.dev/config-components
  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  // buildModules: [
  //   // https://go.nuxtjs.dev/typescript
  //   '@nuxt/typescript-build',
  // ],
  components: true,

  compatibilityDate: "2025-08-14"
});