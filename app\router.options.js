// import { routes } from "vue-router/auto-routes"

const customRouter = [
  // {
  //   path: '/',
  //   redirect: '/en/home',
  //   name: 'home',
  //   // meta: { title: 'menu.home', hideHeader: true },
  //   // redirect: '/:lang/index',

  // },
  {
    path: '/:lang/result/:roleId/:uid/:orderId',
    // redirect: '/en/home',
    name: 'result',
    component: () => import('~/views/result.vue'),
    // meta: { title: 'menu.home', hideHeader: true },
    // redirect: '/:lang/index',

  },
  {
    path: '/:lang/policy/:label',
    // redirect: '/en/home',
    name: 'policy',
    component: () => import('~/pages/[lang]/policy.vue'),
  },
  {
    path: '/activity/:id',
    // redirect: '/en/home',
    name: 'activity',
  },
]

export default {
  routes: (_routes) => _routes.concat(customRouter)
}