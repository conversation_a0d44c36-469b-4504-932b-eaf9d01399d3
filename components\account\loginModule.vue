<template>
    <div class="login_pop_main" v-if="!isShowChooseGame">
        <div class="c_logo_blue"></div>
        <div class="tab_name">{{ $t("account.login.accountLogin") }}</div>
        <div class="input_wrap">
            <div class="input_box">
                <div class="c_icon_email"></div>
                <a-input class="input" :status="oEmailStatus" v-model:value="oEmail"
                    :placeholder="$t('user.email.required')" :maxlength="50" allow-clear>
                </a-input>
            </div>
            <div class="input_box">
                <div class="c_icon_password"></div>
                <a-input-password class="input" :status="oPasswordStatus" v-model:value="oPassword"
                    :placeholder="$t('user.password.required')" :maxlength="50" allow-clear />
            </div>
        </div>
        <div class="login_error_hint">{{ oErrorHint }}</div>
        <a-button class="login_btn" type="primary" :loading="loginLoading" :disabled="loginLoading"
            @click="loginAccount('', '')">{{ $t('pages.pay.login') }}</a-button>
        <div class="login_other">
            <div class="btn" @click="resetpassword">{{ $t('account.login.forgotPassword') }}</div>
            <div class="btn">|</div>
            <div class="btn" @click="registerAccount">{{ $t('account.login.registerNow') }}</div>
        </div>

        <div class="oauth2-3rd">
            <div class="third_media_item" v-for="item, index in thirdConfigList" :key="index" :class="item.mediaIcon"
                @click="showChooseGame(item.game)">
            </div>
            <!-- <div class="third_media_item google"></div>
            <div class="third_media_item apple"></div>
            <div class="third_media_item facebook"></div>
            <div class="third_media_item line"></div> -->
        </div>
        <!-- <div class="login_hint">
            <h4>{{ $t('login.hint.txt') }}</h4>
            <p>{{ $t('login.hint.txt2') }}</p>
        </div> -->
    </div>
    <!-- 三方登录 -->
    <div class="third_media" v-if="isShowChooseGame">
        <div class="third_media_close" @click="isShowChooseGame = false"><left-outlined /></div>
        <div class="h3">{{ $t('login.choose.games') }}</div>
        <div class="choose_media">
            <div class="input_box">
                <a-input class="input input_search_no_border" v-model:value="searchGameName" @change="searchGameHandler"
                    :placeholder="$t('pages.index.support.search')" :maxlength="50" />
                <search-outlined style="font-size: 24px;color: #999; width: 60px; text-align: right;" />
            </div>
        </div>
        <div class="choose_media_list_wrap">
            <div class="choose_media_list">
                <div class="third_media_empty" v-if="searchGameResult.length == 0">
                    <img src="~/assets/images/3rd/empty.png" alt="">
                    <div class="empty_txt">
                        {{ $t('login.search.noData') }}
                    </div>
                </div>
                <div class="choose_media_item" v-for="item, index in searchGameResult" :key="index" v-else>
                    <div class="choose_media_item_li" @click="click3rdLogin([item])">
                        <img :src="item.game_icon" :alt="item.game_name[locale]" />
                        <div class="txt">{{ item.game_name[locale] }}</div>
                    </div>
                </div>
                <!-- <div class="choose_media_item">
                    <div class="choose_media_item_li">
                        <img src="https://yopay-**********.cos.accelerate.myqcloud.com/5bf1fd98-18fc-44d6-96d8-3f5d2dc70f2c.jpg"
                            alt="" />
                        <div class="txt">三国杀名将传</div>
                    </div>
                </div> -->
            </div>
        </div>


        <!-- <a-button class="login_btn" type="primary" :loading="loginLoading" :disabled="loginLoading"
            @click="loginAccount('', '')">{{ $t('account.common.confirm') }}</a-button> -->
    </div>
</template>


<script lang="ts" setup>
import { login, postThirdConfigList, getThirdConfigList } from '~/api/login';
// 引入验证码
import { useYcCaptcha } from "~/utils/ycCaptcha";
const { initYcCaptcha } = useYcCaptcha();
// 登录态管理
import { useAuthStore } from '~/stores/auth';
// @ts-ignore
import { Base64 } from 'js-base64';
const authStore = useAuthStore();
// 登录态  loading
const loginLoading = ref<boolean>(false);

const isShowChooseGame = ref<boolean>(false);

const { locale, t } = useI18n();
const { $validator } = useNuxtApp();
const $v: any = $validator;

const showModal = (status: boolean) => {
    authStore.setLoginModal(status);
};

const $props = defineProps({
    needRedirect: {
        type: Boolean,
        default: false,
    },
});

// 登录弹窗控制器
const $emit = defineEmits(['changeAction', 'closeAndOpenDeregister']);

// const oEmail = ref<string>('<EMAIL>');
const oEmail = ref<string>('');
const oEmailStatus = ref<'error' | 'warning' | ''>('');
// const oPassword = ref<string>('yoka1234');
const oPassword = ref<string>('');
const oPasswordStatus = ref<'error' | 'warning' | ''>('');
const oErrorHint = ref<string>('');  // 错误提示

// 注册
const registerAccount = () => {
    if ($props.needRedirect) {
        navigateTo(`/${locale.value}/account/register`);
    } else {
        $emit('changeAction', 'register');
    }
}
// 忘记密码
const resetpassword = () => {
    if ($props.needRedirect) {
        navigateTo(`/${locale.value}/account/resetpassword`);
    } else {
        $emit('changeAction', 'resetpassword');
    }
}
// 提交接口
const loginAccount = async (token: string = '', captcha_verification: string = '') => {

    oEmail.value = oEmail.value?.trim();
    oPassword.value = oPassword.value?.trim();
    // console.log(oEmail.value, oPassword.value);
    // console.log($v.isEmail(oEmail.value));
    // 验证邮箱
    if (!oEmail.value) {
        oErrorHint.value = t('user.account.required');
        return;
    }
    // 判断邮箱
    if (oEmail.value && oEmail.value.length < 4) {
        oEmailStatus.value = "error";
        oErrorHint.value = t('user.account.error');
        return;
    } else {
        oErrorHint.value = '';
        oEmailStatus.value = "";
    }
    // 密码
    if (oPassword.value) {
        // if (oPassword.value.length < 8 || oPassword.value.length > 20) {
        //     oPasswordStatus.value = "error";
        //     oErrorHint.value = t('account.register.passwordLengthInvalid');
        //     return;
        // }
        oPasswordStatus.value = "";
        oErrorHint.value = '';
    } else {
        oErrorHint.value = t('account.register.passwordEmpty');
        oPasswordStatus.value = "error";
        return;
    }
    // 提交
    loginLoading.value = true;
    try {
        const result: any = await login(oEmail.value, oPassword.value, locale.value, token, captcha_verification);
        loginLoading.value = false;
        if (result && result.code == 200 || result.code == 0) {
            // 注销状态：1待审核 2延期未审核 3驳回
            if ([1, 2, 3, 4].includes(result.data.logout_status)) {
                $emit('closeAndOpenDeregister', result.data);
                return
            }

            let { token, username } = result.data;
            // userStore.setAppOrder(result.data);
            authStore.login(username, token);
            if ($props.needRedirect) {
                navigateTo(`/${locale.value}/account/profile`);
            } else {
            }
        } else {
            if (result.code == 2044) {
                initYcCaptcha(loginAccount);
            } else {
                oErrorHint.value = result.message;
            }
        }
        console.log(result)
    } catch (error) {
        loginLoading.value = false;
        console.error(error)
    }
}
// 三方登录结果  在api/login.ts中配置
const thirdConfigList = computed(() => authStore.loginThirdConfig);

interface InvokeQuery {
    game_id: string;
    app_id: string;
    channel_id: string;
    redirect_uri: string;
    lang: string;
}

// const invokeQuery = ref<InvokeQuery | {}>({});
// 搜索的value
const searchGameName = ref<string>('');
// 搜索的结果
const searchGameResult = ref<any>([]);
// 当前游戏列表
const curGameResult = unref<any>([]);
// 第三方登录 跳转链接
const click3rdLogin = async (game: any) => {
    console.log(game);
    // 判断是否在loading状态
    if (game.length == 1) {
        // 跳转游戏
        const { game_id, app_id, channel_id, redirect_url, login_id } = game[0];
        const invokeQueryState: InvokeQuery = {
            game_id,
            app_id,
            channel_id,
            redirect_uri: window.location.href,
            lang: locale.value
        }
        console.log("🚀 ~ click3rdLogin ~ invokeQueryState:", invokeQueryState)
        // 自定义回调地址
        const state = Base64.encode(JSON.stringify({
            login_type: parseInt(login_id),
            from: 'pb',
            ...invokeQueryState,
        }));
        window.location.href = redirect_url + `&state=${state}`;
        console.log("🚀 ~ click3rdLogin ~ redirect_url:", redirect_url + `&state=${state}`);
    } else if (game.length > 1) {
        // 有多个游戏 弹出选择游戏
        searchGameResult.value = curGameResult.value = game;
        isShowChooseGame.value = true;
    }
}
// 显示选择游戏详情页
const showChooseGame = (game: any) => {
    searchGameResult.value = curGameResult.value = game;
    isShowChooseGame.value = true;
}
// 搜索游戏
const searchGameHandler = (e: any) => {
    console.log(e.target.value);
    searchGameName.value = e.target.value;
    // 搜索
    const curGameResultArr: any[] = [];
    curGameResult.value.forEach((item: any) => {
        if (item.game_name[locale.value]?.toLowerCase().includes(searchGameName.value)) {
            curGameResultArr.push(item);
        }
    });
    searchGameResult.value = curGameResultArr;
}


onMounted(async () => {
    // 获取第三方配置
    if (thirdConfigList.value[0].game.length == 0) {
        getThirdConfigList();
    }
})

</script>
<style lang="scss">
.login_pop_wrap {
    width: 530px !important;

    // .login_pop_main {
    //     width: 490px !important;
    // }

    .ant-modal-content {
        width: 530px;
        border-radius: 20px;
        background: #fff;
        padding: 40px 30px !important;

        .ant-input-affix-wrapper-status-error {
            border: 1px solid #ff4d4f !important;
        }
    }

    .ant-modal-body {
        padding: 0;
    }
}

.input_search_no_border {
    border: none;
    box-shadow: none;

    &:focus {
        border: none;
        box-shadow: none;
        background: none;
    }
}

@media screen and (max-width: 768px) {
    .login_pop_wrap {
        width: 350px !important;

        .login_pop_main {
            width: 310px !important;
        }

        .ant-modal-content {
            width: 350px;
            padding: 20px !important;
        }
    }
}
</style>
<style lang="scss" scoped>
@import url("~/assets/styles/account/login.scss");
@import url("~/assets/styles/account/oauth3rd.scss");
</style>