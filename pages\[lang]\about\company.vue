<template>
    <div class="content">
        <!-- 顶部banner -->
        <div class="header-banner">
            <div class="banner-inner">
                <div class="banner-logo">
                    <img src="~/assets/images/main/site_logo.png" alt="PLAY BEST" class="logo">
                </div>
                <div class="banner-title">{{ $t("pages.index.about.slogan") }}</div>
            </div>
        </div>

        <!-- 关于我们介绍 -->
        <div class="about-us-module-box" ref="aboutRef">
            <div class="about-us-inner" :class="{ ['about-us-inner--active']: isInView }">
                <div class="about-figuration">
                    <main-title-box :name="$t('pages.index.main.about.title')" :title='$t("pages.index.about.title")'
                        align="left"></main-title-box>
                    <div class="about-desc-slider-box">
                        <a-carousel touch-move :arrows="true" :autoplay="true" :autoplay-speed="20000">
                            <template #prevArrow>
                                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="52" viewBox="0 0 26 52"
                                    fill="none">
                                    <path d="M24 2L2 26L24 50" stroke="white" stroke-width="3" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </template>
                            <template #nextArrow>
                                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="52" viewBox="0 0 26 52"
                                    fill="none">
                                    <path d="M2 2L24 26L2 50" stroke="white" stroke-width="3" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </template>
                            <div v-for="(article, index) of articles" :key="index" class="article-item">
                                <!-- {{ article.content }} -->
                                <div v-html="article.content"></div>
                            </div>
                        </a-carousel>
                    </div>
                </div>
                <div class="about-figures">
                    <main-responsive-image wrapper-class="company-icon-wrapper left" :src="leftImg"
                        :scale="`${500 / 280 * 100}%`" class="company-icon"></main-responsive-image>

                    <div class="company-icon-wrapper right">
                        <main-responsive-image :src="topImg" scale="64.2%" class="company-icon"></main-responsive-image>
                        <main-responsive-image :src="bottomImg" scale="100%"
                            class="company-icon"></main-responsive-image>
                    </div>

                </div>
            </div>
            <div class="link-box" v-if="locale === 'ja'">
                <a href="https://www.playbest.net/ykcms/ja/15/privacy.html" target="_blank">プライバシーポリシー</a>
                <a href="https://www.playbest.net/ykcms/ja/14/privacy.html" target="_blank">サービス利用規約</a>
                <a href="https://www.playbest.net/ykcms/ja/13/privacy.html"
                    target="_blank">資金決済法に基づく表示</a>
                <a href="https://www.playbest.net/ykcms/ja/12/privacy.html"
                    target="_blank">特定商取引法に基づく表示</a>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">

import { getAboutUsDescList } from '~/api/main';
import { inView } from '~/utils/inView';

import leftImg from '~/assets/images/main/280x500.jpg';
import topImg from '~/assets/images/main/280x180.jpg';
import bottomImg from '~/assets/images/main/280x280.jpg';

const articles = ref<any>([]);
const { locale } = useI18n();



try {
    const aboutUsDescList = await getAboutUsDescList(locale.value);
    articles.value = computed(() => formatCmsData(aboutUsDescList, {
        "content": "ad_desc",
    }, {
        richText: true
    })).value;

    console.log("🚀 ~ articles.value:", articles.value)
} catch (e) {
    console.log("🚀 ~ e:", e)

}




const aboutRef = ref(null);

let isInView = ref(false);
let unObserver: Function;

onMounted(() => {
    if (aboutRef.value) {
        const onEnter = () => {
            isInView.value = true;
            if (typeof unObserver === 'function') {
                unObserver()
            }
            return undefined;
        }

        const options: InViewOptions = {
            root: undefined,
            amount: 0,
        }

        unObserver = inView(aboutRef.value, onEnter, options)
    }
});

onUnmounted(() => {
    if (typeof unObserver === 'function') {
        unObserver()
    }
})





</script>

<style lang="scss" scoped>
.header-banner {
    position: relative;
    width: 100%;
    padding-top: 27.4%;
    height: 0;
    background: url("~/assets/images/main/about_us_top_banner_bg_tiny.png") no-repeat center top;
    background-size: cover;
}

.banner-inner {
    position: absolute;
    text-align: center;
    width: 100%;
    top: 0;
    padding-top: 11.2%;
    left: 50%;
    transform: translate(-50%, 0);
    color: #fff;
}

.banner-logo {
    font-size: 50px;
    opacity: 0;
    animation: slideRight 0.4s 0.5s ease forwards;
}

.banner-title {
    font-size: 50px;
    opacity: 0;
    font-weight: 700;
    animation: slideLeft 0.4s 0.5s ease forwards;
}

.link-box {
    text-align: center;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 0 20px;
    margin: 60px auto 0;

    a {
        color: #4361EE;
        font: 16px;
        margin: 5px 20px;
        display: block;
        &:hover {
            text-decoration: underline;
        }
    }
}


@keyframes slideRight {
    from {
        opacity: 0;
        transform: translate(-100px, 0);
    }

    to {
        opacity: 1;
        transform: translate(0, 0);
    }
}

@keyframes slideLeft {

    from {
        opacity: 0;
        transform: translate(100px, 0);
    }

    to {
        opacity: 1;
        transform: translate(0, 0);
    }
}


.about-us-module-box {
    background: url('~/assets/images/main/about_page_module_bg_tiny.png') center top no-repeat;
    background-size: 100% auto;
    padding: 120px 0 84px;

    :deep {

        .title-box-title,
        .title-box-divider {
            margin: 0;
        }
    }

    :deep(.title-box-name) {
        margin-bottom: 10px;
    }
}

.about-desc-slider-box {
    position: relative;
    min-height: 394px;
    padding: 30px 0;

    &::after {
        content: '';
        display: block;
        position: absolute;
        z-index: -1;
        background: url('~/assets/images/main/article_bg_tiny.png') no-repeat right top;
        background-size: 100% 100%;
        left: -200px;
        top: 30px;
        bottom: 30px;
        right: 0;
        border-radius: 30px;
    }

    :deep(.ant-carousel .slick-track) {
        display: flex;
        align-items: stretch;
    }

    :deep(.ant-carousel .slick-initialized .slick-slide) {
        display: flex;
        align-items: center;
        height: unset;
    }

    :deep(.slick-arrow) {
        width: 22px;
        height: 48px;
    }

    :deep(.ant-carousel .slick-dots) {
        justify-content: left;
        margin-left: 0;
    }

    :deep(.ant-carousel .slick-prev) {
        left: -65px;
    }

    :deep(.ant-carousel .slick-next) {
        left: calc(80% + 40px);
    }
}

.about-us-inner {
    display: flex;
    opacity: 0;
    gap: 60px;
    justify-content: center;
    transform: translate(0, 100px);
    transition: all 0.5s 0.4s ease;

    &--active {
        opacity: 1;
        transform: translate(0, 0);
    }
}

.article-item {
    width: 100%;
    padding: 60px 20% 60px 0;
    color: #FFF;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    word-break: break-word;
}

.about-figuration {
    width: 39%;
}

.about-figures {
    display: flex;
    width: 39%;
    gap: 20px;
    align-items: center;
}

.company-icon-wrapper {
    display: flex;
    gap: 20px;
    width: 100%;
    max-width: 280px;

    &.right {
        transform: translate(0, 40px);
        flex-direction: column;
    }
}

.left {
    .company-icon {
        margin-top: -7.1%;
    }
}

:deep(.company-icon) {
    width: 100%;
    border-radius: 30px;
    overflow: hidden;
    transition: transform 0.3s;
    transform: scale(1);
    cursor: pointer;

    &:hover {
        transform: scale(1.05);
    }
}


@media screen and (max-width:820px) {

    .about-us-module-box {
        padding: 40px 0 24px;
        background: #fff;
    }

    .header-banner {
        padding-top: 300px;
    }

    .banner-logo {
        width: 50%;
        margin: 0 auto;

        .logo {
            width: 100%;
        }
    }

    .banner-title {
        font-size: 38px;
    }

    .about-figures,
    .about-figuration {
        width: 80%;
    }

    .about-figures {
        margin-bottom: 40px;
    }

    .about-us-inner {
        flex-direction: column;
        align-items: center;
        gap: 40px;
    }

    .about-desc-slider-box {
        padding: 24px 0 30px;
        min-height: unset;

        &::after {
            left: -500px;
            right: -500px;
            bottom: -20px;
        }

        :deep(.ant-carousel .slick-next),
        :deep(.ant-carousel .slick-prev) {
            display: none !important;
        }


        :deep(.ant-carousel .slick-dots) {
            justify-content: center;
            margin-left: unset;
            margin: 0 auto;
        }
    }

    .article-item {
        padding: 42px 10px;
        // width: 80vw !important;
    }

    .link-box {
        margin-top: 20px;
        a {
            font-size: 14px;
            margin: 5px 10px;
            display: block;
        }
    }
}
</style>