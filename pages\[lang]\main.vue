<template>

    <div class="content">

        <!-- banner区域 -->

        <main-banner :data="banners"></main-banner>

        <!-- 精选游戏 -->
        <div class="games-box" v-if="games?.length > 0">
            <div class="games-box-inner" :class="{ 'games-box-inner--active': isInView }" ref="gemeBoxRef">
                <main-title-box :title="$t('pages.main.box.title')" :showDivider="true"></main-title-box>
                <main-game-box :data="games" size="small"></main-game-box>
                <a-button class="check-more-btn" :onClick="goGameListPage">{{ $t('pages.main.box.checkMore')
                    }}</a-button>
            </div>
        </div>

        <!-- 底部简介 -->

        <div class="company-introduce-module" :class="{ 'company-introduce-module--active': isAboutInView }"
            ref="aboutRef">
            <main-title-box :title="$t('pages.index.main.about.title')" :align="'left'"></main-title-box>
            <div class="introduce-box">
                <div class="introduce-box-inner">
                    <div class="figuration-box">
                        <p class="title">{{ $t('pages.main.company.title') }}</p>
                        <p class="desc">{{ $t('pages.main.company.desc') }} </p>
                        <a-button type="primary" shape="round" class="btn" :onClick="goAboutPage">{{
                            $t('pages.main.company.btn') }}</a-button>
                    </div>
                    <div class="figure-box">
                        <img src="~/assets/images/main/main_introduce_bg_tiny.jpg" alt=""
                            class="company-introduce-icon">
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系我们 -->
        <div class="concat-us-module">
            <main-concat-us :data="concatChannels"></main-concat-us>
        </div>

    </div>
</template>

<script setup lang="ts">

import { goToPageSwitch } from '~/utils/tools';
import { inView } from '~/utils/inView';
import { getConcatUsChannelList, getGameList, getMainBanners } from '~/api/main';
import { formatCmsData } from '~/utils/pipe';

const { locale } = useI18n();

const banners = ref<any>([]);

const games = ref<any>([]);

try {
    const mainBannerMetaData = await getMainBanners(locale.value);
    banners.value = computed(() => formatCmsData(mainBannerMetaData, {
        "title": "title",
        "desc": "ad_desc",
        "image_pc": "pc_thumb",
        "image_mb": "mob_thumb",
        "thumb": "ad_thumb",
        "link": "ad_url",
    })).value;

} catch (e) {
    console.log("🚀 ~ e:", e)
}


try {
    const gamesList = await getGameList(locale.value);
    games.value = computed(() => formatCmsData(gamesList, {
        "logo": "mob_thumb",
        "painting": "pc_thumb",
        "figure": "ad_thumb",
        "name": "title",
        "desc": "ad_desc",
        "link": "ad_url"
    })?.filter?.(item => {
        return item.isChoice
    })).value;

    console.log("🚀 ~ games.value:", games.value)
} catch (e) {
    console.log("🚀 ~ e:", e)

}

const concatChannels = ref<any>([]);

try {
    const concatChannelsList = await getConcatUsChannelList(locale.value);
    concatChannels.value = computed(() => formatCmsData(concatChannelsList, {
        "icon": "ad_thumb",
        "title": "title",
        "desc": "ad_desc",
        "link": "ad_url"
    })).value;

    console.log("🚀 ~ concatChannels.value:", concatChannels.value)
} catch (e) {
    console.log("🚀 ~ e:", e)

}

function goGameListPage() {
    goToPageSwitch('game/list', 'www', locale.value);
}

function goAboutPage() {
    goToPageSwitch('about/company', 'www', locale.value);
}



const gemeBoxRef = ref(null);

let isInView = ref(false);
let unObserver: Function;

onMounted(() => {
    if (gemeBoxRef.value) {
        const onEnter = () => {

            nextTick(() => {
                isInView.value = true;
            })

            console.log("🚀 ~ onEnter ~ isInView.value:", isInView.value)
            if (typeof unObserver === 'function') {
                unObserver()
            }
            return undefined;
        }

        const options: InViewOptions = {
            root: undefined,
            amount: 0,
        }

        unObserver = inView(gemeBoxRef.value, onEnter, options)
    }
});

onUnmounted(() => {
    if (typeof unObserver === 'function') {
        unObserver()
    }
})


const aboutRef = ref(null);

let isAboutInView = ref(false);
let unObserverAbout: Function;

onMounted(() => {
    if (aboutRef.value) {
        const onEnter = () => {

            nextTick(() => {
                isAboutInView.value = true;
            })

            console.log("🚀 ~ onEnter ~ isAboutInView.value:", isAboutInView.value)
            if (typeof unObserverAbout === 'function') {
                unObserverAbout()
            }
            return undefined;
        }

        const options: InViewOptions = {
            root: undefined,
            amount: 0,
        }

        unObserverAbout = inView(aboutRef.value, onEnter, options)
    }
});

onUnmounted(() => {
    if (typeof unObserverAbout === 'function') {
        unObserverAbout()
    }
})



</script>

<style scoped lang="scss">
.content {
    position: relative;
    z-index: 1;
}

.games-box {
    background: url('~/assets/images/main/main_page_bg.png') center center no-repeat;
    background-size: cover;
    padding: 60px 0 84px;


    :deep(.title-box-divider) {
        margin-top: 14px;
        margin-bottom: 53px;
    }
}


.check-more-btn {
    display: block;
    outline: none;
    cursor: pointer;
    margin: 69px auto 0;
    width: 300px;
    height: 68px;
    flex-shrink: 0;
    border-radius: 42px;
    border: 2px solid #3A58A9;
    background: #F3F3F6;
    color: #111;
    text-align: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.48px;
    transition: all 0.5s ease;

    &:hover {
        border: 2px solid #3A58A9;
        background: #3A58A9;
        color: #F3F3F6;
    }
}


.games-box-inner,
.company-introduce-module {
    opacity: 0;
    transform: translate(0, 100px);
    transition: all 0.5s 0.2s ease;

    &--active {
        opacity: 1;
        transform: translate(0, 0);
    }
}

.company-introduce-module {
    padding: 8% 0 9.3%;
    background: rgba(43, 43, 43, 1) url('~/assets/images/main/mask_company_bg_tiny.png') no-repeat center center;
    background-size: cover;

    :deep(.title-box-wrapper) {
        padding-left: 17%;
        max-width: 86%;
    }

    :deep(.title-box-title) {
        color: #fff;
        width: 100%;
        max-width: 630px;
    }

    :deep(.title-box-desc) {
        color: #fff;
        width: auto;
        max-width: 630px;
    }

    :deep(.title-box-divider) {
        margin-top: 14px;
        margin-bottom: 53px;
    }
}

.introduce-box {
    position: relative;
    width: 86%;
    padding: 50px 0 40px 17%;
    background: url('~/assets/images/main/about_articel_bg_tiny.png') no-repeat right top;
    background-size: 100% 100%;
}

.introduce-box-inner {
    display: flex;
    max-width: 1320px;
    width: 90%;
}

.figuration-box {
    width: 50%;
    margin-right: 10%;
    font-weight: 400;
    font-style: normal;
    line-height: normal;
    color: #FFF;
    display: flex;
    flex-direction: column;

    .title {
        font-size: max(30px, calc(46px/1920px*100vw));
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: normal;
        margin-bottom: 22px;
    }

    .desc {
        font-size: max(14px, calc(17px/1920px*100vw));
        letter-spacing: 1.5px;
        margin-bottom: 26px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .btn {
        $bgColor: #3A58A9;
        $color: #fff;
        $hoverLight: 10%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $bgColor;
        border-color: $bgColor;
        height: 64px;
        max-width: 266px;
        padding: 17px 57px;
        font-size: 22px;
        line-height: 1;
        font-style: normal;
        font-weight: 400;
        color: $color;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: normal;

        &:hover {
            background-color: lighten($bgColor, 10%);
            border-color: lighten($bgColor, 10%);
            color: lighten($color, 10%)
        }
    }

}

.figure-box {
    transform: scale(1.5);
    width: 40%;
    height: 287px;
    border-radius: 16px;
    overflow: hidden;
    // background: url('~/assets/images/main/main_introduce_bg_tiny.jpg') no-repeat center top;
    background-size: contain;
}

.company-introduce-icon {
    height: 100%;
    width: auto;
    object-fit: contain;
    margin: 0 auto;
    display: block;
    border-radius: 16px;
    overflow: hidden;
}

.concat-us-module {
    background: url('~/assets/images/main/concat_us_module_bg_tiny.png') no-repeat left top;
    background-size: cover;
}


@media screen and (max-width:1200px) {

    :deep(.game-box-wrapper) {

        --offset-x: 2.9vw;
        --space-gap: 30px;
        --gap-w: calc(var(--space-gap) + var(--offset-x));
        --col-count: 2;
        --item-max: calc(23vw);
        --wrapper-width: 1224px;


        .game-detail {

            .game-name {
                font-size: 16px;
                height: 44px;
            }

            .game-desc {
                font-size: 14px;
                height: 72px;
            }
        }

        .game-channels {
            bottom: 30px;
        }

        .game-channel {
            width: 34px;
            height: 34px;
        }
    }



}

@media screen and (max-width:1000px) {

    :deep(.game-box-wrapper) {

        .game-desc {
            -webkit-line-clamp: 4;
        }

        .game-channel {
            width: 28px;
            height: 28px;
        }

    }
}


@media screen and (max-width:820px) {

    :deep(.game-box-wrapper) {

        &.small {
            --offset-x: 2.9vw;
            --space-gap: 20px;
            --gap-w: calc(var(--space-gap) + var(--offset-x));
            --col-count: 3;
            --item-max: 426px;
            --wrapper-width: 1444px;
        }

        .game-box::after {
            opacity: 1;
            background-color: rgba(0, 0, 0, .2);
        }


        .game-logo {
            transform: scale(0.638);
        }

        .game-painting {
            transform: scale(0.812) translate(-10px);
        }


        .game-box {
            width: 100%;
            max-width: 100%;
        }


        .game-detail {
            top: 25%;
            left: 20px;
            opacity: 1;
            pointer-events: all;

            .game-name {
                font-size: 18px;
            }

            .game-desc {
                font-size: 14px;
                height: 72px;
            }
        }

        .game-channels {
            bottom: 50px;
        }

        .game-channel {
            width: 34px;
            height: 34px;
        }
    }
}

@media screen and (max-width:480px) {


    :deep(.game-box-wrapper) {

        .game-desc {
            font-size: 14px;
            height: 70px;
        }

        .game-channels {
            bottom: 20px;
        }


        .game-channel {
            font-size: 0;
            // width: 24px;
            // height: 24px;
        }
    }

}

// 移动端响应兼容
@media screen and (max-width:820px) {

    .games-box {
        padding: 30px 0 20px;

        :deep(.title-box-divider) {
            margin-top: 0;
            margin-bottom: 20px;
        }

        :deep(.title-box-title) {
            margin-bottom: 0;
        }
    }

    .check-more-btn {
        display: flex;
        font-size: 18px;
        justify-content: center;
        align-items: center;
        width: -webkit-fill-available;
        height: auto;
        padding: 10px 20px;
        margin: 30px 40px 20px;

    }

    .company-introduce-module {
        :deep(.title-box-wrapper) {
            padding-left: 5%;
        }
    }

    .introduce-box {

        border-radius: 16px;
        width: 100%;
        padding: 56px 10% 43px;
        background: #293e7e;

        &-inner {
            margin: 0 auto;
            flex-direction: column-reverse;
        }

        .figuration-box,
        .figure-box {
            width: 100%;
        }

        .figure-box {
            background: url('~/assets/images/main/main_introduce_bg_tiny.jpg') no-repeat center top;
            transform: scale(1) translate(0, -20px);
            width: 80%;
            height: unset;
            border-radius: 16px;
            padding-top: 80%;
            margin: 0 auto;
            background-size: cover;
        }

        .btn {
            margin: 0 auto;
            max-width: 100%;
            width: 100%;
        }

    }

    .concat-channel {
        width: 100%;
    }

    .company-introduce-icon {
        display: none;
    }

}
</style>