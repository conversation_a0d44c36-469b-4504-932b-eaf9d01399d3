<template>
    <!-- pc端 -->
    <div class="pc" v-if="paymentType == 'pc'">
        <a-radio-group v-model:value="localPayMethodVal" class="pay_method">
            <div class="item_wrap" v-for="item in pageInfo.paymentMethods" :key="item.id">

                <div class="item" :class="{ recommand: item.recommended, active: localPayMethodVal == item.id }"
                    @click="handlePayMethodChange(item, $event)" :id="`paymentId${item.id}`">
                    <a-radio :value="item.id"></a-radio>
                    <div :class="paymentMethodsConfig[item.id]?.imgStyle?.wrapper">
                        <img :alt="item.Name" :class="paymentMethodsConfig[item.id]?.imgStyle?.img"
                            :src="getImageUrl(paymentMethodsConfig[item.id]?.img)" />
                    </div>
                </div>

            </div>
        </a-radio-group>
        <!-- mycard 场景 -->
        <div class="my_card_wrap" v-if="mycardItems.length > 0 && localPayMethodVal == 60001">
            <div class="my_card_hint">{{ $t('pay.payment.mycard.select') }}</div>
            <div class="item_box" v-for="item, index in mycardItems" :key="index">
                <div class="my_card_item" :class="{ active: item.id == curMycardItemId }"
                    @click="handleMycardItemSelect(index, item.id)">
                    <h4 :class="{ two_line: !(item.description && item.description[locale]) }">{{
                        item.name[locale] }}
                    </h4>
                    <div class="txt">{{ pageInfo.currency }} {{ item.price }}</div>
                    <div class="activity_goods_label" v-if="item.activityIcon">
                        <img :src="item.activityIcon[locale]" />
                    </div>
                    <div class="desc" v-if="item.description && item.description[locale]">{{
                        item.description[locale] }}
                    </div>
                    <div class="countDown_wrap" v-if="item.activityCountdown > 0">
                        <div class="icon_clock"></div>
                        <div class="num">
                            <div class="num_txt">{{ formatTimeSecond(item.activityCountdown) }}</div>
                        </div>
                    </div>
                    <div class="activity_goods_num" v-if="parseInt(item.activityMaxNum) > 0">
                        <div class="label_txt">{{ item.activityNum || 0 }}/{{ item.activityMaxNum }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 移动端 -->
    <div class="mobile" v-if="paymentType == 'mobile'">
        <a-radio-group v-model:value="localPayMethodVal" class="order_f_pay_method">
            <div class="item_wrap" v-for="item in pageInfo.paymentMethods" :key="item.id">
                <!-- mycard 场景 -->
                <template v-if="item.id == 60001">
                    <div class="item" :class="{ recommand: item.recommended, active: localPayMethodVal == item.id }"
                        @click="handlePayMethodChange(item)">
                        <a-radio :value="item.id"></a-radio>
                        <div class="imgBox">
                            <img class="img" src="~/assets/images/pay/MYCARD.png" />
                        </div>
                    </div>
                    <div class="my_card_wrap" v-if="mycardItems.length > 0 && localPayMethodVal == 60001">
                        <div class="my_card_hint">{{ $t('pay.payment.mycard.select') }}</div>
                        <div class="item_box" v-for="item, index in mycardItems" :key="index">
                            <div class="my_card_item" :class="{ active: item.id == curMycardItemId }"
                                @click="handleMycardItemSelect(index, item.id)">
                                <h4>{{ item.name[locale] }}</h4>
                                <div class="txt">{{ pageInfo.currency }} {{ item.price }}</div>
                                <div class="activity_goods_label" v-if="item.activityIcon">
                                    <img :src="item.activityIcon[locale]" />
                                </div>
                                <div class="countDown_wrap" v-if="item.activityCountdown > 0">
                                    <div class="icon_clock"></div>
                                    <div class="num">
                                        <div class="num_txt">{{ formatTimeSecond(item.activityCountdown) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="desc" v-if="item.description && item.description[locale]">{{
                                    item.description[locale] }}
                                </div>
                                <div class="activity_goods_num" v-if="parseInt(item.activityMaxNum) > 0">
                                    <div class="label_txt">{{ item.activityNum || 0 }}/{{ item.activityMaxNum }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- adyen 信用卡 场景 -->
                <div class="item credit_card"
                    :class="{ recommand: item.recommended, active: localPayMethodVal == item.id }"
                    v-if="item.id == 60002" @click="handlePayMethodChange(item)">
                    <a-radio :value="item.id"></a-radio>
                    <div class="credit_card_flex1_wrap">
                        <div class="icon_creditcard">
                            <img class="img" src="~/assets/images/pay/credit_card.png" />
                            <div class="txt">{{ $t('pay.credit.card') }}</div>
                        </div>
                        <div class="order_f_credit_card_list">
                            <img class="img" src="~/assets/images/common/credit_icon_m.png" />
                        </div>
                    </div>
                </div>
                <!-- 其他场景 场景 -->
                <div class="item" :class="{ recommand: item.recommended, active: localPayMethodVal == item.id }"
                    v-if="!(item.id == 60001 || item.id == 60002)" @click="handlePayMethodChange(item)">
                    <a-radio :value="item.id"></a-radio>
                    <div class="imgBox">
                        <img class="img" :alt="item.Name" :src="getImageUrl(paymentMethodsConfig[item.id].img)" />
                    </div>
                </div>
            </div>
        </a-radio-group>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isMobile, getImageUrl } from "~/utils/tools";
import { isAppleDevice } from "~/utils/tools";

const { $jsbridge } = useNuxtApp() as any; // 全局jsbridge

const { t, locale } = useI18n()

const props = defineProps({
    pageInfo: {
        type: Object,
        required: true
    },
    mycardItems: {
        type: Array as PropType<any[]>,
        default: () => []
    },
    payMethodVal: {
        type: Number,
        required: true
    },
    curMycardItemId: {
        type: Number,
        default: 0
    },
    paymentType: {
        type: String,
        default: 'pc'
    },
    isWebview: {
        type: Boolean,
        default: false
    }
})

const $emit = defineEmits(['update:payMethodVal', 'setPayMethod', 'selectMycardItem', 'orderStatusClose', 'setHrefJump', 'setSubmitData', 'setQrCodeUrl'])

// 支付方式列表
const paymentMethodsConfig = ref<any>({
    //mycard
    "60001": {
        "img": "~/assets/images/pay/MYCARD.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    //adyen 信用卡
    "60002": {
        "img": "~/assets/images/common/credit_icon_pc.png",
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    //paypal
    "60003": {
        "img": "~/assets/images/pay/Paypal.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    //EVONET 信用卡 场景 
    "60007": {
        "img": '~/assets/images/pay/evonet_credit.png',
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    //街口 场景 
    "60008": {
        "img": "~/assets/images/common/credit_icon_7.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    //雷蛇 场景 
    "60009": {
        "img": "~/assets/images/common/razerGold3.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    //antom: 支付宝支付 HK 场景
    "60010": {
        "img": "~/assets/images/pay/alipay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // COD 支付宝 HK QIXI 场景 HK
    "60011": {
        "img": "~/assets/images/pay/alipay_hk_logo.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // COD 支付宝 HK QIXI 场景 国内
    "60012": {
        "img": "~/assets/images/pay/alipay_logo_new.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // EVONET ： AlipayCN(EVONET)
    "60015": {
        "img": "~/assets/images/pay/EVONET_ALIPAY_CN.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // EVONET ： AlipayHK(EVONET)
    "60016": {
        "img": "~/assets/images/pay/EVONET_ALIPAY_HK.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 Bank transfer Indonesia
    "60020": {
        "img": "~/assets/images/pay/coda20_Bank_transfer_Indonesia.png",
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    // CODA支付 QRIS
    "60021": {
        "img": "~/assets/images/pay/coda21_QRIS.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 GOPAY
    "60022": {
        "img": "~/assets/images/pay/coda22_GOPAY.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 OVO
    "60023": {
        "img": "~/assets/images/pay/coda23_OVO.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 DOKU Wallet
    "60025": {
        "img": "~/assets/images/pay/coda25_DOKU_Wallet.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 Card payments Indonesia
    "60026": {
        "img": "~/assets/images/pay/coda26_Card_payments_Indonesia.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 Bank transfer and cash at retail Thailand
    "60030": {
        "img": "~/assets/images/pay/coda30_Bank_transfer_and_cash_at_retail_Thailand.png",
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    // CODA支付 K PLUS
    "60031": {
        "img": "~/assets/images/pay/coda31_K_PLUS.png",
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    // CODA支付 Promptpay
    "60032": {
        "img": "~/assets/images/pay/coda32_Promptpay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 7 Eleven TH
    "60033": {
        "img": "~/assets/images/pay/coda33_7_Eleven_TH.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 Rabbit LINE Pay
    "60035": {
        "img": "~/assets/images/pay/coda35_Rabbit_LINE_Pay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 TrueMoney Wallet
    "60036": {
        "img": "~/assets/images/pay/coda36_TrueMoney_Wallet.png",
        "imgStyle": {
            "wrapper": "icon_creditcard",
            "img": "creditcard_img"
        }
    },
    // CODA支付 Rabbit LINE Pay
    "60038": {
        "img": "~/assets/images/pay/coda38_ShopeePay_TH.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 Card Payments Thailand
    "60039": {
        "img": "~/assets/images/pay/coda39_Card_Payments_Thailand.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 马来西亚 Touch & Go	
    "60040": {
        "img": "~/assets/images/pay/coda40_touch_go.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付 马来西亚 boost
    "60041": {
        "img": "~/assets/images/pay/coda41_boost.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  马来西亚 Google Apple Pay Logo
    "60071": {
        "img": "~/assets/images/pay/coda71_Google_Apple_Pay_Logo.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  菲律宾 GCash
    "60050": {
        "img": "~/assets/images/pay/coda50_gcash.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  菲律宾 7-Eleven
    "60051": {
        "img": "~/assets/images/pay/coda51_711.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  菲律宾 MAYA
    "60052": {
        "img": "~/assets/images/pay/coda52_maya.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  菲律宾 Google Apple Pay Logo
    "60072": {
        "img": "~/assets/images/pay/coda71_Google_Apple_Pay_Logo.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  印尼 Indomaret
    "60060": {
        "img": "~/assets/images/pay/coda60_indomaret.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  印尼 shopeepay
    "60061": {
        "img": "~/assets/images/pay/coda61_shopeepay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  印尼 DANA
    "60062": {
        "img": "~/assets/images/pay/coda62_dana.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  印尼 LinkAja
    "60063": {
        "img": "~/assets/images/pay/coda63_linkaja.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  印尼 Google Pay Indonesia	
    "60070": {
        "img": "~/assets/images/pay/coda61_shopeepay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // CODA支付  US Google Pay / Apple Pay 		
    "60080": {
        "img": "~/assets/images/pay/coda71_Google_Apple_Pay_Logo.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // Adyen: Google Pay	
    "60090": {
        "img": "~/assets/images/pay/adyen90_google_pay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // Adyen: Apple Pay
    "60091": {
        "img": "~/assets/images/pay/adyen91_apple_pay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // evonet: konbini
    "60017": {
        "img": "~/assets/images/pay/konbini2.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
    // evonet: paypay
    "60018": {
        "img": "~/assets/images/pay/paypay.png",
        "imgStyle": {
            "wrapper": "imgBox",
            "img": "img"
        }
    },
})

// 本地支付方式值
const localPayMethodVal = ref(props.payMethodVal)

// 监听父组件传入的支付方式变化
watch(() => props.payMethodVal, (newVal) => {
    localPayMethodVal.value = newVal
})

// 监听本地支付方式变化并同步到父组件
watch(() => localPayMethodVal.value, (newVal) => {
    $emit('update:payMethodVal', newVal)
})

// 处理支付方式变更
const handlePayMethodChange = (item: any, event?: Event) => {
    $emit('setPayMethod', item, event || null)
}

// 处理 Mycard 商品选择
const handleMycardItemSelect = (index: number, id: number) => {
    $emit('selectMycardItem', index, id)
}

// 处理支付跳转
const handlePaymentRedirect = (data: any, paymentId: number) => {
    // 判断是手机端 还是pc端

    console.log("paymentId", paymentId)
    switch (paymentId) {
        // case 60001: // mycard
        // case 60003: // paypal
        // case 60007: // EVONET
        // case 60008: // 街口支付
        // case 60009: // 雷蛇
        // case 60011: // COD 支付宝 HK QIXI 场景 HK
        // case 60012: // COD 支付宝 HK QIXI 场景 国内
        // case 60015: // EVONET ： AlipayCN(EVONET)
        // case 60016: // EVONET ： AlipayHK(EVONET)
        // case 60020: // CODA支付 Bank transfer Indonesia
        // case 60021: // CODA支付 QRIS
        // case 60022: // CODA支付 GOPAY
        // case 60023: // CODA支付 OVO
        // case 60025: // CODA支付 DOKU Wallet
        // case 60026: // CODA支付 Card payments Indonesia
        // case 60030: // CODA支付 Bank transfer and cash at retail Thailand
        // case 60031: // CODA支付 K PLUS
        // case 60032: // CODA支付 Promptpay
        // case 60033: // CODA支付 7 Eleven TH
        // case 60035: // CODA支付 Rabbit LINE Pay
        // case 60036: // CODA支付 TrueMoney Wallet
        // case 60038: // CODA支付 ShopeePay TH
        // case 60039: // CODA支付 Card Payments Thailand
        // case 60040: // CODA支付 马来西亚 Touch & Go
        // case 60041: // CODA支付 马来西亚 boost
        // case 60071: // CODA支付  马来西亚 Google Apple Pay Logo
        // case 60050: // CODA支付  菲律宾 GCash
        // case 60051: // CODA支付  菲律宾 7-Eleven
        // case 60052: // CODA支付  菲律宾 MAYA
        // case 60072: // CODA支付  菲律宾 Google Apple Pay Logo
        // case 60060: // CODA支付  印尼 Indomaret
        // case 60061: // CODA支付  印尼 shopeepay
        // case 60062: // CODA支付  印尼 DANA
        // case 60063: // CODA支付  印尼 LinkAja
        // case 60070: // CODA支付  印尼 Google Pay Indonesia	
        // case 60080: // CODA支付  US Google Pay / Apple Pay 		
        //     if (isMobile()) {
        //         if (props.isWebview) {
        //             $jsbridge.switchBarChange(1)
        //         }
        //         window.location.href = data.transactionUrl
        //     } else {
        //         if (data.transactionUrl) {
        //             $emit('orderStatusClose', true);  // 弹出状态窗口
        //             // window.open(data.transactionUrl, '_blank')
        //             // console.log("data.transactionUrl",data.transactionUrl)
        //             $emit('setHrefJump', data.transactionUrl)
        //         }
        //     }
        //     break;
        case 60010: // antom: 支付宝支付
            if (isMobile()) {
                if (props.isWebview) {
                    $jsbridge.switchBarChange(1)
                }
                window.location.href = data.transactionUrl
            } else {
                $emit('setQrCodeUrl', data.qrCodeUrl);
                $emit('orderStatusClose', true)
            }
            break;
        case 60002:  // adyen
        case 60090: // Adyen: Google Pay
            $emit('setSubmitData', {
                paymentClass: 'google',
                ...data,
            });
            break;
        case 60091: // Adyen: Apple Pay
            if (isAppleDevice() && window.ApplePaySession) {
                $emit('setSubmitData', {
                    paymentClass: 'apple',
                    ...data,
                });
            } else {
                message.warning(t('pay.apple.only.hint'), 3)
            }
            break;
        default:
            if (isMobile()) {
                if (props.isWebview) {
                    $jsbridge.switchBarChange(1)
                }
                window.location.href = data.transactionUrl
            } else {
                if (data.transactionUrl) {
                    $emit('orderStatusClose', true);  // 弹出状态窗口
                    // window.open(data.transactionUrl, '_blank')
                    // console.log("data.transactionUrl",data.transactionUrl)
                    $emit('setHrefJump', data.transactionUrl)
                }
            }
    }
}
defineExpose({
    handlePaymentRedirect
})



</script>

<style lang="scss" scoped>
@import '~/assets/styles/pay_common.scss';
@import '~/assets/styles/pay.scss';
</style>