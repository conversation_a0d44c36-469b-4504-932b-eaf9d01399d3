/**
 * 从Nuxt应用程序中获取并使用$axios实例。
 * @param options 请求配置选项。
 * @returns 返回封装后的axios请求。
 */
import URLS from "~/api/uri";
// 服务器获取请求
import { useAsyncDataCustom } from "~/utils/useAsyncDataCustom"; // 调整导入路径
// 服务器获取
import { useFetchCustom } from "~/utils/useFetchCustom"; // 调整导入路径
import { $axios } from "~/utils/axiosCustiom";

/**
 * 异步获取项目列表数据。
 * @returns 返回一个Promise，解析为项目列表数据。
 */
/**
 * @function 拉取首页列表
 * @method get
 * @returns
 */
const getProjectList = (kolSuffix: string = "") => {
    let url = URLS.Api.getProjectList;
    // console.log(url)
    return useAsyncDataCustom("getProjectList", {
        method: "GET",
        data: kolSuffix ? { kolSuffix } : {},
        url,
        lazy: true,
    });
    // return useFetchCustom(url, {
    //     key:"getProjectList",
    //     method: "GET",
    // });
};

/**
 * @function 拉取首页列表  使用axios 方法
 * @method get
 * @returns
 */
const getAxiosProjectList = () => {
    let url = URLS.Api.getProjectList;
    // console.log(url)
    return $axios({
        method: "GET",
        url,
    });
};

/**
 * 获取角色列表的函数。
 * @param projectId 项目ID，用于查询特定项目下的角色列表。
 * @returns 返回一个Promise，解析为从API获取的角色列表数据。
 */
const getRoleList = (projectId: string) => {
    return $axios({
        method: "GET",
        url: `${URLS.Api.getRoleList}/${projectId}`,
    });
};

/**
 * 获取特定角色信息的函数。
 * @param parameter 包含项目ID、服务器ID和角色ID的对象，用于查询特定角色的信息。
 * @returns 返回一个Promise，解析为从API获取的特定角色的信息。
 */
const getRole = (parameter: any) => {
    return $axios({
        method: "GET",
        url: `${URLS.Api.getRole}/${parameter.projectId}/${parameter.serverId
            }/${encodeURIComponent(parameter.roleId)}`,
    });
};

/**
 * 获取服务器列表的函数。
 * @param project_id 项目ID，用于查询特定项目下的服务器列表。
 * @returns 返回一个Promise，解析为从API获取的服务器列表数据。
 */
const getServerList = (project_id: string) => {
    return useAsyncDataCustom("getServerList", {
        method: "GET",
        url: `${URLS.Api.getServerList}/${project_id}`,
        lazy: true,
    });
};

/**
 * 获取页面配置信息的函数。
 * @param SchemeId 页面配置的唯一标识ID，用于查询特定页面的配置信息。
 * @returns 返回一个Promise，解析为从API获取的页面配置信息。
 */
const getPageInfo = (SchemeId: number, roleId?: string) => {
    return $axios({
        method: "GET",
        url: `${URLS.Api.getPageInfo}/${SchemeId}`,
        lazy: true,
        params: roleId ? { roleId } : {},
    });
};

/**
 * 生单
 * @param params 表单数据对象，包含需要提交给服务器的所有信息。
 * @returns 返回一个Promise，解析为API返回的提交结果。
 */
const postSubmit = (params: any, header: any = {}) => {
    return $axios({
        method: "POST",
        url: `${URLS.Api.postSubmit}`,
        data: params,
        headers: header,
    });
};
/**
 * 获取订单状态
 * @param params 查询订单的参数对象。
 * @returns 返回一个Promise，解析为订单详情。
 */

const getOrderDetail = (params: any) => {
    return $axios({
        method: "GET",
        url: `${URLS.Api.getOrderDetail}`,
        params,
    });
};

/**
 * 获取订单状态(用于webview)
 * @param params 查询订单的参数对象。
 * @returns 返回一个Promise，解析为订单详情。
 */

const getWebviewOrderDetail = (params: any) => {
    return $axios({
        method: "GET",
        headers: {
            'Accept-Language': params?.language || 'en'
        },
        url: `${URLS.Api.getWebviewOrderDetail}`,
        params,
    });
};

/**
 * 获取mycard商品
 * @param SchemeId 方案ID。
 * @returns 返回一个Promise，解析为mycard商品列表。
 */

const getMycardItem = (SchemeId: number, roleId: number = 0, kolSuffix?: string) => {
    return $axios({
        method: "post",
        url: `${URLS.Api.getMycardItem}`,
        data: { SchemeId, roleId, kolSuffix },
    });
};

/**
 * 获取用户 活动商品的购买数量
 * @param uid，productIds
 * @returns 返回一个Promise，解析为mycard商品列表。
 */
// const getActivityRemainingBuy = (uid: number,productIds:[number]) => {
//     return $axios({
//         method: "get",
//         url: `${URLS.Api.getActivityRemainingBuy}`,
//         params: { uid,productIds },
//     });
// };

export {
    getProjectList,
    getAxiosProjectList,
    getRoleList,
    getRole,
    getServerList,
    getPageInfo,
    postSubmit,
    getOrderDetail,
    getWebviewOrderDetail,
    getMycardItem,
};
