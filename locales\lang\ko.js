// import antdEnUS from 'ant-design-vue/es/locale-provider/ko_KR'
// import momentEU from 'moment/locale/ko'
import global from './ko/global'

import menu from './ko/menu'
import setting from './ko/setting'
import user from './ko/user'
import account from './ko/account'
import pages from './ko/pages'

const components = {
    // antLocale: antdEnUS,
    momentName: 'ko',
    // momentLocale: momentEU
}

export default {
    message: '-',
    'login.hint.txt': "본 웹 사이트는 공식 게임 이메일 계정으로만 로그인하실 수 있습니다.",
    'login.hint.txt2': "기타 3자 로그인 계정(예: 구글 로그인, 페이스북, 애플 로그인 등)은 먼저 게임으로 이동하여 이메일 연동을 진행해 주세요.",
    'login.error.email': '잘못된 이메일 계정 또는 비밀번호 입니다. 다시 입력해 주세요',
    'login.email.name': '이메일 로그인으로 충전',
    'layouts.usermenu.dialog.title': 'Message',
    'layouts.usermenu.dialog.content': 'Are you sure you would like to logout?',
    'layouts.userLayout.title': 'Ant Design is the most influential web design specification in Xihu district',
    'pay.credit.card': '신용카드',
    'pay.role.comfirm': '확인',
    'pay.how.get.roleId': '캐릭터ID 확인 방법?',
    'pay.comfirm.account': '주문 확인',
    'pay.comfirm.account.roleId': '충전 하실려는 캐릭터ID 및 계정을 확인하세요',
    'page.fetch.fail': '페이지 로딩 실패, 클릭하여{ XX }홈{ YY }으로 이동',
    'login.current.name': '최근 로그인',
    'login.role.name': '캐릭터',
    'footer.policy.cookies': '쿠키정책',
    'footer.policy.refund': '환불정책',
    'footer.policy.website': '이용약관',
    'footer.policy.privacy': '개인정보정책',
    'footer.policy.website2':'게임 서비스 계약',
    'footer.policy.privacy2':'개인정보 보호정책',
    'ux.txt.agree': '모두 동의',
    'api.login.lose': '로그인 실패, 다시 시도 해주세요',
    'api.login.request.fail': '로딩 실패, 조금 뒤 다시 시도해주세요',
    'common.policy.cookies': "당사는 쿠키(Cookies)를 사용하여 더 좋은 방문 경험을 제공해 드리겠습니다. 쿠키(Cookies)중 일부는 본 웹사이트의 운영에 있어서 매우 중요한 부분이며, 일부는 귀하의 웹사이트 사용 상황을 파악하여 웹사이트를 개선하는 데 도움이 됩니다. '모두 동의'를 클릭하 쿠키(Cookies) 정책 및 개인정보 보호 정책에 동의하십시오.",
    'common.comming.soon':'기대 부탁드립니다',
    'pay.payment.mycard.select':'Mycard결제수단으로 하기 상품을 선택하여 주세요',
    'pay.payment.mycard.hint':'Mycard상품을 선택하여 주세요',
    'pay.order.not.paid':'주문 결제가 완료되지 않았습니다',
    'pay.order.comfirming.loading':'주문 확인 중',
    'pay.order.comfirming.txt':'주문 확인 중, 조금 뒤 주문 리스트에서 주문 상태를 확인 부탁드립니다충전 관련 문의가 있을 경우 "고객센터"로 연락 부탁드립니다.',
    'pay.role.login.hint':'현재 게임은 캐릭터 ID를 사용한 충전만 지원합니다.',
    'cookies.policy.agree.all':'모든 쿠키 항목 동의',
    'cookies.policy.disagree.all':'모든 쿠키 항목 거부',
    'cookies.policy.manage':'쿠키 설정',
    'cookies.policy.confirm.my':'선택 사항 확인하기',
    'cookies.policy.function.txt':'해당 쿠키는 웹사이트의 성능을 분석하고 개선하는데 도움이 됩니다. 또한 더 나는 서비스 경험을 제공을 위해 다음과 같은 용도로도 사용 될수 있습니다.예시: 귀하의 로그인 정보를 기억하고 본 웹사이트의 사용 상황을 더욱 정확하게 파악',
    'cookies.policy.function.title':'기능 관련 쿠키',
    'cookies.policy.analysis.title':'분석 관련 쿠키',
    'cookies.policy.analysis.txt':'해당 쿠키는 방문자와 웹사이트의 상호 작용하는 방식을 파악하는데 도움이 됩니다. 이를 통해 방문횟수, 트래픽 출처, 사이트에 머무는 시간등을 측정하고 사이트 최적화를 위해 사용 될 수 있습니다.',
    'cookies.policy.base.title':'필수 쿠키',
    'cookies.policy.base.txt':'해당 쿠키는 웹사이트가 제대로 작동되기 위해 필요로 하며, 방문자가 웹사이트의 기능을 정상적으로 이용할 수 있게 보장해 줍니다.',
    'cookies.policy.base.status':'활성화 상태 유지',
    'cookies.policy.center':'쿠키 관리 센터',
    'common.get.more':"자세히 알아보기",
    'pay.order.status.alipay.hint':'알리페이에서 스캔하여 결제',
    'pay.maintenance.hint': '현재 페이지 점검 중으로 결제 기능을 일시적으로 사용할 수 없습니다.',
    'pay.apple.only.hint': '이 결제 방법은 Apple 기기에서만 사용할 수 있습니다. 다른 결제 방법을 선택해 주십시오.',
    'pay.order.status.processing': '주문이 처리 중입니다. 잠시만 기다려 주세요',
    'login.search.noData': '검색 결과가 없습니다',
    'login.choose.games':'게임 선택',
    ...components,
    ...global,
    ...menu,
    ...setting,
    ...user,
    ...account,
    ...pages
}
