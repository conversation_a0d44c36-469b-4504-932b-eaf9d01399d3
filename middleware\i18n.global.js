// middleware/i18n.js

import { LanguageLabels } from '~/constants/common';
export default defineNuxtRouteMiddleware((to, from) => {
    const { $i18n } = useNuxtApp()
    const { locale } = $i18n;
    const currentCookieLang = useCookie("APP_LANG", { maxAge: 86400 * 30, path: "/", domain: process.env.VITE_COOKIE_DOMAIN }); //获取cookie中的lang

    // console.log("route", route.params.lang);
    let langId = LanguageLabels[to.params.lang] ? to.params.lang : (LanguageLabels[currentCookieLang.value] ? currentCookieLang.value : "en");


    // console.log("langId ==================", langId);
    // console.log("from.params.lang ==================", from.params.lang);
    // console.log("to.params.lang ==================", to.params.lang);

    // 当是/路径的时候， 判断获取当前 host 并做跳转
    const getRedirectPath = () => {
        // 获取当前请求的 URL
        const requestUrl = useRequestURL();  // 服务器 获取当前请求的 URL
        const host = process.client ? window.location.host : requestUrl.host;
        // console.log("host ==================", host);
        // console.log("process.env.VITE_ACCOUNT_HOST_NAME ==================", process.env.VITE_ACCOUNT_HOST_NAME);
        // console.log("process.env.VITE_PAYMENT_HOST_NAME ==================", process.env.VITE_PAYMENT_HOST_NAME);
        if (!host) return '/main';
        switch (host) {
            case process.env.VITE_ACCOUNT_HOST_NAME: return '/account/login';
            case process.env.VITE_PAYMENT_HOST_NAME: return '/home';
            default: return '/main';
        }
    }

    // 通过cookie 判断页面跳转的语言
    if (to.fullPath === '/') {
        const redirectPath = getRedirectPath();
        if (currentCookieLang.value) {
            // console.log("currentCookieLang.value", currentCookieLang.value)
            locale.value = langId;
            return navigateTo(`/${langId}${redirectPath}`);
        } else {
            // console.log("index middle")
            //console.log("navigator==============", navigator)
            if (navigator && navigator.language) {  //判断用户浏览器语言
                let redirectLang = "en";
                let lang = navigator.language;
                const langFirst = navigator.language.split('-')[0];
                switch (langFirst) {
                    case "zh":
                        if (lang == "zh-CN") {
                            redirectLang = "zh-Hans";
                        } else {
                            redirectLang = "zh-Hant";
                        }
                        break;
                    case "en":
                        redirectLang = "en";
                        break;
                    default: redirectLang = langFirst;
                }
                redirectLang = LanguageLabels[redirectLang] ? redirectLang : "en";
                const router = useRouter();
                router.push(`/${redirectLang}${redirectPath}`);
                return;
            }
        }
    } else {
        locale.value = langId;
    }

    // 活动页面跳转  http://localhost/zh-Hans/activity/liziqi
    if (to.path.startsWith("/activity/")) {
        if (currentCookieLang.value) {
            // console.log("currentCookieLang.value", currentCookieLang.value)
            locale.value = langId;
            return navigateTo(`/${langId}${to.path}`);
        } else {
            if (navigator && navigator.language) {  //判断用户浏览器语言
                let redirectLang = "en";
                let lang = navigator.language;
                const langFirst = navigator.language.split('-')[0];
                switch (langFirst) {
                    case "zh":
                        if (lang == "zh-CN") {
                            redirectLang = "zh-Hans";
                        } else {
                            redirectLang = "zh-Hant";
                        }
                        break;
                    case "en":
                        redirectLang = "en";
                        break;
                    default: redirectLang = langFirst;
                }
                redirectLang = LanguageLabels[redirectLang] ? redirectLang : "en";
                const router = useRouter();
                router.push(`/${redirectLang}${to.path}`);
                return;
            }
        }
    }


    const headerBgColor = useState('headerBgColor', () => "");
    // 协议页面顶部替换
    if (to.fullPath.indexOf('/policy/') > -1) {
        headerBgColor.value = "blue"
    } else if (to.fullPath.indexOf('/account/') > -1) {
        headerBgColor.value = "black"
    } else {
        headerBgColor.value = ""
    }
    // if (process.server) return; // 确保只在客户端执行
    // const lang = navigator.language.split('-')[0]; // 获取浏览器语言

    // // 获取浏览器语言，如果不支持则使用默认语言
    // const language_redirect = LanguageLabels[lang] ? lang : "en";
    // // 如果访问根路径，进行重定向
    // if (to.fullPath === '/') {
    //     return navigateTo(`/${language_redirect}/home`);
    // }
})

