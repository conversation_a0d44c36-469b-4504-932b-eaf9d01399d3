<template>
    <div class="select_lang_box">
        <div class="language_icon"></div>
        <a-dropdown :trigger="['click']" placement="topLeft">
            <div class="ant-dropdown-link" @click.prevent>
                {{ currentLang }}
                <DownOutlined />
            </div>
            <template #overlay>
                <a-menu>
                    <li v-for="item in languageArray" :key="item.id"
                        class="ant-dropdown-menu-item ant-dropdown-menu-item-only-child" role="menuitem" tabindex="-1"
                        data-menu-id="th" aria-disabled="false" @click="newLink(item.id)">
                        <a href="javascript:;" class="ant-dropdown-menu-title-content new-select-link">{{ item.name
                            }}</a>
                    </li>
                    <!-- <a-menu-item v-for="item in languageArray" :key="item.id">
                        <a href="javascript:;">{{ item.name }}</a>
                    </a-menu-item> -->
                </a-menu>
            </template>
        </a-dropdown>
    </div>
</template>
<script lang="ts" setup>
// @ts-ignore
import { DownOutlined } from '@ant-design/icons-vue';
// @ts-ignore
import type { MenuProps } from 'ant-design-vue';
import { LanguageLabels } from '~/constants/common';
import { useAuthStore } from '~/stores/auth';
import { useI18n } from "vue-i18n";
const { locale } = useI18n();

// const locales = ['zh-Hans', 'zh-Hant', 'en', 'id', 'th', 'ja', 'ko']
// const languageLabels: any = {
//     'zh-Hans': '简体中文',
//     'zh-Hant': '繁體中文',
//     "en": 'English',
//     "id": 'Indonesia',
//     "th": 'ภาษาไทย',
//     "ja": '日本語',
//     "ko": '한국어',
// }
const languageArray = reactive([
    { id: "zh-Hans", name: '简体中文' },
    { id: "zh-Hant", name: '繁體中文' },
    { id: "en", name: 'English' },
    { id: "id", name: 'Indonesia' },
    { id: "th", name: 'ภาษาไทย' },
    { id: "ja", name: '日本語' },
    { id: "ko", name: '한국어' }
])
let langId = "";
const currentCookieLang: any = useCookie("APP_LANG", { maxAge: 86400 * 365 , domain: process.env.VITE_COOKIE_DOMAIN, path:"/"}); //获取cookie中的lang
const route = useRoute(); //路由
// 获取当前语言
const currentLang = ref("");
// currentLang.value = LanguageLabels[langId];
const switchLang = (val: string) => {
    locale.value = val; //本地使用的语言
    currentCookieLang.value = val; //更改cookie中的值
    currentLang.value = LanguageLabels[val] || "en";
    // currentLang.value = LanguageLabels[val] || "English";
};
onMounted(() => {
    if (LanguageLabels[locale.value]) {
        currentLang.value = LanguageLabels[locale.value] || "English";
        currentCookieLang.value = locale.value;
    }
})

// const onClick: MenuProps['onClick'] = ({ key }: any) => {
//     console.log(`Click on item ${key}`);
//     useAuthStore().setAPPLang(key);
//     //设置语音
//     switchLang(key);
//     const newPath = route.path.replace(route.params.lang, key)
//     window.location.href = newPath;
// };
const newLink = (key: string) => {
    // 埋点: 选择语言
    YKTrack.track('select', {
        params: {
            id: '205',
            value1: key, // 语言
        }
    })
    console.log(`Click on item ${key}`);
    useAuthStore().setAPPLang(key);
    //设置语音
    switchLang(key);
    const newPath = route.path.replace(route.params.lang, key)
    window.location.href = newPath;
}

</script>
<style lang="scss">
.select_lang {

    .anticon-down {
        transition: all 0.5s ease;
    }

    .ant-dropdown-open {
        text-shadow: 0 0 6px #fff;

        .anticon-down {
            transform: rotateZ(180deg)
        }
    }

    .newLink {
        color: #000;
    }
}

.new-select-link {
    color: #000;

    &:hover {
        color: #000;
    }
}
</style>
<style lang="scss" scoped>
.select_lang_box {
    display: flex;
    font-size: 16px;
    color: #fff;
    align-items: center;
    cursor: pointer;

    .language_icon {
        width: 22px;
        height: 22px;
        background: url(~/assets/images/footer/language_icon.png) top center/100% 100% no-repeat;
        margin-right: 8px;
    }

    .ant-dropdown-link {
        cursor: pointer;
        color: #fff;
        line-height: 22px;
    }
}
</style>