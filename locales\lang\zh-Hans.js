// import antd from 'ant-design-vue/es/locale-provider/zh_CN'
// import momentCN from 'moment/locale/zh-cn'
import global from './zh-<PERSON>/global'

import menu from './zh-<PERSON>/menu'
import setting from './zh-<PERSON>/setting'
import user from './zh-<PERSON>/user'
import account from './zh-<PERSON>/account'
import pages from './zh-<PERSON>/pages'

const components = {
  // antLocale: antd,
  momentName: 'zh-cn',
  // momentLocale: momentCN
}

export default {
  message: '-',
  'login.hint.txt':"本网站仅支持官方游戏账号进行登录。",
  'login.hint.txt2':"其他三方登录账号（如谷歌登录、Facebook、苹果登录等）请先前往游戏内进行账号绑定。",
  'login.error.email':'账号或密码错误，请重新输入',
  'login.email.name':'账号登录充值',
  'layouts.usermenu.dialog.title': '信息',
  'layouts.usermenu.dialog.content': '您确定要注销吗？',
  'layouts.userLayout.title': 'Ant Design 是西湖区最具影响力的 Web 设计规范',
  'pay.credit.card':'信用卡',
  'pay.role.comfirm':'确认',
  'pay.how.get.roleId':'如何获取角色ID？',
  'pay.comfirm.account':'确认订单',
  'pay.comfirm.account.roleId':'请确认要充值的角色ID或登录账号',
  'page.fetch.fail':'页面信息加载失败，请返回{ XX }首页{ YY }',
  'login.current.name':'最近登录',
  'login.role.name':'角色',
  'footer.policy.cookies':'Cookies政策',
  'footer.policy.refund':'退款政策',
  'footer.policy.website':'网站使用条款',
  'footer.policy.privacy':'在线隐私权政策',
  'footer.policy.website2':'游戏服务合约',
  'footer.policy.privacy2':'隐私保护政策',
  'ux.txt.agree':'接受所有',
  'api.login.lose':'登錄失敗，請重新登錄',
  'api.login.request.fail':'請求數據失敗，請稍後再試',
  'common.policy.cookies':'我们使用cookies为您提供更佳的访问体验。有些cookies对本网站的运行至关重要；有些则是帮助我们了解您是如何使用网站的，以便我们可以对其进行改进。单击“接受所有”以同意我们的Cookies政策和隐私政策。',
  'common.comming.soon':'敬请期待',
  'pay.payment.mycard.select':'Mycard支付方式请选择下方商品',
  'pay.payment.mycard.hint':'请选择Mycard的商品',
  'pay.order.not.paid':'订单未完成支付',
  'pay.order.comfirming.loading':'订单确认中',
  'pay.order.comfirming.txt':'订单确认中，请稍后在订单列表内查看订单状态。如遇充值问题请“联系客服”',
  'pay.role.login.hint':'当前游戏仅支持使用角色ID充值',
  'cookies.policy.agree.all':'接受所有可选Cookie',
  'cookies.policy.disagree.all':'拒绝所有可选Cookie',
  'cookies.policy.manage':'管理Cookie',
  'cookies.policy.confirm.my':'确认我的选择',
  'cookies.policy.function.txt':'这些Cookie可帮助我们分析您对网站的使用情况，以评估和改善我们的性能。它们也可用于提供更好的客户体验。例如:记住您的登录详细信息，或向我们提供有关我们网站使用情况的信息。',
  'cookies.policy.function.title':'功能Cookie',
  'cookies.policy.analysis.title':'分析Cookie',
  'cookies.policy.analysis.txt':'这些Cookie帮助我们了解访客与网站的互动情况。我们可以从中得知访问次数、流量来源以及访客在网站上花费的时间，并以此来衡量网站的使用情况并进行优化。',
  'cookies.policy.base.title':'必要的Cookie',
  'cookies.policy.base.txt':'这些Cookie是网站正常运行所必须的，让您能够正常使用网站的功能，且不会从我们的系统中关闭',
  'cookies.policy.base.status':'始终处于活动状态',
  'cookies.policy.center':'Cookie管理中心',
  'common.get.more':"了解更多",
  'pay.order.status.alipay.hint':'打开支付宝扫一扫付款',
  'pay.maintenance.hint': '页面维护中，暂无法支付。',
  'pay.apple.only.hint': '当前支付方式仅支持苹果设备，请选择其他支付方式。',
  'pay.order.status.processing': '订单处理中，请稍后再试',
  'login.search.noData': '搜索无结果',
  'login.choose.games':'选择游戏',
  ...components,
  ...global,
  ...menu,
  ...setting,
  ...user,
  ...account,
  ...pages
}
