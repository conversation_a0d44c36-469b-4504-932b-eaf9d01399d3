.ac_check_email_phone {
    .ant-btn,
    .ant-input-lg,
    .ant-input-group-addon,
    .ant-picker {
        border: none;
        box-shadow: none;
    }
    .ant-picker {
        width: 100%;
    }
    .ant-input-lg {
        flex: 1;
    }
    .captcha_ant_btn {
        border: none;
        border-left: 1px solid #d9d9d9;
        background: none;
        margin-right: -10px;
        width: 180px !important;
        text-align: center;
        box-shadow: none !important;
        padding: 0 6px;
        & > span {
            border: none;
            box-shadow: none;
            margin-right: -4px;
        }
    }
    .check_method_select {
        width: 100%;
        .ant-select-selector {
            border: none;
            box-shadow: none;
        }
    }
    .phone_input_group {
        width: 100%;
        display: flex;
        align-items: center;
        .phone_country_no {
            margin-right: 10px;
            width: 150px;
        }
    }
}
@media screen and (max-width: 768px) {
    .ac_check_email_phone .phone_input_group {
        .phone_country_no {
            width: 120px;
        }
        * {
            font-size: 12px !important;
        }
    }
    .ac_check_email_phone .captcha_ant_btn {
        width: 120px !important;
    }
    .ac_check_email_phone {
        .ant-btn,
        .ant-input-lg,
        .ant-input-group-addon,
        .ant-picker {
            font-size: 12px !important;
        }
    }
}
