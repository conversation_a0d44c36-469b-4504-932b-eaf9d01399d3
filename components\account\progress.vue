<template>
    <div class="progress-ring">
        <svg width="140" height="140">
            <circle class="progress-ring__circle progress-ring__circle--background" cx="70" cy="70" r="60" />
            <circle ref="progressObj" class="progress-ring__circle progress-ring__circle--progress" cx="70" cy="70"
                r="60" />
        </svg>
        <div class="progress_cont">
            <div class="h3">{{$props.percent}}%</div>
            <div class="txt">{{ $t('account.securityCheck.completionProgress') }}</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
const { t } = useI18n();
const $props = defineProps({
    percent: {
        type: Number,
        default: 0
    }
})
const progressObj = ref(null);
const setProgress = (percent: number) => {
    const circle: any = progressObj.value;
    if (circle) {
        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        const offset = circumference * (1 - percent / 100);

        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = offset;
    }
}

onMounted(() => {
    watch(() => $props.percent, (newVal, oldVal) => {
        setProgress(newVal)
    })
})
</script>

<style lang="scss" scoped>
.progress-ring {
    width: 140px;
    height: 140px;
    position: relative;
}

.progress-ring__circle {
    fill: none;
    stroke-width: 14;
    stroke-linecap: round;
}

.progress-ring__circle--background {
    stroke: #E3E7F3;
}

.progress-ring__circle--progress {
    stroke: #5B80DD;
    transition: stroke-dashoffset 0.3s;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}
.progress_cont {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 0 10px;
    top: 0;
    left: 0;
    z-index: 9;
    font-size: 14px;
    text-align: center;
    color: #111;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .h3 {
        font-size: 30px;
        line-height: 40px;
        font-weight: bold;
    }
}
</style>