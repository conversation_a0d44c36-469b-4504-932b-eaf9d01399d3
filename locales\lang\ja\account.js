export default {
    "account.login.accountLogin": "アカウントログイン",
    "account.login.emailLogin": "メールアドレスでログイン",
    "account.login.pleaseEnterEmail": "メールアドレスを入力してください",
    "account.login.pleaseEnterPassword": "パスワードを入力してください",
    "account.login.forgotPassword": "パスワードを忘れた場合",
    "account.login.registerNow": "新規登録",
    "account.login.cancelLoginAuthorization": "ログイン認証の取消",
    "account.login.confirm": "確認",
    "account.login.return": "戻る",
    "account.login.onlyOfficialGameEmail": "現在、このサイトは公式ゲームのメールアドレスでログインしたアカウントのみ対応しています。",
    "account.login.notSupportEmailVerificationCode": "メール認証コードでのログイン方法は未実装です。",
    "account.login.useEmailVerificationCodeOrThirdParty": "メール認証コードまたはGoogle、Facebook、iOSなどのサードパーティアカウントでのログインを希望する場合、まずゲーム内でメールアカウントの連携を行ってください。",
    "account.login.pleaseEnterVerificationCode": "認証コードを入力してください",
    "account.login.get": "送信",
    "account.login.sent": "送信済み",
    "account.register.accountRegistration": "メールアドレスで登録",
    "account.register.enterEmail": "メールアドレス",
    "account.register.passwordRequirements": "8～20桁のパスワード（英字・数字を両方含むもの）",
    "account.register.confirmPassword": "パスワード確認",
    "account.register.register": "新規登録",
    "account.register.alreadyHaveAccount": "アカウントはすでに登録済みです",
    "account.register.agreeTerms": "ゲーム利用規約とプライバシーポリシーに同意する",
    "account.register.ageConfirmation": "私は14歳以上です",
    "account.register.termsAndAgeConfirmation": "ゲームの利用規約とプライバシーポリシーをお読みいただき、同意してください。また、14歳以上であることも確認してください。",
    "account.register.termsConfirmation": "ゲームの利用規約とプライバシーポリシーをお読みいただき、ご同意いただけますようお願いいたします。",
    "account.common.confirm": "確認",
    "account.common.return": "戻る",
    "account.forgotPassword.title": "パスワードを忘れた場合",
    "account.forgotPassword.enterEmail": "メールアドレス",
    "account.forgotPassword.nextStep": "次へ",
    "account.forgotPassword.noSecurityVerification": "アカウントがセキュリティ検証を完了していません",
    "account.forgotPassword.contactSupport": "パスワードのリセットには、登録されたメールアドレスまたは携帯電話番号の確認が必要です。これらの確認を行っていなかった場合には、",
    "account.forgotPassword.contactSupport2":"「カスタマーサポート」へ連絡ください。",
    "account.forgotPassword.cannotModify": "パスワードの再設定には、メールアドレスまた携帯電話番号の認証が必要です。認証を行わないとパスワードの変更はできません。",
    "account.securityVerification.title": "セキュリティ確認",
    "account.securityVerification.methods": "以下の方法で認証",
    "account.securityVerification.phoneVerification": "携帯番号認証",
    "account.securityVerification.emailVerification": "メールアドレス認証",
    "account.securityVerification.phoneInstructions2": "「送信」をクリックすると、認証コードがご登録の携帯電話番号またはメールアドレス（  { XX }）に送信されます。コードを受け取ったら、画面の指示に従って入力してください。",
    "account.securityVerification.emailInstructions2": "「送信」ボタンをクリックすると、認証コードがあなたのメールアドレス { XX } に送信されます",
    "account.securityVerification.enterVerificationCode": "確認コード入力",
    "account.resetPassword.setNewPassword": "新しいパスワードを設定",
    "account.resetPassword.success": "パスワード変更が完了しました。もう一度ログインしてください",
    "account.profile.accountInformation": "アカウント情報",
    "account.profile.emailVerificationAvailable": "メールアドレス認証を行うことができます。",
    "account.profile.emailVerificationImportance": "アカウント保護のため、メールアドレス認証を行ってください。",
    "account.profile.skipVerification": "今は行わない",
    "account.profile.startEmailVerification": "確認コードで認証",
    "account.profile.nickname": "プロフィール名",
    "account.profile.accountName": "ユーザー名",
    "account.profile.accountID": "アカウントID",
    "account.profile.verifyEmail": "メールアドレス認証",
    "account.profile.phoneNumber": "携帯電話番号",
    "account.profile.notBound": "未連携",
    "account.profile.notVerified": "未認証",
    "account.profile.notSet": "未設定",
    "account.profile.verify": "認証へ",
    "account.profile.bind": "認証",
    "account.profile.bound": "連携済み",
    "account.profile.changeBind": "変更",
    "account.profile.edit": "編集",
    "account.securityCheck.title": "セキュリティ診断",
    "account.securityCheck.bindAccountName": "アカウント名連携",
    "account.securityCheck.accountNameBound": "アカウント名連携済み",
    "account.securityCheck.verifyEmail": "メールアドレス認証",
    "account.securityCheck.emailVerified": "メールアドレス認証済み",
    "account.securityCheck.fillPersonalInfo": "プロフィールを設定する",
    "account.securityCheck.personalInfoFilled": "プロフィール設定済み",
    "account.securityCheck.bindPhoneNumber": "携帯電話番号登録",
    "account.securityCheck.phoneNumberBound": "携帯電話登録済み",
    "account.securityCheck.completionProgress": "完了",
    "account.personalInfo.title": "プロフィール",
    "account.personalInfo.name": "名前",
    "account.personalInfo.birthday": "生年月日",
    "account.personalInfo.gender": "性別",
    "account.personalInfo.notSet": "未設定",
    "account.personalInfo.edit": "編集",
    "account.personalInfo.modify": "修正",
    "account.editNickname.title": "ニックネームを設定してください",
    "account.editNickname.changeLimit": " プロフィール名を設定後、180日間は変更することができません。",
    "account.editNickname.currentNickname": "現在のプロフィール名",
    "account.editNickname.notSet": "未設定",
    "account.editNickname.enterNewNickname": "新しいプロフィール名を入力してください",
    "account.editNickname.nicknameLength": "プロフィール名は3文字以上で入力してください",
    "account.editNickname.privacyWarning": "プライバシー保護のため、実名、住所、電話番号、またはその他の個人情報の使用は避けてください。",
    "account.common.confirm": "確認",
    "account.common.cancel": "キャンセル",
    "account.setEmailAccount.title": "メールアドレスを設定",
    "account.setEmailAccount.description": "認証済みのメールアドレスがアカウント名として使用されます。設定が完了すると、メールでログインできるようになります",
    "account.setEmailAccount.agreement": "「確認」ボタンをクリックするとメールアドレスが保存され、通知の送信やセキュリティに使用することに同意したものとみなされます。",
    "account.setEmailAccount.enterEmail": "メールアドレスを入力してください",
    "account.setEmailAccount.enterVerificationCode": "確認コード入力",
    "account.common.get": "送信",
    "account.common.sent": "送信済み",
    "account.setEmailAccount.passwordRequirements": "8～20桁のパスワード（英字・数字を両方含むもの）",
    "account.setEmailAccount.confirmPassword": "パスワード確認",
    "account.common.confirm": "確認",
    "account.verifyEmail.title": "メールアドレスを登録",
    "account.verifyEmail.agreement": "「認証」ボタンをクリックするとメールアドレスが保存され、通知の送信やセキュリティに使用することに同意したものとみなされます。",
    "account.verifyEmail.enterEmail": "メールアドレスを入力してください",
    "account.verifyEmail.enterVerificationCode": "確認コード入力",
    "account.common.get": "送信",
    "account.common.sent": "送信済み",
    "account.verifyEmail.verify": "認証",
    "account.bindPhoneNumber.title": "携帯電話番号登録",
    "account.bindPhoneNumber.description": "アカウントの安全性を保つため、携帯電話の番号を登録してください。",
    "account.bindPhoneNumber.agreement": "セキュリティ強化のためアカウントに携帯電話番号を追加できます。Playbestから重要なメッセージおよび認証コードを送信されることがあります。",
    "account.bindPhoneNumber.select": "選択",
    "account.bindPhoneNumber.enterPhoneNumber": "携帯電話番号を入力してください",
    "account.bindPhoneNumber.enterVerificationCode": "確認コード入力",
    "account.common.get": "送信",
    "account.common.sent": "送信済み",
    "account.bindPhoneNumber.bind": "認証",
    "account.changeEmail.title": "メールアドレスを再設定",
    "account.changeEmail.currentEmail": "現在認証済みのメールアドレス",
    "account.changeEmail.description": "メールアドレスを再設定後、通知や認証情報は全て新しいメールアドレスに送信されます",
    "account.securityVerification.title": "セキュリティ確認",
    "account.securityVerification.methods": "以下の方法で認証",
    "account.securityVerification.phoneVerification": "携帯電話認証",
    "account.securityVerification.emailVerification": "メールアドレス認証",
    "account.securityVerification.emailInstructions": "「送信」ボタンをクリックすると、認証コードがあなたのメールアドレスに送信されます",
    "account.securityVerification.enterVerificationCode": "確認コード入力",
    "account.common.get": "送信",
    "account.common.sent": "送信済み",
    "account.common.nextStep": "次へ",
    "account.changeEmail.agreement": "「認証」ボタンをクリックするとメールアドレスが保存され、通知の送信やセキュリティに使用することに同意したものとみなされます。",
    "account.changeEmail.enterEmail": "メールアドレスを入力してください",
    "account.changePhoneNumber.title": "携帯番号を再設定",
    "account.changePhoneNumber.currentPhoneNumber": "現在の携帯電話番号",
    "account.changePhoneNumber.description": "携帯電話番号またはメールアドレスを変更した場合、通知や認証コードは全て変更後の番号またはメールアドレスに送信されます。",
    "account.securityVerification.methods": "以下の方法で認証",
    "account.securityVerification.phoneVerification": "携帯電話認証",
    "account.securityVerification.phoneInstructions": "「送信」をクリックすると、登録した携帯電話番号に認証コードが送信されます。",
    "account.securityVerification.enterVerificationCode": "確認コード入力",
    "account.common.get": "送信",
    "account.common.sent": "送信済み",
    "account.common.nextStep": "次へ",
    "account.changePhoneNumber.agreement": "セキュリティ強化のためアカウントに携帯電話番号を追加できます。Playbestから重要なメッセージおよび認証コードを送信されることがあります。",
    "account.resetPassword.setNewPassword": "新しいパスワードを設定",
    "account.resetPassword.enterOriginalPassword": "変更前のパスワードを入力してください",
    "account.resetPassword.verifyOriginalPassword": "変更前のパスワードを確認する",
    "account.resetPassword.setNewPassword": "新しいパスワードを設定してください",
    "account.personalInfo.enterFirstName": "名前を入力してください",
    "account.personalInfo.enterLastName": "苗字を入力してください",
    "account.personalInfo.selectGender": "性別を選択してください",
    "account.personalInfo.gender": "性別",
    "account.personalInfo.selectBirthday": "生年月日を選択してください",
    "account.personalInfo.birthdayWarning": "生年月日は一度しか変更できませんので、慎重に入力してください。",
    "account.personalInfo.male": "男性",
    "account.personalInfo.female": "女性",
    "account.personalInfo.secret": "公開しない",
    "account.personalInfo.other": "その他",
    "account.common.save": "保存",
    "account.avatar.selectAvatar": "プロフィール画像を選択してください",
    "account.avatar.pleaseSelectAvatar": "プロフィール画像を選択してください",
    "account.security.title": "セキュリティ",
    "account.security.passwordManagement": "パスワード管理",
    "account.security.passwordUpdateRecommendation": "安全性を保つため、パスワードを定期的に変更することをお勧めします。",
    "account.security.lastPasswordUpdateTime": "前回のパスワード変更日",
    "account.security.changePassword": "パスワードを変更",
    "account.common.modify": "修正",
    "account.common.set": "設定",
    "account.orders.myOrders": "購入履歴",
    "account.orders.all": "全部",
    "account.orders.pendingPayment": "支払い待ち",
    "account.orders.pendingShipment": "発送待ち",
    "account.orders.completed": "完了しました",
    "account.orders.closed": "終了しました",
    "account.orders.allGames": "すべてのゲーム",
    "account.orders.orderNumber": "注文番号",
    "account.orders.orderTime": "注文時刻",
    "account.orders.cancelOrder": "注文キャンセル",   
    "account.orders.paymentMethod": "支払い方法",
    "account.orders.noOrders": "カートが空です",
    // 注册相关
    "account.register.emailEmpty": "メールアドレスを入力してください",
    "account.register.passwordEmpty": "パスワードを入れてください",
    "account.register.confirmPasswordEmpty": "パスワードを入力してください",
    "account.register.emailInvalid": "有効なメールアドレスを入力してください",
    "account.register.emailAlreadyRegistered": "このメールアドレスはすでに登録されています",
    "account.register.passwordLengthInvalid": "パスワードは8-20文字の間で入力してください",
    "account.register.passwordTooSimple": "パスワードは、英数字と特殊文字を2種類以上含むように設定してください",
    "account.register.passwordInvalidChars": "パスワードに使用できない記号が含まれています",
    "account.register.passwordMismatch": "2回入力したパスワードが一致しません",

    // 登录相关
    "account.login.emailEmpty": "メールアドレスを入力してください",
    "account.login.passwordEmpty": "パスワードを入れてください",
    "account.login.emailInvalid": "有効なメールアドレスを入力してください",
    "account.login.emailNotRegistered": "プレイヤーが存在しません",
    "account.login.credentialsInvalid": "メールアドレスまたはパスワードが間違いました。もう一度入力してください",
    "account.login.tooManyAttempts": "誤ったパスワードを入力した回数が多すぎます。\nしばらく経ってから、もう一度お試しください。",

    // 验证码相关
    "account.verification.emailEmpty": "メールアドレスを入力してください",
    "account.verification.passwordEmpty": "パスワードを入れてください",
    "account.verification.emailInvalid": "有効なメールアドレスを入力してください",
    "account.verification.codeSent": "認証コード送信済み",
    "account.verification.noSecurityEmail": "このアカウントはメール認証が行われていません",
    "account.verification.emailNotFound": "プレイヤーが存在しません",
    "account.verification.codeInvalid": "正しい認証コードを入力してください",
    "account.verification.codeReused": "入力された認証コードの有効期限が過ぎているため、認証コードの再送信と入力をお願いします。",

    // 邮箱绑定相关
    "account.email.alreadyBound": "このメールアドレスはすでに登録されています",
    "account.email.bindSuccess": "アカウント名の連携に成功しました",
    "account.email.accountBindSuccess": "アカウント名の設定とメールアドレスの認証が完了しました",
    "account.email.empty": "メールアドレスを入力してください",
    "account.email.passwordEmpty": "パスワードを入れてください",
    "account.email.invalid": "有効なメールアドレスを入力してください",
    "account.email.codeEmpty": "認証コードを入力してください",
    "account.email.codeSent": "認証コード送信済み",
    "account.email.verificationSuccess": "メールアドレス認証済み",

    // 通用错误
    "account.error.gameLimitExceeded": "メールアドレスに連携されたアカウント数が上限に達しました",
    "account.error.tooManyRequests": "認証コードの送信が短時間内に繰り返されすぎています。時間をおいてお試しください",
    "account.error.dailyLimitExceeded": "本日の認証コード取得回数が上限に達しています",
    "account.error.codeExpired": "入力された認証コードの有効期限が過ぎているため、認証コードの再送信と入力をお願いします。",

    // 手机绑定相关
    "account.phone.empty": "携帯電話番号を入力してください",
    "account.phone.invalid": "有効な電話番号を入力してください",
    "account.phone.bindSuccess": "携帯番号認証済み",
    "account.phone.changeBindSuccess": "携帯番号の再設定が完了しました",
    "account.phone.areaCodeEmpty": "市外局番を入力してください",
    "account.phone.areaCode": "市外局番",

    // 密码相关
    "account.password.originalEmpty": "変更前のパスワードを入力してください",
    "account.password.newEmpty": "新しいパスワードを入力してください",
    "account.password.originalIncorrect": "変更前のパスワードが間違っています。もう一度入力してください",
    "account.password.sameAsCurrent": "新しいパスワードが現在のパスワードと同じです",
    "account.password.mismatch": "新しいパスワードが一致しません。再度入力してください",
    "account.password.changeSuccess": "パスワード変更が完了しました。もう一度ログインしてください",

    // 忘记密码
    "account.forgotPassword.noSecurityContact": "アカウントがセキュリティ検証を完了していません",

    // 昵称相关
    "account.nickname.empty": "新しいプロフィール名を入力してください",
    "account.nickname.tooShort": "プロフィール名の長さは3文字以上で入力してください",
    "account.nickname.tooLong": "入力したプロフィール名が長すぎます。再度入力してください",
    "account.nickname.sameAsCurrent": "入力したプロフィール名が現在のプロフィール名と同じです。再度入力してください",
    "account.nickname.containsSensitiveWords": "ニックネームに不適切な文字が含まれています。再入力してください",
    "account.nickname.editLimitReached": "ニックネーム変更は180日間できません。しばらくお待ちください。",
    "account.nickname.setSuccess": "ニックネームが設定されました",

    // 其他错误
    "account.error.network": "ネットワークが不安定です。しばらくしてから再試行してください",
    "account.error.loginExpired": "登録情報が無効になりました。もう一度ログインしてください",

    // 注销账号
    ...{
        "account.security.deregister.s10.t1": "アカウント削除申請中",
        "account.security.deregister.s8.t1": "申請が却下されました",
        "account.security.deregister.s9.t1": "アカウント削除の審査中",
        "account.security.deregister": "アカウントの削除",
        "account.security.deregister.apply": "アカウントの削除申請",
        "account.security.deregister.desc": "アカウントの削除は、申請後30日以内に完了します。",
        "account.security.deregister.popup.title": "Playbestアカウントを削除",
        "pages.security.deregister.8": "{gamename}ゲームアカウントを削除",
        "account.security.deregister.s1.title": "ご注意！Playbestアカウントの削除申請中です！",
        "account.security.deregister.s1.p1": "申請中のアカウント：{XXX}",
        "account.security.deregister.s1.p2": "完了すると、アカウントは完全に削除され、復元することはできません。",
        "account.security.deregister.s1.p3": "アカウント内のすべてのゲーム・通貨残高・未使用のサービス・個人情報および履歴も完全に削除されます。",
        "account.security.deregister.s1.f1": "Playbestアカウントを削除する場合は、手続きを完了してください。",
        "account.security.deregister.nextStep": "次へ",
        "account.security.deregister.s3.btn1": "次へ({XXX}s)",
        "account.security.deregister.s3.t1": "アカウント削除の条件を読み、\nページの下部にあるボックスにチェックを入れてください",
        "account.security.deregister.s4.t1": "「アカウント削除に関する注意事項」を読み、\nページの下部にあるボックスにチェックを入れてください",
        "account.security.deregister.s3.check": "私はアカウント削除条件をよく読み、条件を満たしていました",
        "account.security.deregister.s4.check": "「アカウント削除に関する注意事項」を読み、同意します",
        "account.security.deregister.s3.p1": "アカウントと保有資産の安全を確保するため、削除申請が有効になる前に、以下の条件を満たしていることを確認してください。また、スタッフが状況に応じて確認を行う権利を有します:",
        "account.security.deregister.s3.p2": "1、アカウントが安全な状態であること: アカウントが正常に使用されており、違反処理（ゲーム関連の規約やルールに違反してアカウントが封鎖されるなどの処置）が行われていないこと、盗難や封鎖のリスクがないこと\n2、ゲームの利益が清算されているか適切に処理されていること: ゲーム内の貨幣、その他のゲームバーチャルアイテム、ゲームの付加価値サービス（以下「ゲームの利益」と総称）を含む。適切に処理してください。処理されない場合、そのゲームの利益を放棄するものと見なされます\n3、アカウントに未完了の注文がないこと: ゲームトークンのチャージ、その他のゲームバーチャルアイテムや周辺商品など\n4、アカウントが紛争状態でないこと: 苦情や通報、苦情や通報の受理、仲裁、訴訟などの紛争がないこと\n5、アカウントにリスクがないこと: 最近1ヶ月以内に異常なログイン記録がなく、その他のセキュリティリスクがないこと\n6、アカウントに未完了の取引がないこと: アカウント内に未完了の注文や取引がないこと（プラットフォームの周辺商店での未完了の注文を含む）\n7、アカウントに契約がないこと: アカウントの削除によって未解決の契約関係やその他の権利義務が存在しないこと、またはこのプラットフォームがアカウントの削除により未解決の権利義務が発生するとの見解がないこと\n8、その他満たすべき条件\n\n*注意: アカウント削除のクールタイムは30日です。審査期間中はアカウントが凍結されます。削除申請を撤回したい場合は、凍結期間中にゲームにログインして凍結を解除するか、カスタマーサポート窓口にお問い合わせください。凍結期間が終了した後、アカウントは完全に削除されます。\nユーザーがゲーム内で複数の地域にキャラクターを作成できる場合、ゲームアカウントの削除は、すべての地域およびキャラクターに対して適用されます。",
        "account.security.deregister.s4.p1": "1. 削除前に、Playbestアカウントが次の全ての条件を満たしていることを確認する必要があります。いずれかの条件を満たさない場合、Playbestはアカウント削除の申請を拒否する権利を有します。\na) 削除申請の際に、Playbestアカウントは正常に使用でき、いかなるアカウントのセキュリティリスクにつながる恐れはありません（アカウントのブロックまたは凍結・アカウントの異常なログイン動作・異常な使用動作などが含まれ、これらに限定されません）。\nb) 所有権に関する紛争はなく、他人のアカウントの違法な使用・第三者とのアカウントの共有および/またはその他のアカウントの使用権をめぐって紛争が生じたり、第三者の正当な権利と利益を損害したりする恐れがありません。\n2. 「アカウント削除に関する注意事項」を読んで同意して、アカウントの安全性確認と個人情報の確認を完了した後、アカウントは正式にアカウント削除手続きのクールダウン期間（以下「クールダウン期間」ともいいます）に入ります。クールダウン期間は【30】日間となります。期間中にこのアカウントでログインしたり、いずれかのPlaybestサービスを使用したりする必要がある場合は、ログインページからアカウントを再アクティブ化することができます（つまり、アカウント削除のキャンセル手続きが行えます）。クールダウン期間中に、このアカウントでログインしたり、このアカウントを通じてPlaybestサービスを使用したりすることがなかった場合、アカウントはクールダウン期間満了後に削除されます。\n3. Playbestアカウント削除の結果\na) Playbestアカウントが削除されると、アカウントでログインしPlaybestサービスを利用することができなくなり、同じ登録情報で再度登録していただいた場合でも、かつて利用していたいかなるコンテンツやデータを復元することはできません。\nb) このアカウントのログインとPlaybestサービスの使用によって生成されたすべてのコンテンツ・情報・データ・キャラクターおよび記録は、削除または匿名化されます（法律法令で別途規定されている場合を除く）。前述の削除または匿名化されたコンテンツ・情報・データ・記録はアクセス・送信・取得・継続使用または復元することができなくなり、Playbestにそれらの復元を要求する権利も有しません。\nc) Playbestアカウントの使用中に取得したアカウント内のリチャージ残高および関連するバーチャルプロップ・過去の戦績・各種クーポン・優先サービスカード・月間パス・週間パス・有効期限が切れていないプロップ・完了していないタスク・アカウント内で発生したがまだ消費されていないその他の権利と利益および/または発生が予想される権利および利益などは、放棄されたものとみなされ、継続して使用または復元することはできません。\nD) アカウントの削除が完了すると、アカウントを登録して、アカウントと関連Playbestサービスを使用する際に当社と締結したすべての契約は、関連契約中に引き続き有効であると合意された部分と法律法令に別段の定めがある場合を除き、全部終止されます。\ne) Playbestアカウントを削除しても、削除される前に発生したアカウントの行為や、それに関連して発生した責任が免除または軽減されることはできません。\n4. Playbestアカウントが削除された後、アカウントに関係する個人情報とデータは削除または匿名化されます。適用される法律法令で認められる最大限の範囲で、特定の状況においてはお客様の個人情報を削除できない場合があることを認め、これに同意するものとします。これらの状況には下記が含まれます：\na) 当社のビジネス・システム・ユーザーを不正行為から保護するため；\nb) 既存の機能を損なう技術的問題に対応する；\nc) 他のユーザーが権利を行使するために必要とする場合；\nd) 法的手続きに従って法律執行に係る要求を遵守する；\ne) 科学的または歴史的研究用；\nf) 当社とユーザーの関係に合理的に関連する当社の内部目的のため、または法的義務を果たすため。",
        "pages.security.deregister.23": "1. ゲームアカウントを削除することはPlaybestパスの通常の使用に影響することはなく、このパスアカウントを通じて引き続き他のPlaybestゲームやPlaybest公式ウェブサイトにログインできます。\n2. 削除する前に、ゲームアカウントが次の全ての条件を同時に満たしていることを確認する必要があります。いずれかの条件を満たさない場合、Playbestはアカウント削除の申請を拒否する権利を有します。\na) 削除を申請する際に、アカウントは正常に使用でき、いかなるアカウントのセキュリティリスクにつながる恐れもありません（パスアカウントのブロックまたは凍結・アカウントの異常なログイン動作・異常な使用動作などが含まれるが、これらに限定されません）。\nb) 所有権に関する紛争はなく、他人のアカウントの違法な使用・第三者とのアカウントの共有および/またはその他のアカウントの使用権をめぐって紛争が生じたり、第三者の正当な権利と利益を損害したりする恐れのある状況がありません。\n3. 「アカウント削除に関する注意事項」を読んで同意して、アカウントの安全性確認と個人情報の確認を完了した後、アカウントは正式にアカウント削除手続きのクールダウン期間（以下「クールダウン期間」ともいいます）に入ります。クールダウン期間は【30】日間となります。期間中にこのアカウントをログインしたり、当該ゲームのいずれかのサービスを使用したりする必要がある場合は、ログインページからアカウントを再アクティブ化することができます（つまり、削除手続きのキャンセル処理が行えます）。クールダウン期間中に、このアカウントでログインしたり、このアカウントを通じていかなるゲームのサービスを使用したりすることがなかった場合、アカウントはクールダウン期間満了後に削除されます。\n4. ゲームアカウントの削除の結果\na) ゲームアカウントが削除されると、このアカウントをログインし当該ゲームのサービスを使用することができなくなり、同じ登録情報で再度｛ ｝アカウントを登録しても、アカウントのログインによりアカウントに設定したいかなるコンテンツやデータを復元することはできません。\nb) このアカウントのログインと｛ ｝サービスの使用によって生成されたすべてのコンテンツ・情報・データ・キャラクターおよび記録は削除または匿名化されます（法律法令で別途規定されている場合を除く）。前述の削除または匿名化されたコンテンツ・情報・データ・記録はアクセス・送信・取得・継続使用または復元することができなくなり、Playbestにそれらの復元を要求する権利も有しません。\nc) ｛gamename｝アカウントの使用中に取得したアカウント内のリチャージ残高および関連するバーチャルプロップ・過去の戦績・各種クーポン・優先サービス・月間パス・週間パス・有効期限が切れていないプロップ・完了していないタスク・アカウント内で発生したがまだ消費されていないその他の権利と利益および/または発生が予想される権利および利益などは、放棄されたものとみなされ、継続して使用または復元することはできません。\nd) アカウントの削除が完了すると、アカウントを登録して、アカウントと関連ゲームのサービスを使用する際に当社と締結したすべての契約は、関連契約中に引き続き有効であると合意された部分と法律法令に別段の定めがある場合を除き、全部終止されます。\ne) ゲームアカウントを削除しても、アカウントが削除前に発生したアカウントの行為や、それに関連して発生した責任が免除または軽減されることはできません。\n5. ｛ ｝アカウントが削除された後、アカウントに関係する個人情報とデータは削除または匿名化されます。適用される法律法令で認められる最大限の範囲で、特定の状況においてはお客様の個人情報を削除できない場合があることを認め、これに同意するものとします。これらの状況には下記が含まれます：\n(a) 当社のビジネス・システム・ユーザーを不正行為から保護するため；\n(b) 既存の機能を損なう技術的問題に対応する；\n(c) 他のユーザーが権利を行使するために必要とする場合；\n(d) 法的手続きに従って法律執行に係る要求を遵守する；\n(e) 科学的または歴史的研究用；\n(f) 当社とユーザーの関係に合理的に関連する当社の内部目的のため、または法的義務を果たすため。",
        "pages.security.deregister.24": "次へ",
        "pages.security.deregister.25": "アカウント削除を行わない",
        "account.security.deregister.s5.t1": "アカウント削除の理由を選択してください",
        "pages.security.deregister.27": "アカウント削除",
        "account.security.deregister.s5.t3": "最大3つまで選べます",
        "account.security.deregister.s5.p1": "ゲームが面白くなかった",
        "account.security.deregister.s5.p2": "特典が少なく楽しめない",
        "account.security.deregister.s5.p3": "チャージに関する問題",
        "account.security.deregister.s5.p4": "単にゲームをやめるだけ",
        "account.security.deregister.s5.p5": "このアカウントをもう使わない",
        "account.security.deregister.s5.p6": "その他",
        "account.security.deregister.s5.p7": "その他の理由を記入してください、（最大30文字）",
        "account.security.deregister.s5.b1": "提出",
        "account.security.deregister.s6.t1": "アカウントを削除してもよろしいですか？",
        "account.security.deregister.s6.p1": "弊社の製品をご支援いただき誠にありがとうございます。",
        "account.security.deregister.s6.p2": "手続きが完了すると、アカウントが削除されます。慎重に操作してください。",
        "account.security.deregister.s6.p3": "削除処理が完了すると、アカウントデータ及びこのアカウントをログインした後に生成された使用データは完全に削除され、復元することはできません。慎重に選択し操作してください。",
        "account.security.deregister.s6.b1": "次へ",
        "account.security.deregister.s6.b2": "考え直す",
        "account.security.deregister.s7.t1": "削除申請を提出しました",
        "account.security.deregister.s7.t1-1": "弊社の製品をご利用いただき誠にありがとうございました",
        "pages.security.deregister.45": "対象アカウント：",
        "account.security.deregister.s7.p2": "申請日時：{XXX}",
        "account.security.deregister.s7.p3": "法令で定められた場合およびアカウント削除契約書に定められた場合を除き、アカウント削除の審査は申請提出日から30営業日以内に完了します。",
        "account.security.deregister.s7.d2": "審査期間中に、アカウントは凍結されクールダウン期間となります。削除申請を取り消したい場合は、期間中にゲームにログインして解除するか、弊社までご連絡ください。アカウントはクールダウン期間満了後に完全に削除されます。",
        "account.security.deregister.s7.d3": "「終了」ボタンをクリックしてログアウトしてください。",
        "account.security.deregister.s7.b1": "終了",
        "account.security.deregister.s3.btn2": "手続きをやめる",
        "pages.security.deregister.52": "アカウント削除をキャンセルして戻る",
        "pages.security.deregister.53": "アカウント削除申請中",
        "account.security.deregister.s5.t2": "対象アカウント：{XXX}",
        "pages.security.deregister.55": "申請日時：",
        "pages.security.deregister.56": "アカウントの削除申請を提出しました",
        "account.security.deregister.s10.t2": "削除は申請日から30日間以内に完了します",
        "pages.security.deregister.58": "法令で定められた場合およびアカウント削除契約書に定められた場合を除き、アカウント削除の審査は申請提出日から30営業日以内に完了します。",
        "account.security.deregister.s8.p4": "アカウント削除手続きを中止する場合は、「手続きをやめる」をクリックしてください。削除アカウントを見直す場合は、「アカウントを変更」をクリックし、別のアカウントでログインしてください。",
        "pages.security.deregister.60": "手続きをやめる",
        "account.security.deregister.s8.b1": "アカウント変更",
        "pages.security.deregister.62": "アカウントの削除申請は拒否されました",
        "pages.security.deregister.63": "対象アカウント：",
        "pages.security.deregister.64": "申請日時：",
        "account.security.deregister.s8.t2": "審査の結果、アカウントには次のリスクがありましたため、一時的にキャンセルの対象外となります",
        "pages.security.deregister.67": "削除を取り消したい場合は「手続きをやめる」をクリックしてください。アカウント削除を続ける場合は、「アカウント変更」をクリックし、別のアカウントでログインしてください。",
        "pages.security.deregister.68": "手続きをやめる",
        "pages.security.deregister.69": "アカウント変更",
        "pages.security.deregister.70": "アカウントの削除申請を審査しています",
        "pages.security.deregister.71": "対象アカウント：",
        "pages.security.deregister.72": "申請日時：",
        "pages.security.deregister.73": "アカウント削除の審査中",
        "account.security.deregister.s9.p3": "アカウントの削除申請を受け取りましたが、システムがアカウントに異常を検出したため、申請が人工審査担当に転送されました。",
        "account.security.deregister.s9.p4": "人工審査は時間がかかる場合があるため、しばらくお待ちください。ご理解のほどよろしくお願いいたします。",
        "account.security.deregister.s9.p5": "削除を取り消したい場合は「手続きをやめる」をクリックしてください。アカウント削除を続ける場合は、「アカウント変更」をクリックし、別のアカウントでログインしてください。",
        "pages.security.deregister.77": "手続きをやめる",
        "pages.security.deregister.78": "アカウント変更",
        "account.security.deregister.msg2": "アカウントの削除を取り消しました",
        "account.security.deregister.msg1": "アカウントの削除理由を1つ以上お選びください。",
        "pages.security.deregister.81": "1日1回しかアカウントの削除申請を行うことはできません。明日申請してください。",
        "pages.security.deregister.82": "アカウント削除申請中のため、お支払いができません。",
        "pages.security.deregister.83": "ご不明な点がございましたら、カスタマーサポート窓口までお問い合わせください。",
        "pages.security.deregister.84": "アカウント削除申請中。もし何か問題があれば、カスタマーサービスにご連絡ください。または、アカウントセンターにログインして対応してください。",
        "pages.security.deregister.85": "申請が却下されました。もし何か問題があれば、カスタマーサービスにご連絡ください。または、アカウントセンターにログインして対応してください。",
        "pages.security.deregister.86": "アカウント削除の審査中。もし何か問題があれば、カスタマーサービスにご連絡ください。または、アカウントセンターにログインして対応してください。",
        "pages.security.deregister.s7.app": "ページを閉じると、現在のアカウントからログアウトされます。"
    },

    "account.inner.lastName": "みょうじ",
    "account.inner.firstName": "なまえ",
    "account.inner.goToSetting": "設定",
    "account.inner.nicknameNotSet": "ニックネーム未設定",
    "account.inner.bind": "認証",
    "account.inner.verify": "認証へ",
    "account.inner.changeBinding": "変更",
    "account.inner.bound": "連携済み",
    "account.inner.passwordManagement": "パスワード管理",
    "account.inner.lastUpdate": "最終更新",
    "account.inner.update": "更新",
    "account.inner.switchAccount": "アカウントを切り替え",
    "account.inner.deleteAccount": "アカウントの削除",
    "account.inner.unbind": "紐付け解除",
    "account.inner.unbindConfirm": "{media_name}の認証を解除しますか？",
    "account.inner.unbindConfirmDesc": "解除すると、{media_name}のアカウント{nickname} を連携アカウントとしてログインすることができなくなります。",
    "account.inner.hint":"ヒント",
    "account.inner.confirmSwitchAccount": "アカウントを切り替えてもよろしいですか？",
    "account.inner.step.verify": "検証",
    "account.inner.step.finished": "終了！",
    "account.inner.step.verifySuccess": "検証成功",
    "account.inner.step.ok": "よし",
    "account.inner.bindAccount.desc": "設定後、メールアドレスとパスワードでログインできるようになります。",
    "account.inner.unbindAccount.onlyOne": "ゲームに正常にログインできるようにするため、少なくとも1つの外部アカウントによるログイン方法を設定してください。 または、アカウント名と連携してから解除してください。",
    "account.inner.unbindAccount.unbindSuccess": "連携解除",
    "account.inner.setAccountName.title": "ユーザー名を設定する",
    "account.inner.setAccountName.success": "設定しました",
    "account.inner.setAccountName.desc": "アカウント名の設定が完了しました",
    "account.inner.emailVerificationSuccess": "メールボックスを認証しました",
    "account.inner.emailVerificationSuccessDesc": "メールボックスの認証が完了しました。\nこのメールボックス宛に通知や確認メッセージが送信されます。",
    "account.inner.changeEmailSuccess": "メールボックスを変更しました",
    "account.inner.changeEmailSuccessDesc": "メールボックスを変更しました。\n新しいメールボックス宛に通知や確認メッセージが送信されます。",
    "account.inner.phoneBindSuccess": "携帯電話番号の認証しました",
    "account.inner.phoneBindSuccessDesc": "携帯電話番号の認証が完了しました。\nこの番号宛に通知や確認メッセージが送信されます。",
    "account.inner.changePhoneSuccess": "携帯電話番号を変更しました",
    "account.inner.changePhoneSuccessDesc": "新しい番号宛に通知や確認メッセージが送信されます。",
    "account.inner.changePasswordSuccess": "パスワードを変更しました。",
    "account.inner.changePasswordSuccessDesc": "一度ログインし直してください。",
    "account.inner.bindSuggestion": "アカウントのセキュリティ保護のため、可能な限りメールアドレスまたはアカウント連携を行うことをお勧めいたします。また、連携を行っていない場合、一部の機能が制限される場合があります。",
    "account.inner.bindSuccess": "連携完了",
    "account.profile.editProfile": "プロフィールを編集する",
}

