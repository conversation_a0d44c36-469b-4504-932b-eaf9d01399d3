export type ElementOrSelector =
    | Element
    | Element[]
    | NodeListOf<Element>
    | string;


export type ViewChangeHandler = (entry: IntersectionObserverEntry) => void

type MarginValue = `${number}${'px' | '%'}`
type MarginType =
    MarginValue |
    `${MarginValue} ${MarginValue}` |
    `${MarginValue} ${MarginValue} ${MarginValue}` |
    `${MarginValue} ${MarginValue} ${MarginValue} ${MarginValue}`

export interface InViewOptions {
    root?: Element | Document
    margin?: MarginType
    amount?: "some" | "all" | number
}


export interface WithQuerySelectorAll {
    querySelectorAll: Element["querySelectorAll"]
}

export type SelectorCache = { [key: string]: NodeListOf<Element> }

export function resolveElements(
    elements: ElementOrSelector,
    selectorCache?: SelectorCache
): Element[] {
    if (typeof elements === "string") {
        let root: WithQuerySelectorAll = document;

        if (selectorCache) {
            selectorCache[elements] ??= root.querySelectorAll(elements)
            elements = selectorCache[elements]
        } else {
            elements = root.querySelectorAll(elements)
        }
    } else if (elements instanceof Element) {
        elements = [elements]
    }

    /**
     * Return an empty array
     */
    return Array.from(elements || [])
}

const thresholds = {
    some: 0,
    all: 1,
}

export function inView(
    elementOrSelector: ElementOrSelector,
    onStart: (entry: IntersectionObserverEntry) => void | ViewChangeHandler,
    { root, margin: rootMargin, amount = "some" }: InViewOptions = {}
): VoidFunction {
    const elements = resolveElements(elementOrSelector)

    const activeIntersections = new WeakMap<Element, ViewChangeHandler>()

    const onIntersectionChange: IntersectionObserverCallback = (entries) => {
        entries.forEach((entry) => {
            const onEnd = activeIntersections.get(entry.target)

            /**
             * If there's no change to the intersection, we don't need to
             * do anything here.
             */
            if (entry.isIntersecting === Boolean(onEnd)) return

            if (entry.isIntersecting) {
                const newOnEnd = onStart(entry)
                if (typeof newOnEnd === "function") {
                    activeIntersections.set(entry.target, newOnEnd)
                } else {
                    observer.unobserve(entry.target)
                }
            } else if (onEnd) {
                onEnd(entry)
                activeIntersections.delete(entry.target)
            }
        })
    }

    const observer = new IntersectionObserver(onIntersectionChange, {
        root,
        rootMargin,
        threshold: typeof amount === "number" ? amount : thresholds[amount],
    })

    elements.forEach((element) => observer.observe(element))

    return () => observer.disconnect()
}


export interface UseInViewOptions
    extends Omit<InViewOptions, "root" | "amount"> {
    root?: Element;
    once?: boolean
    amount?: "some" | "all" | number
}

// export function useInView(refDom: Element, { root, margin, amount, once = false }: UseInViewOptions = {}) {
//     const isInView = ref(false);
//     console.log("🚀 ~ useInView ~ refDom.value:111", isInView.value)
//     if (!refDom || (once && isInView)) return isInView;


//     const onEnter = () => {
//         isInView.value = true;
//         console.log("🚀 ~ onEnter ~ isInView:", isInView)


//         return once ? undefined : () => isInView.value = false;
//     }

//     const options: InViewOptions = {
//         root: (root) || undefined,
//         margin,
//         amount,
//     }

//     const isViewUnObserver = inView(refDom, onEnter, options)

//     onUnmounted(() => {
//         isViewUnObserver()
//     })

//     return isInView;
// }
