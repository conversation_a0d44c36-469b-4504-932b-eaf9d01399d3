<template>
    <div class="page-support">
        <div class="header">
            <img src="~/assets/images/support/banner.png" alt="PLAY BEST" />
            <div>{{ $t("pages.index.support.banner") }}</div>
        </div>
        <div class="center-size recommend">
            <div class="recommend-list wrap">
                <template v-for="(item, index) in recommendList" :key="index">
                    <div class="recommend-list-item wrap-item" @click.stop="clickRecommend(item)">
                        <div class="auto-height">
                            <div class="recommend-list-item-content auto-height-content"
                                :style="{ background: `url(${item.ad_thumb}) top center/100% 100% no-repeat` }">
                                <div class="right">
                                    <div class="t-shape"></div>
                                    <div class="info">
                                        <div class="icon">
                                            <img v-if="index === 0"
                                                src="~/assets/images/support/ic_round-account-circle.png" alt="">
                                            <img v-if="index === 1"
                                                src="~/assets/images/support/ant-design_money-collect-filled.png"
                                                alt="">
                                            <img v-if="index === 2"
                                                src="~/assets/images/support/ion_game-controller-outline.png" alt="">
                                        </div>
                                        <div class="title">
                                            <span class="ellipsis-2">
                                                {{ item.title }}
                                            </span>
                                        </div>
                                        <div class="subtitle">
                                            <span class="ellipsis-2">
                                                {{ item.ad_desc }}
                                            </span>

                                        </div>
                                        <div class="btn" @click.stop="clickRecommend(item)">
                                            {{ $t("pages.index.support.see") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <template v-if="list.length > 0">
            <div class="center-size content-title">
                <div class="title">
                    {{ $t("pages.index.support.faq") }}
                    <span></span>
                </div>
                <div class="search">
                    <input type="text" :placeholder="$t('pages.index.support.search')" v-model="searchText"
                        @input="searchInput">
                    <img src="~/assets/images/support/search.svg" alt="">
                </div>
            </div>
            <div class="center-size content-list wrap">
                <template v-for="(item, index) in list" :key="index">
                    <div class="content-list-item-width wrap-item">
                        <div class="content-list-item-height" @click.stop="goToKeFu(item)">
                            <div class="content-list-item-content">
                                <div class="top"
                                    :style="{ background: `url(${item.ad_thumb}) top center/100% 100% no-repeat` }">
                                    <div class="hover">
                                        <div class="hover-title">
                                            {{ item.ad_desc }}
                                        </div>
                                        <div class="hover-button button-outlined">
                                            <img src="~/assets/images/support/icon_kf.png" alt="" />
                                            {{ $t("pages.index.support.consult") }}
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom">
                                    <div class="txt">{{ item.title }}</div>
                                    <div class="link">
                                        <template v-for="(it2, idx2) in item.meta" :key="idx2">
                                            <img src="~/assets/images/support/twitter.png" alt=""
                                                v-if="it2.key === 'twitter'" @click.stop="openLink(it2.value)">
                                            <img src="~/assets/images/support/facebook.png" alt=""
                                                v-if="it2.key === 'facebook'" @click.stop="openLink(it2.value)">
                                            <img src="~/assets/images/support/youtube.png" alt=""
                                                v-if="it2.key === 'youtube'" @click.stop="openLink(it2.value)">
                                            <img src="~/assets/images/support/discord.png" alt=""
                                                v-if="it2.key === 'discord'" @click.stop="openLink(it2.value)">
                                            <img src="~/assets/images/support/lounge.png" alt=""
                                                v-if="it2.key === 'lounge'" @click.stop="openLink(it2.value)">
                                            <img src="~/assets/images/support/bahamut.png" alt=""
                                                v-if="it2.key === 'bahamut'" @click.stop="openLink(it2.value)">
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </template>
        <!--        <div class="footer">-->
        <!--            <div class="footer-title">找不到您想要的信息？</div>-->
        <!--            <div class="footer-buttons">-->
        <!--                <div class="button-outlined">咨询在线客服</div>-->
        <!--                <div class="button-outlined">查看我的问题单</div>-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</template>
<script lang="ts" setup>
import { getTopBanner, getRecommendList, getContentList } from "~/api/support";
import { useAuthStore } from '~/stores/auth.js';
import { useUserStore } from '~/stores/user.js';

const { locale } = useI18n();

const userStore = useUserStore();
const authStore = useAuthStore();
console.log(userStore)
console.log(authStore)

// const topBanner = ref('');
const recommendList = ref<any[]>([]);
const listOrigin = ref<any[]>([]);
const list = ref<any[]>([]);
const searchText = ref('');

const searchInput = async (event?: any) => {
    if (searchText.value !== '') {
        const ls = listOrigin.value.filter((e: any) => e.title.indexOf(searchText.value) > -1);
        list.value = ls
    } else {
        list.value = listOrigin.value
    }
};

const wrapUrl = (url: string): string => {
    const langMap: { [key: string]: string } = {
        'zh-Hans': 'zh-CN',
        'zh-Hant': 'zh-TW',
    }
    const id = authStore.auth_token || 0
    const language = langMap[locale.value] ?? locale.value
    return url + `&language=${language}&id=${id}`;
};

const clickRecommend = (item: any) => {
    const link = item.meta.filter((e: any) => e.key === 'link')[0].value;
    window.open(wrapUrl(link))
};

const goToKeFu = (item: any) => {
    if (authStore.isLogin) {
        const link = item.meta.filter((e: any) => e.key === 'link')[0].value;
        // window.open(`https://liveask.yongyetech.cn/connect?c=8DSONY&g=269&p=89`)
        window.open(wrapUrl(link))
    } else {
        authStore.$patch({ loginModal: true });
    }
};

const openLink = (url: string) => {
    window.open(url)
};

onMounted(() => {
    getRecommendList(locale.value).then(res => {
        recommendList.value = res.data.list
    })
    getContentList(locale.value).then(res => {
        listOrigin.value = res.data.list
        searchInput()
    })
    // getTopBanner(locale.value).then(res => {
    //     topBanner.value = res
    // })
});
</script>
<style lang="scss">
.header_bg_color {
    background: rgba(0, 0, 0, 0.3);

    &.fixed {
        background: rgba(0, 0, 0, 0.8);
    }

    &.blue {
        background: var(--base_color);
    }
}
</style>
<style lang="scss" scoped>
/* ====================================================================== 公共 */
.center-size {
    max-width: 1380px;
    margin: 0 auto;
}

.button-fill {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--base_color);
    border-width: 1.6px;
    border-style: solid;
    border-color: var(--base_color);
    border-radius: 42px;
    color: #fff;
    //&:hover {
    //    //background-color: var(--base_color);
    //}
}

.button-outlined {
    transition: all 0.5s ease 0s;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 42px;
    border-width: 1.6px;
    border-style: solid;
    border-color: var(--base_color);
    background-color: transparent;
    color: #000;

    &:hover {
        background-color: var(--base_color);
        color: #fff;
    }
}

.auto-height {
    height: 0;
    padding-top: 45.45%;
    position: relative;
}

.auto-height-content {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.t-shape {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    transform: skew(-10deg);
    background-color: #F4F6FC;

    &:after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 50%;
        height: 100%;
        transform: skewX(10deg);
        background-color: #F4F6FC;
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ellipsis-2 {
    /* 
    使用 -webkit-line-clamp 时，设置 padding 可能会影响省略号的显示。
    这是因为 -webkit-box 的行为会与容器的内边距（padding）发生冲突，导致内容位置不正确。
    为了确保 padding 不影响省略号的显示，可以使用一个额外的容器来处理内边距。
    */
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: normal;
}

.wrap {
    /* 在父元素中定义 child 继承父元素的变量 */
    --gap: 20px;
    --count: 3;
    /* Flex布局 */
    display: flex;
    /* 允许换行 */
    flex-wrap: wrap;
    /* 设置固定间隙 */
    gap: var(--gap);

    // ==============================================================================================列表宽度warp
    // width: calc((100% - (34px * 2)) / 3);

    // &:nth-child(3n) {
    //     margin-right: 0;
    // }

    // &:not(:nth-child(3n)) {
    //     margin-right: 34px; // 自适应1 计算项宽
    // }
    // ==============================================================================================end
}

.wrap-item {
    flex: 0 0 calc((100% - (var(--gap) * (var(--count) - 1))) / var(--count));

    /* flex 属性是一个简写属性，包含了三个子属性：flex-grow、flex-shrink 和 flex-basis。 */
    /* 第一个 0 代表 flex-grow：表示该项目不增长（即在可用空间增加时，不会扩展）。 */
    /* 第二个 0 代表 flex-shrink：表示该项目不缩小（即在可用空间不足时，不会缩小）。 */

    /* flex: 1 是 CSS Flexbox 中的简写属性，实际上等同于 flex: 1 1 0%，其含义如下： */
    /* flex-grow: 1：表示该项目可以在可用空间增加时扩展，占据额外的空间。1 表示它在同级兄弟元素中会相对增长。 */
    /* flex-shrink: 1：表示该项目可以在可用空间不足时缩小。1 表示它在同级兄弟元素中会相对缩小。 */
    /* flex-basis: 0%：指定了项目的基础宽度，0% 表示项目初始占用空间为零。最终的宽度将由 flex-grow 和 flex-shrink 的计算决定。 */
}

/* ====================================================================== end */
.page-support {
    background: #fff;
    min-height: calc(100vh - 295px);
}

.header {
    max-height: 350px;
    overflow: hidden;
    position: relative;

    >img {
        width: 100%;
        display: block;
    }

    >div {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding-top: 60px;
        display: flex;
        align-items: center;
        justify-content: center;

        color: #FFF;
        text-align: center;
        font-family: "Source Han Sans";
        font-size: 50px;
        font-style: normal;
        font-weight: 900;
        line-height: 1.2;
    }
}

.recommend {
    .recommend-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        padding: 50px 0;
        border-bottom: 1px solid #ECECF0;

        --gap: 34px;
        --count: 3;

        .recommend-list-item {
            .recommend-list-item-content {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;

                border-radius: 16px;
                overflow: hidden;
                background: #fff;
                cursor: pointer;
                transition: all 0.5s ease 0s;

                display: flex;
                justify-content: flex-end;

                &:hover {
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.43);
                    transform: translate(0, -5px);
                }

                .right {
                    width: 50%;
                    position: relative;
                    left: 1px;

                    .t-shape {
                        // opacity: 0.95;
                    }

                    .info {
                        position: relative;
                        z-index: 1;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        flex-flow: column;
                        align-items: flex-end;
                        padding-top: 16px;
                        padding-bottom: 16px;
                        padding-left: 20px;
                        padding-right: 20px;

                        .icon {
                            width: 38px;
                            height: 38px;
                            border-radius: 8px;
                            background: #76ADFF;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            overflow: hidden;
                        }

                        .title {
                            padding-top: 8px;
                            color: #1084EF;
                            text-align: right;
                            font-family: "PingFang SC";
                            font-size: 18px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 1.2;
                        }

                        .subtitle {
                            padding-top: 4px;
                            color: #5F5F5F;
                            text-align: right;
                            font-family: "PingFang SC";
                            font-size: 13px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 1.2;
                            word-break: break-all;
                        }

                        .btn {
                            margin-top: auto;
                            border-radius: 42px;
                            border: 1px solid #C2C2CD;
                            background: #F0F4FD;
                            color: #414141;
                            padding: 0 12px;
                            height: 32px;
                            flex-shrink: 0;
                            text-align: center;
                            font-family: "PingFang SC";
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 32px;
                        }
                    }
                }
            }
        }
    }
}

.content-title {
    padding-top: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
        color: #111;
        font-family: "PingFang SC";
        font-size: 34px;
        font-style: normal;
        font-weight: 400;
        line-height: 180.2%;
        /* 61.268px */
        position: relative;

        >span {
            display: block;
            width: 70px;
            height: 4px;
            flex-shrink: 0;
            border-radius: 4px;
            background: #61B3FF;
            position: absolute;
            top: 65px;
            left: 4px;
        }
    }

    .search {
        width: 412px;
        position: relative;

        input {
            width: 100%;
            height: 48px;
            flex-shrink: 0;
            border-radius: 24px;
            background: #F9F9FA;
            border: 0;
            outline: 0;
            padding: 0 44px 0 16px;
            color: #333;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            &::placeholder {
                color: #979393;
            }
        }

        img {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto 16px auto 0;
            width: 18px;
        }
    }
}

.content-list {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 50px;
    border-bottom: 1px solid #ECECF0;
    padding-top: 31px;

    --gap: 37px;
    --count: 4;

    .content-list-item-width {}

    .content-list-item-height {
        width: 100%; // 自适应2 设置高度
        height: 0; // 自适应2 设置高度
        padding-top: 147.8%; // 自适应2 设置高度
        border-radius: 16px;
        overflow: hidden;
        background: #fff;
        position: relative;
        cursor: pointer;
        transition: all 0.5s ease 0s;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.20);

        &:hover {
            transform: translate(0, -5px);

            .content-list-item-content {
                .top {
                    .hover {
                        opacity: 1;
                    }
                }
            }
        }
    }

    .content-list-item-content {
        height: 100%;
        width: 100%;
        position: absolute;
        left: 0;
        top: 0;

        .top {
            height: 78.8%;
            position: relative;

            .hover {
                width: 100%;
                height: 100%;
                display: flex;
                flex-flow: column;
                justify-content: space-between;
                padding: 26px 20px;
                background: linear-gradient(180deg, rgba(17, 17, 17, 0.4) 0%, rgba(17, 17, 17, 0.8) 76%);
                opacity: 0;
                transition: all 0.5s ease 0s;

                .hover-title {
                    color: #FFF;
                    text-align: justify;
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;


                    display: -webkit-box;
                    -webkit-line-clamp: 9;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .hover-button {
                    min-width: 220px;
                    height: 48px;
                    flex-shrink: 0;
                    color: #FFF;
                    border-color: #FFF;
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;

                    &:hover {
                        border-color: var(--base_color);
                    }

                    >img {
                        margin-right: 16px;
                    }
                }
            }
        }

        .bottom {
            height: 21.2%;
            display: flex;
            flex-flow: column;
            justify-content: center;
            padding: 0 16px;

            .txt {
                font-family: "PingFang SC";
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                line-height: 1;
            }

            .link {
                padding-top: 10px;
                display: flex;

                img {
                    width: 30px;
                    margin-right: 6px;
                }
            }

            .pay_btn {
                text-decoration: none;
                --pay_btn_height: 36px;
                width: 50%;
                height: var(--pay_btn_height);
                line-height: var(--pay_btn_height);
                border-radius: calc(var(--pay_btn_height) / 2);
                vertical-align: middle;
                text-align: center;
                font-size: 18px;
                color: #fff;
                background: var(--base_color);
            }
        }
    }
}

.footer {
    height: 285px;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    background: #fbfcfe;

    .footer-title {
        padding-bottom: 36px;
        color: #111;

        text-align: center;
        font-family: "PingFang SC";
        font-size: 34px;
        font-style: normal;
        font-weight: 400;
        line-height: 180.2%;
        /* 61.268px */
    }

    .footer-buttons {
        display: flex;

        div {
            min-width: 316px;
            height: 68px;

            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            &:first-child {
                margin-right: 88px;
            }
        }
    }
}

@media screen and (max-width: 1280px) {
    .center-size {
        padding-left: 30px;
        padding-right: 30px;
    }

    .recommend .recommend-list {
        --count: 2;
    }
}

@media screen and (max-width: 1024px) {
    .content-list {
        .content-list-item-width {
            .content-list-item-height {
                .content-list-item-content {
                    .top {
                        height: 74%;

                        .hover {
                            .hover-title {
                                font-size: 14px;
                                display: -webkit-box;
                                -webkit-line-clamp: 3;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                            }

                            .hover-button {
                                display: none;
                            }
                        }
                    }

                    .bottom {
                        height: 26%;
                        padding: 0 16px;

                        .txt {
                            font-size: 22px;
                        }

                        .link {
                            padding-top: 12px;

                            >img {
                                width: 26px;
                            }
                        }
                    }
                }
            }
        }
    }
}

// chrome调试平板电脑 768 但 iPad Air 820
@media screen and (max-width: 820px) {
    .header {
        height: 300px;
        overflow: hidden;

        >img {
            width: auto;
            height: 100%;
        }

        >div {
            font-size: 36px;
        }
    }

    .recommend .recommend-list {
        padding: 20px 0;
    }

    .recommend .recommend-list .recommend-list-item .recommend-list-item-content .right .info {
        padding-right: 16px;
        padding-top: 12px;
        padding-bottom: 12px
    }

    .recommend .recommend-list .recommend-list-item .recommend-list-item-content .right .info .title {
        font-size: 16px;
    }

    .recommend .recommend-list .recommend-list-item .recommend-list-item-content .right .info .btn {
        display: none;
    }

    .content-title {
        padding-top: 28px;
        display: flex;
        flex-flow: column;
        align-items: flex-start;

        .title {
            color: #111;
            font-family: "PingFang SC";
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 180.2%;

            /* 50.456px */
            span {
                top: 60px;
                left: 4px;
            }
        }

        .search {
            width: 100%;
            position: relative;
            margin-top: 35px;
            margin-bottom: 3px;
        }
    }

    .content-list {
        --gap: 30px;
        --count: 3;

        .content-list-item-width {

            .content-list-item-height {
                .content-list-item-content {
                    .bottom {
                        padding: 0 16px;

                        .txt {
                            font-size: 20px;
                        }

                        .link {
                            padding-top: 8px;

                            >img {
                                width: 24px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 430px) {
    .center-size {
        padding-left: 16px;
        padding-right: 16px;
    }

    .header {
        height: 200px;
    }

    .header>div {
        padding-top: 50px;
    }

    .recommend .recommend-list {
        padding: 20px 0;
        --gap: 20px;
        --count: 1;
    }


    .content-list {
        --gap: 16px;
        --count: 2;

        .content-list-item-width {
            .content-list-item-height {
                .content-list-item-content {
                    .bottom {
                        padding: 0 12px;

                        .txt {
                            font-size: 18px;
                        }

                        .link {
                            padding-top: 8px;

                            >img {
                                width: 24px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 375px) {
    .content-title .title {
        font-size: 22px;

        span {
            top: 50px;
        }
    }

    .content-list {

        // --gap: 16px;
        // --count: 1; // 不用再小了
        .content-list-item-width {
            .content-list-item-height {
                .content-list-item-content {
                    .bottom {
                        padding: 0 8px;

                        .txt {
                            font-size: 16px;
                        }

                        .link {
                            padding-top: 4px;

                            >img {
                                width: 16px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
