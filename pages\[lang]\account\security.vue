<template>
    <div class="account_wrap">
        <accountSidebar :index="2" ref="accountSidebarRef"></accountSidebar>
        <!-- 右边区域 -->
        <div class="account_right">
            <div class="h2">{{ $t('account.security.title') }}</div>
            <!-- 提示 -->
            <accountHint></accountHint>

            <!-- 密码管理 -->
            <div class="ac_common_box ac_c_mb20 ac_security_box ac_c_r20">
                <div class="ac_header">
                    <div class="h1">{{ $t('account.security.passwordManagement') }}</div>
                    <div class="txt">
                        {{ $t('account.security.passwordUpdateRecommendation') }}
                    </div>
                </div>
                <div class="edit_box">
                    <div class="edit_box_txt">
                        {{ $t('account.security.lastPasswordUpdateTime') }}：{{ userInfo.last_update_pwd_date || '-' }}
                    </div>
                    <div class="edit_box_btn" @click="editPassword">
                        <div class="edit_icon"></div>
                        <div class="txt">{{ $t('account.common.modify') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改密码 -->
    <a-modal
        v-model:visible="showEditPassword"
        :footer="null"
        class="ac_header_choice_pop"
        @onCancel="closeEdit"
        @onOk="closeEdit"
        :destroyOnClose="true"
        :keyboard="true"
        :afterClose="
            () => {
                closeEdit();
            }
        "
    >
            <accountResetPasswordModule :type="editPasswordType" @closeEdit="closeEdit"></accountResetPasswordModule>
    </a-modal>
</template>

<script setup lang="ts">
const accountSidebarRef = ref<any>(null);
import { useAuthStore } from "~/stores/auth";
import { useAccountStore } from "~/stores/account";
const authStore = useAuthStore();
const accountStore = useAccountStore();

// 用户信息
const userInfo:any = computed(() => accountStore.userInfo);
const showEditPassword = ref<boolean>(false);

// 刷新用户信息
const refreshUserInfo = () => {
    accountSidebarRef.value.getUserInfoFun();
};
provide('refreshAccountUserInfo', refreshUserInfo);
// 配置首页样式
const diyWrapStyle = useState("diyWrapStyle", () => "");

// 修改密码
const editPasswordType = ref<string>("");
const editPassword = () => {
    editPasswordType.value = "editPasswordAgain";
    showEditPassword.value = true;
}

const closeEdit = () => {
    editPasswordType.value = "";
    showEditPassword.value = false;
}


onMounted(() => {
    diyWrapStyle.value = "background:#fff;";
});
</script>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
.account_right {
    min-height: 700px;
    flex-direction: column;
}
.ac_security_box {
    width: 100%;
    .ac_header {
        padding: 20px 50px;
        .h1 {
            color: #111;
            font-size: 18px;
            line-height: 30px;
        }
        .txt {
            color: #5f5f5f;
            font-size: 14px;
            line-height: 30px;
        }
    }
}
.edit_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 50px;
    .edit_box_txt {
        color: #5f5f5f;
        font-size: 14px;
    }
    .edit_box_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 320px;
        height: 50px;
        background: #e6ecfc;
        color: #111;
        font-size: 18px;
        border-radius: 10px;
        cursor: pointer;
        &:hover {
            opacity: 0.9;
        }
        .edit_icon {
            width: 25px;
            height: 25px;
            background: url(~/assets/images/account/icon_edit.png) center
                center/100% 100% no-repeat;
            margin-right: 10px;
        }
    }
}
@media screen and (max-width: 1200px) {
    
    .edit_box {
        .edit_box_btn {
            width: 300px;
        }
    }
}
@media screen and (max-width: 768px) {
    .ac_security_box {
        .ac_header {
            padding: 20px;
        }
    }
    .edit_box {
        padding: 20px;
        flex-direction: column;
        .edit_box_txt {
            margin-bottom: 14px;
        }
        .edit_box_btn {
            width: 300px;
            height: 40px;
        }
    }
}
</style>
