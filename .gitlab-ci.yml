#image: sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/php/node:12.18.2-stretch
# image: sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/php/node:18.20.0
image: sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/php/node:20.18.3



variables:
  VERSION: 1.0.0
  DOCKER_REGISTRY: sjb-storage-registry.cn-hangzhou.cr.aliyuncs.com/sdk
  PROJECT_NAME: yopay-web
  # ONLY_DEV: ^develop$|^dev_.*$|^temp_.*$  # 正则表达式模式
  # ONLY_TEST: ^test$|^test_.*$|^hotfix_.*$
  # ONLY_RELEASE: ^release.*$
stages:
  - build
  - deploy
  - docker_master
  - ansible


# 线上环境
deploy_production:
  stage: build
  script:
    - yarn config set strict-ssl false 
    - yarn config set  registry http://registry-npm.test.dc.yokagames.com
    - yarn config set network-timeout 180000 -g
    - yarn config set yarn-offline-mirror-pruning false

    # 国内代理
    #- yarn config set proxy http://***********:3128
    #- yarn config set https-proxy http://***********:3128
    # 海外代理
    #- yarn config set proxy http://***********:1081
    #- yarn config set https-proxy http://***********:1081
    - rm -rf yarn.lock
    #- yarn install --registry http://registry-npm.test.dc.yokagames.com
    -  yarn install --registry http://registry.npmmirror.com


    - yarn run build
  tags:
    #- docker
    - docker-80138
  artifacts:
    paths:
      - .output/
      - frontend.conf
  only:
    - tags
    - master  # 只有当代码提交到 master 分支时才会触发部署到生产环境的流程



# 测试环境 地址：https://payment-test.playbest.net/
deploy_test:
  stage: build
  script:
    #- npm install -g yarn
    #- yarn config set registry http://registry-npm.test.dc.yokagames.com
    #- yarn config set registry https://registry.yarnpkg.com/
    - yarn config set strict-ssl false 
    # 清除缓存  
    #- yarn cache clean --force
    - yarn config set http://registry.npmmirror.com
    #- yarn config set registry http://registry-npm.test.dc.yokagames.com
    
    - yarn config set network-timeout 180000 -g
    - yarn config set yarn-offline-mirror-pruning false

    # 国内代理
    #- yarn config set proxy http://***********:3128
    #- yarn config set https-proxy http://***********:3128
    # 海外代理
    #- yarn config set proxy http://***********:1081
    #- yarn config set https-proxy http://***********:1081
    - rm -rf yarn.lock
    #- yarn install --registry http://registry-npm.test.dc.yokagames.com
    - yarn install --registry http://registry.npmmirror.com

    - yarn run build:test
  tags:
    #- docker
    - docker-80138
  artifacts:
    paths:
      - .output/
      - frontend.conf
  only:
    - /^test$|^test_.*$|^hotfix_.*$/



# 开发环境 地址：https://payment-dev.playbest.net/
deploy_develop:
  stage: build
  script:
    - yarn config set registry http://registry-npm.test.dc.yokagames.com
    - yarn config set strict-ssl false 
    
    - yarn config set network-timeout 180000 -g
    - yarn config set yarn-offline-mirror-pruning false
    #- yarn config set registry https://registry.yarnpkg.com/
    - yarn config set network-timeout 180000 -g
    - yarn config set yarn-offline-mirror-pruning false

    # 国内代理
    - yarn config set proxy http://***********:3128
    - yarn config set https-proxy http://***********:3128
    # 海外代理
    #- yarn config set proxy http://***********:1081
    #- yarn config set https-proxy http://***********:1081
    - rm -rf yarn.lock
    #- yarn install --registry http://registry-npm.test.dc.yokagames.com
    - yarn install --registry http://registry.npmmirror.com

    - yarn run build:dev
  tags:
    #- docker
    - docker-80138
  artifacts:
    paths:
      - .output/
      - frontend.conf
  only: 
    - /^develop$|^dev_.*$|^temp_.*$/



# 预发布环境
deploy_release:
  stage: build
  script:
    #- yarn config set  registry http://registry-npm.test.dc.yokagames.com
    - yarn config set strict-ssl false 
    - yarn config set network-timeout 180000 -g
    - yarn config set yarn-offline-mirror-pruning false

    # 国内代理
    - yarn config set proxy http://***********:3128
    - yarn config set https-proxy http://***********:3128
    # 海外代理
    #- yarn config set proxy http://***********:1081
    #- yarn config set https-proxy http://***********:1081
    - rm -rf yarn.lock
    #- yarn install --registry http://registry-npm.test.dc.yokagames.com
    - yarn install --registry http://registry.npmmirror.com

    - yarn run build:release
  tags:
    #- docker
    - docker-80138
  artifacts:
    paths:
      - .output/
      - frontend.conf
  only:
    - /^release.*$/



# docker
# 测试环境 docker
docker_test_build:
  stage: deploy
  script:
    - ls
    - docker build --pull -t "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID} .
    - docker push "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID}
  tags:
    - shell
  dependencies:
    - deploy_test
  only:
    - /^test$|^test_.*$|^hotfix_.*$/


# 开发环境 docker
docker_dev_build:
  stage: deploy
  script:
    - ls
    - docker build --pull -t "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID} .
    - docker push "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID}
  tags:
    - shell
  dependencies:
    - deploy_develop
  only:
    - /^develop$|^dev_.*$|^temp_.*$/


# 预发布环境
docker_release_build:
  stage: deploy
  script:
    - ls
    - docker build --pull -t "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID} .
    - docker push "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$VERSION"-${CI_PIPELINE_ID}
  tags:
    - shell
  dependencies:
    - deploy_release
  only:
    - /^release.*$/



# 线上环境 docker
docker_build_master:
  stage: docker_master
  script:
    - git checkout master
    - git pull
    - latest_commit=$(git rev-parse master)
    - echo "CI_COMMIT_SHA - $CI_COMMIT_SHA"
    - echo "latest_commit - $latest_commit"
    - if [ $latest_commit != $CI_COMMIT_SHA ]; then echo "tagging and deploying a version is allowed only on the HEAD of master"; exit 1; fi
    - docker build --pull -t "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$CI_COMMIT_TAG" .
    - docker push "$DOCKER_REGISTRY"/"$PROJECT_NAME":"$CI_COMMIT_TAG"
  tags:
    - shell
  dependencies:
    - deploy_production
  only:
    refs:
      - tags

# test
ansible_test_deploy:
  stage: ansible
  script:
    # - ~/ansible_deploy.py -p 44 -t 835 -n $CI_PROJECT_NAME -v $VERSION-${CI_PIPELINE_ID}
    - ~/new_ansible_deploy_sdk.py -n "$PROJECT_NAME" -v $VERSION-${CI_PIPELINE_ID}
  only:
    - /^test$|^test_.*$|^hotfix_.*$/



# dev
ansible_dev_deploy:
  stage: ansible
  script:
    - ~/ansible_deploy.py -p 70 -t 2048 -n $PROJECT_NAME -v $VERSION-${CI_PIPELINE_ID}
  only:
    - /^develop$|^dev_.*$|^temp_.*$/
    
# 预发布环境
ansible_release_build:
  stage: ansible
  script:
    - ~/ansible_deploy.py -p 70 -t 2201 -n $PROJECT_NAME -v $VERSION-${CI_PIPELINE_ID}
  #tags:
  #  - shell
  only:
    - /^release.*$/
