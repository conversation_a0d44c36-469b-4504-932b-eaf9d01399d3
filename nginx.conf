user  nginx;
worker_processes  auto;
pid        /var/run/nginx.pid;

events {
    use epoll;
    worker_connections 51200;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server_names_hash_bucket_size 512;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 500m;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    tcp_nopush     on;
    tcp_nodelay on;

    keepalive_timeout  65;

    # gzip 压缩
    gzip on;
    gzip_min_length  1k;
    gzip_buffers     4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 2;
    gzip_types     text/plain application/javascript application/x-javascript text/javascript text/css application/xml;
    gzip_vary on;
    gzip_proxied   expired no-cache no-store private auth;
    gzip_disable   "MSIE [1-6]\.";

    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    include /etc/nginx/conf.d/*.conf;

    server{
        #开启和关闭gzip模式
        gzip on;
        #gizp压缩起点，文件大于2k才进行压缩;设置允许压缩的页面最小字节数，页面字节数从header头得content-length中进行获取。 默认值是0，不管页面多大都压缩。建议设置成大于2k的字节数，小于2k可能会越压越大。
        gzip_min_length 2k;
        # 设置压缩所需要的缓冲区大小，以4k为单位，如果文件为7k则申请2*4k的缓冲区 
        gzip_buffers 4 16k;
        # 设置gzip压缩针对的HTTP协议版本
        gzip_http_version 1.0;
        # gzip 压缩级别，1-9，数字越大压缩的越好，也越占用CPU时间
        gzip_comp_level 2;
        #进行压缩的文件类型
        gzip_types text/plain application/javascript text/css application/xml;
        # 是否在http header中添加Vary: Accept-Encoding，建议开启
        gzip_vary on;
    }
}
