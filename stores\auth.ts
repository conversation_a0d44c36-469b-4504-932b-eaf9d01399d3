// stores/counter.ts
// 登录态管理器
import { defineStore } from "pinia";

import { ACCESS_TOKEN, APP_LANG } from "~/constants/common";
import { Storage } from "~/utils/Storage";

const maxAge = 60 * 60 * 24 * 60; // 60天
let domain = process.env.VITE_COOKIE_DOMAIN;

if (typeof window !== "undefined") {
    const origin = window.location.origin;
    if (origin.indexOf("funtales.com") > -1) {
        domain = ".funtales.com";
    }
}
// pb 的toekn
const $token = () => {
    return useCookie("auth_token", { maxAge, domain, path: "/" });
};
// 请求的token
const $pb_token = () => {
    return useCookie("pb_token", { maxAge, domain, path: "/" });
};
const $user = () => {
    return useCookie("user", { maxAge, domain, path: "/" });
};

interface stateType {
    appLang: string;
    isLogin: boolean; // 是否登录
    isRoleLogin: any; // 角色登录  根据具体项目
    user: null | string; // 用户名
    auth_token: null | string; // 认证token
    loginModal: boolean; // 是否显示登录框
    loginModalStatus: string; // 登录框状态
    accountPassSwitch: boolean; // 是否显示账密登录
    loginThirdConfig: any[]; // 第三方登录配置列表
}
/**
 * 定义一个名为`auth`的Pinia store，用于管理用户认证状态。
 */
export const useAuthStore = defineStore("auth", {
    // 初始化状态
    state: () =>
        ({
            appLang: "",
            isLogin: false, // 是否登录
            isRoleLogin: {}, // 角色登录  根据具体项目
            user: null, // 用户名
            auth_token: null, // 认证token
            loginModal: false, // 是否显示登录框
            loginModalStatus: "login", // 登录框状态    
            accountPassSwitch: true, // 是否显示账密登录
            loginThirdConfig: [
                {
                    mediaIcon: "Google",
                    game: [],
                },
                {
                    mediaIcon: "Apple",
                    game: [],
                },
                {
                    mediaIcon: "Facebook",
                    game: [],
                },
                {
                    mediaIcon: "Line",
                    game: [],
                },
            ], // 第三方登录配置列表
        } as stateType),
    // 登录操作，更新状态并设置cookie
    actions: {
        setAPPLang(appLang: string) {
            this.appLang = appLang ?? "en";
            Storage.set(APP_LANG, this.appLang);
        },
        // 设置PB登录token
        setPBAuthToken(auth_token: string) {
            this.auth_token = auth_token || null;
            $token().value = auth_token;
            $pb_token().value = auth_token;
            // Storage.set(ACCESS_TOKEN, this.auth_token);
            localStorage.setItem(ACCESS_TOKEN, auth_token);
        },
        setUser(user: string) {
            this.user = user;
            $user().value = user;
        },
        getUser() {
            return $user().value;
        },
        // 是否将 pbToken 赋值到 authToken上
        getPBAuthToken(isReset: boolean = false) {
            if (isReset && $pb_token().value) {
                this.auth_token = $token().value = $pb_token().value || null;
            }
            return {
                token: $pb_token().value || this.auth_token || $token().value,
                user: $user().value,
            };
        },
        // 设置角色登录 token
        setRoleAuthToken(
            itemName: string,
            auth_token: string,
            serverId: string,
            roleId: string
        ) {
            const $roleToken = useCookie(`role_token_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $serverId = useCookie(`serverId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $roleId = useCookie(`roleId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            $roleToken.value = auth_token;
            $serverId.value = serverId;
            $roleId.value = roleId;
            this.auth_token = auth_token || null;
            $token().value = auth_token;
            this.isRoleLogin[itemName] = true; //同步角色登录态
        },
        getRoleAuthToken(itemName: string) {
            const $roleToken = useCookie(`role_token_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $serverId = useCookie(`serverId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $roleId = useCookie(`roleId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            return {
                token: $roleToken.value,
                serverId: $serverId.value,
                roleId: $roleId.value,
            };
        },
        logoutRole(itemName: string) {
            console.log("logoutRole", itemName);
            const $roleToken = useCookie(`role_token_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $serverId = useCookie(`serverId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            const $roleId = useCookie(`roleId_item${itemName}`, {
                maxAge,
                path: "/",
                domain,
            });
            $roleToken.value = null;
            $serverId.value = null;
            $roleId.value = null;
            $token().value = null;
        },
        // 控制登录弹窗
        setLoginModal(status: boolean, statusType: string = "login") {
            this.loginModal = status;
            this.loginModalStatus = statusType;
        },
        login(user: string, auth_token: string) {
            this.isLogin = true;
            this.user = user;
            this.auth_token = auth_token;
            this.loginModal = false;
            if (process.browser) {
                $pb_token().value = auth_token;
                $token().value = auth_token;
                $user().value = user;
                localStorage.setItem("user", user);
                localStorage.setItem("auth_token", auth_token);
            }
        },
        // 登出操作，清除状态及cookie
        logout(callBack: Function | null = null) {
            this.isLogin = false;
            this.user = null;
            this.auth_token = null;
            if (process.browser) {
                // 使用cookie来管理登录态
                $token().value = null;
                $user().value = null;
                $pb_token().value = null;
                localStorage.removeItem("user");
                localStorage.removeItem("auth_token");
            }
            if (callBack) {
                callBack();
            }
        },
        // 页面跳转loading 关闭
        hideLoading() {
            const commonLoading = useState("commonLoading", () => true);
            commonLoading.value = false;
        },
        // 页面跳转loading 关闭
        showLoading() {
            const commonLoading = useState("commonLoading", () => true);
            commonLoading.value = true;
        },
        // 设置第三方登录配置
        setLoginThirdConfig(config: any[]) {
            this.loginThirdConfig = config;
        },
        setAccountPassSwitch(status: string) {
            if (status === "1") {
                this.accountPassSwitch = true;
            } else {
                this.accountPassSwitch = false;
            }
        },
    },
});
