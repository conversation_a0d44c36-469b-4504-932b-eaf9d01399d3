/**
 * 从Nuxt应用程序中获取并使用$axios实例。
 * @param options 请求配置选项。
 * @returns 返回封装后的axios请求。
 */
import URLS from "~/api/uri";
import { $axios } from "~/utils/axiosCustiom";

const siteId = process.env.VITE_CMS_PAYMENT_SITE_ID
// const siteId = 4
/**
 * 推荐广告列表
 * @returns 返回一个Promise，解析为列表数据。
 */
const getRecommendList = (locale:string = 'en') => {
    return $axios({
        url: URLS.Support.ad,
        method: 'POST',
        data:{
            'site_id': siteId,
            'identifier': 'support_recommend',
            'locale': locale,
        }
    }).then(res=>{
        res.data.list = res.data.list.map((e:any) => {
            e.ad_desc = e.ad_desc.replace(/<p>|<\/p>/g, '');
            return e
        });
        return res;
    });
};
/**
 * 推荐顶部横幅
 * @returns 返回一个Promise，解析为列表数据。
 */
const getTopBanner = (locale:string = 'en') => {
    // return $axios({
    //     url: URLS.Support.ad,
    //     method: 'POST',
    //     data:{
    //         'site_id': 1,
    //         'identifier': 'support_top_banner',
    //         'locale': locale,
    //     }
    // }).then(({data}) => {
    //     return data.list[0].ad_thumb;
    // });
};
/**
 * 推荐主内容列表
 * @returns 返回一个Promise，解析为列表数据。
 */
const getContentList = (locale:string = 'en') => {
    return $axios({
        url: URLS.Support.ad,
        method: 'POST',
        data:{
            'site_id': siteId,
            'identifier': 'support_content_list',
            'locale': locale,
        }
    }).then(res=>{
        res.data.list = res.data.list.filter((e:any) => e.title !== '');
        res.data.list = res.data.list.map((e:any) => {
            e.ad_desc = e.ad_desc.replace(/<p>|<\/p>/g, '');
            return e
        });
        return res;
    });
};

export {
    getRecommendList,
    getTopBanner,
    getContentList,
};
