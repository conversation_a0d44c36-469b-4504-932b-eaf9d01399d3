<template>
    <div class="wrap_normal" v-if="isFetchOk == 'OK'">
        <!-- 订单状态 -->
        <div class="order_wrap" :class="isWebview ? 'order_webview_wrap' : ''"
            v-if="orderStatusDetail.order_status_code == 1">
            <div class="title">{{ $t('pages.pay.order-payment') }}</div>
            <div class="order_content">
                <div class="order_pay_time"
                    v-html="$t('pages.pay.please-complete-the-payment-within-XX', { XX: `<span class='order_result_time_value'>${countdown}</span>` })">
                </div>
                <!-- 支付宝支付 -->
                <template v-if="isAlipay">
                    <div class="flex_center" style="margin-bottom: 10px;">
                        <span class="left">{{ $t('pages.pay.item-information') }}： </span>
                        <span class="right">{{ orderStatusDetail.product_name }}</span>
                    </div>
                    <div class="order_detail">
                        <div class="order_detail_h5">{{ $t('pay.order.status.alipay.hint') }}</div>
                        <div class="alipay_price">{{ orderStatusDetail.currency }} {{ orderStatusDetail.amount }}</div>
                        <div class="qrcode_wrap">
                            <img class="qrcode_img" :src="qrCodeUrl" alt="alipay QrCode" />
                            <img class="logo" src="~/assets/images/pay/alipay_hk_logo.jpg" alt="alipay Logo" />
                        </div>
                    </div>
                </template>
                <!-- 其他支付 -->
                <div class="order_detail" v-else>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.item-information') }}</span>
                        <span class="right">{{ orderStatusDetail.product_name }}</span>
                    </div>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.order-price') }}</span>
                        <span class="right">{{ orderStatusDetail.currency }} {{ orderStatusDetail.amount }}</span>
                    </div>
                </div>
                <div class="order_btn c_btn_a" @click="payIsComplete" data-track-id="1001">{{
                    $t('pages.pay.payment-complete') }}</div>
                <a href="javascript:;" class="order_payment_txt" @click="selectOther" data-track-id="1002">{{
                    $t('pages.pay.select-other-payment-method') }}</a>
                <div class="order_hint">
                    <p>{{ $t('pages.pay.note') }}</p>
                    <p>{{ $t('pages.pay.note1') }}</p>
                    <p>{{ $t('pages.pay.note2') }}</p>
                    <p>{{ $t('pages.pay.note3') }}</p>
                </div>
            </div>
        </div>
        <!-- 支付成功 -->
        <div class="order_wrap" :class="isWebview ? 'order_webview_wrap' : ''"
            v-else-if="orderStatusDetail.order_status_code == 3 || orderStatusDetail.order_status_code == 4">
            <div class="title">{{ $t('pages.pay.order-payment') }}</div>
            <div class="order_content">
                <div class="icon_order_status success"></div>
                <div class="order_status_txt">{{ $t('pages.pay.payment-success') }}</div>
                <div class="order_status_hint_txt">{{ $t('pages.pay.payment-success-desc') }}</div>
                <div class="order_detail small">
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.order-number') }}</span>
                        <span class="right">{{ orderStatusDetail.order_id }}</span>
                    </div>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.purchasing-account') }}</span>
                        <span class="right">{{ orderStatusDetail.user_name }}</span>
                    </div>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.purchasing-character') }}</span>
                        <span class="right">{{ orderStatusDetail.role_name }}</span>
                    </div>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.order-price') }}</span>
                        <span class="right">{{ orderStatusDetail.currency }} {{ orderStatusDetail.amount }}</span>
                    </div>
                    <div class="order_detail_line">
                        <span class="left">{{ $t('pages.pay.item-information') }}</span>
                        <span class="right">{{ orderStatusDetail.product_name }}</span>
                    </div>
                </div>
                <div class="order_btn_line">
                    <div class="order_btn c_btn_a" @click="goToPayAgain" data-track-id="1102">{{
                        $t('pages.pay.go-back-payment') }}</div>
                    <div class="order_btn_empty c_btn_a" v-if="isLogin && !isWebview" @click="goMyList"
                        data-track-id="1101">
                        {{ $t('pages.pay.view-order') }}
                    </div>
                </div>
            </div>
        </div>
        <!-- 支付失败 -->
        <div class="order_wrap" :class="isWebview ? 'order_webview_wrap' : ''"
            v-else-if="orderStatusDetail.order_status_code == 2">
            <div class="title">{{ $t('pages.pay.order-payment') }}</div>
            <div class="order_content">
                <div class="icon_order_status fail"></div>
                <div class="order_status_txt">{{ $t('pages.pay.payment-fail') }}</div>
                <div class="order_status_hint_txt">{{ $t('pages.pay.payment-fail-desc') }}</div>
                <div class="order_btn c_btn_a" @click="goToPayAgain" data-track-id="1201">{{ $t('pages.pay.pay-again')
                    }}</div>
            </div>
        </div>
    </div>
    <!-- 错误情况 -->
    <div class="error_status" :class="isWebview ? 'order_webview_wrap' : ''" v-else-if="isFetchOk == 'no_auth'">
        <div class="icon">
            <div class="c_status_no_auth"></div>
        </div>
        <div class="txt">{{ $t('api.login.lose') }}</div>
        <a-button v-if="isWebview" class="order_webview_cancel" @click="closed" data-track-id="1201">{{
            $t('pages.pay.pay-again')
            }}</a-button>
    </div>
    <!-- 错误情况 -->
    <div class="error_status" :class="isWebview ? 'order_webview_wrap' : ''" v-else>
        <div class="icon">
            <div class="c_status_network_error"></div>
        </div>
        <div class="txt">{{ $t('pages.pay.payment-fail-desc') }}</div>
        <a-button v-if="isWebview" class="order_webview_cancel" @click="closed" data-track-id="1201">{{
            $t('pages.pay.pay-again')
        }}</a-button>
    </div>
</template>
<script lang="ts" setup>
import { getOrderDetail, getWebviewOrderDetail } from "~/api/pay";
import moment from 'moment';
// 登录态管理
import { useAuthStore } from '~/stores/auth'
const authStore = useAuthStore()
const isLogin = computed(() => authStore.isLogin);
const orderStatusDetail = ref<any>({
    order_status_code: 1
});
const router = useRouter();
const { $jsbridge } = useNuxtApp() as any;

const isFetchOk = ref("OK");
// 多语言问题
const { locale, t } = useI18n();
const goMyList = () => {
    navigateTo(`/${locale.value}/my/list`);
    closed();
}

const $prop = defineProps({
    json: {  // 配置数据数据
        type: Object,
        default: {},
    },
    qrCode: { // 支付宝二维码
        type: String,
        default: ''
    },
    otherPage: {   // 判断是否在其他页面中，而不是订单页
        type: Boolean,
        default: false,
    },
    isWebview: { // webview中不展示查看订单按钮
        type: Boolean,
        default: false
    },
    isAlipay: { // 是否是支付宝支付
        type: Boolean,
        default: false
    }
})
const $emit = defineEmits(['setClose']);


// 支付宝二维码
import QRCode from 'qrcode';
const qrCodeUrl = ref<string>('');
// 二维码配置
const qrCodeOpts = {
    errorCorrectionLevel: 'H',
    type: 'image/jpeg',
    quality: 0.5,
    margin: 0,
    width: 200,
    height: 200
}
// With async/await
const generateQR = async (text: string) => {
    try {
        qrCodeUrl.value = await QRCode.toDataURL(text, qrCodeOpts)
    } catch (err) {
        console.error(err)
    }
}

// 倒计时的值
const countdown = ref<string>("30:00");
// 订单关闭时间
let endTime: any = 0;
let countdownTimer: any = null;
let orderStatusTimer: any = null;
let localStorageName = "";
const setCountdown = (time: number = 30) => {
    if ($prop.json.orderId) {
        countdown.value = `${time}:00`;
        endTime = moment().add(time, 'minutes').valueOf();
        localStorageName = `ORDER_END_TIME_${$prop.json.orderId}`;
        if (localStorage.getItem(localStorageName)) {
            endTime = parseInt(localStorage.getItem(localStorageName) || '0');
        } else {
            localStorage.setItem(localStorageName, endTime.toString());
        }
        countdownTimer = setInterval(() => {
            const now = new Date().getTime();
            // console.log(Math.floor((endTime - now) / 1000));
            const duration = moment.duration(endTime - now);
            if (endTime - now > 0) {
                countdown.value = `${duration.minutes().toString().padStart(2, '0')}:${duration.seconds().toString().padStart(2, '0')}` // 更新显示的倒计时文本
            } else {
                clearInterval(countdownTimer) // 当倒计时结束时清除定时器
                countdown.value = '00:00' // 可选：将倒计时文本重置为特定内容
                closed();
            }
        }, 1000) // 每隔1秒执行一次
    }
}

let stopWatchEffect: any;
onMounted(async () => {
    console.log($prop.json);
    stopWatchEffect = watchEffect(() => {
        let { orderId, roleId, uid } = $prop.json;
        // console.log({ orderId, roleId, uid })
        if (orderId) {
            if ($prop.isAlipay) {
                generateQR($prop.qrCode);
                setCountdown(15);
            } else {
                setCountdown();
            }
            getOrderStatus();
            orderStatusTimer = setInterval(() => {
                getOrderStatus();
            }, 5000)
        }
    })
})

// 支付完成 按钮
const payIsComplete = async () => {
    getOrderStatus(true);
}

// 选择其他付款方式
const selectOther = () => {
    if ($prop.isWebview) {
        // webview中跳转到原来的页面
        const query = JSON.parse(localStorage.getItem('WEBVIEW_QUERY') || '{}');
        if (query && Object.keys(query).length > 0) {
            const queryString = new URLSearchParams(query).toString();
            window.location.href = `/webview?${queryString}`;
        } else {
            closed();
        }
    } else {
        closed();
    }
}

// 获取订单详情
const getOrderStatus = async (isCheckResult: boolean = false) => {
    let { orderId, roleId, uid } = $prop.json;
    // webview中调用不同的接口
    const getOrderApi = $prop.isWebview ? getWebviewOrderDetail : getOrderDetail;
    try {
        let result: any = await getOrderApi({ orderId, roleId, uid, language: locale.value });
        YKTrack.setRoleId(roleId || '')
        // console.log("getOrderDetail", result);
        if (result && result.code == 200 || result.code == 0) {
            let { data } = result;
            orderStatusDetail.value = data;
            if (isCheckResult) {
                if (data.order_status_code == 1) {
                    message.error({
                        content: t('pay.order.status.processing'),
                        class: 'c_message_big',
                        style: {
                            marginTop: '20vh',
                        },
                        duration: 3,
                    });
                }else if (data.order_status_code == 2){
                    message.error({
                        content: t('pay.order.not.paid'),
                        class: 'c_message_big',
                        style: {
                            marginTop: '20vh',
                        },
                        duration: 3,
                    });
                }else if (data.order_status_code == 3 || data.order_status_code == 4) {
                    message.success({
                        content: t('pages.pay.payment-complete'),
                        class: 'c_message_big',
                        style: {
                            marginTop: '20vh',
                        },
                        duration: 3,
                    });
                    // 埋点: 订单返回结果 0:成功 1:失败
                    YKTrack.track('server', {
                        params: {
                            id: '807',
                            value1: 0,
                            value2: orderId, // 订单号
                            value3: roleId, // 角色ID
                            value4: uid, // 用户ID
                        }
                    })
                }
                if (!$prop.isWebview) {
                    closeTimer();
                    closed();
                }
            } else {
                // 1  待支付   2  关闭  3  等待发货   4  完毕
                if (data.order_status_code !== 1) {
                    closeTimer();
                    // 埋点: 订单返回结果 0:成功 1:失败
                    YKTrack.track('server', {
                        params: {
                            id: '807',
                            value1: [3, 4].includes(data.order_status_code) ? 0 : 1,
                            value2: orderId, // 订单号
                            value3: roleId, // 角色ID
                            value4: uid, // 用户ID
                        }
                    })
                }
            }
            // $emit('orderStatusChange', data.order_status_code);
        } else {
            if (result.code == 401) {
                // 错误弹窗
                message.error({
                    content: () => t('api.login.lose'),
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration: 3,
                });

                isFetchOk.value = "no_auth";
            } else {
                // 错误弹窗
                message.error({
                    content: () => t('pages.pay.payment-fail-desc'),
                    class: 'c_message_big',
                    style: {
                        marginTop: '20vh',
                    },
                    duration: 3,
                });
                isFetchOk.value = "error";
            }
            closeTimer();
            // 埋点: 订单返回结果 0:成功 1:失败
            YKTrack.track('server', {
                params: {
                    id: '807',
                    value1: 1,
                    value2: orderId, // 订单号
                    value3: roleId, // 角色ID
                    value4: uid, // 用户ID
                }
            })
        }
    } catch (err) {
        console.log(err)
    }
}

// 关闭订单 并跳转去原来支付的页面
const goToPayAgain = () => {
    closed();
    if ($prop.isWebview) return
    if ($prop.otherPage) {
        const cookiePayPage = useCookie(`orderpath_${$prop.json.orderId}`);
        const url = cookiePayPage.value
        cookiePayPage.value = null;
        if (url) {
            navigateTo(url)
        } else {
            navigateTo(`/${locale.value}/home`)
        }
    }
}

onUnmounted(() => {
    stopWatchEffect();
    closeTimer();
    localStorage.removeItem(localStorageName);
})
// 关闭计时器
const closeTimer = () => {
    clearInterval(orderStatusTimer);
    clearInterval(countdownTimer);

};
const closed = () => {
    closeTimer();
    localStorage.removeItem(localStorageName);
    if ($prop.isWebview) {
        let code = -1
        let message = ''
        switch (orderStatusDetail.value.order_status_code) {
            case 2:
                code = 10017
                message = '支付失败'
                break
            case 3:
            case 4:
                code = 0
                message = '支付成功'
                break
            default:
                code = 10018
                message = '支付取消'
                break
        }
        // 发送结果到客户端
        const data = { code: code, data: { orderId: orderStatusDetail.value.order_id, payType: orderStatusDetail.value.pay_method }, message: message };
        $jsbridge.sendPayResult(data);
        console.log('调用jsbridge关闭');
        $jsbridge.close();
    } else {
        $emit("setClose", false);
    }
}
defineExpose({ closed })
</script>


<style lang="scss">
.order_result_time_value {
    color: #3A58A9;
    font-size: 30px;
    margin: 0 6px;
    line-height: 30px;
    vertical-align: baseline;
}
</style>

<style lang="scss" scoped>
@import url('~/assets/styles/components/order.scss');

.order_webview_wrap {
    max-width: 100%;
    height: 100%;
    overflow-y: scroll;
    background-color: #fff;
    border-radius: 20px;

    .order_webview_cancel {
        display: flex;
        justify-content: center;
        margin: 10px auto;
        height: 50px;
        width: 80%;
        text-align: center;
        flex-shrink: 0;
        border-radius: 30px;
        background: #3A58A9;
        font-size: 20px;
        text-align: center;
        color: #fff;
        line-height: 46px;
        cursor: pointer;
        padding: 0;
    }

    .order_content {
        height: calc(100% - 61px) !important;
        overflow-y: scroll;
    }
}
</style>