// stores/counter.ts
// 登录态管理器
import { defineStore } from "pinia";

import {
    PROJECTS,
    APP_ORDER,
    APP_ROLE,
    APP_INFO,
    APP_LANG,
} from "~/constants/common";
import { Storage } from "~/utils/Storage";
// 默认banner
import banner_pc from "~/assets/images/order/list_banner.jpg";
import banner_mb from "~/assets/images/order/list_banner_m.jpg";
/**
 * 定义一个名为`user`的Pinia store，用于管理用户支付状态。
 */
export const useUserStore = defineStore("user", {
    // 初始化状态
    state: () => ({
        cookiesPolicy: true,
        projects: Storage.get(PROJECTS, []),
        appOrder: null,
        APP_LANG: Storage.get(APP_LANG, "en"),
        banner_pc, // 默认banner
        banner_mb,
    }),
    // 登录操作，更新状态并设置cookie
    actions: {
        setProjects(projects: any) {
            this.projects = projects ?? [];
            const ex = 7 * 24 * 60 * 60 * 1000;
            Storage.set(PROJECTS, this.projects, ex);
        },
        // 获取用户cookies协议
        getCookiesPolicy() {
            const $cookiesAgree = useCookie('cookies_agree', { maxAge: 86400 * 365, path: "/", domain: process.env.VITE_COOKIE_DOMAIN });
            return $cookiesAgree.value;
        },
        // 获取用户支付信息
        getUserPayment(projectId:string,SchemeId:string) {
            return localStorage.getItem(`payment_user_${projectId}_${SchemeId}`);
        },
        // 保存用户支付信息
        setUserPayment(projectId:string,SchemeId:string,payment:any) {
            if(projectId && SchemeId && payment) {
                localStorage.setItem(`payment_user_${projectId}_${SchemeId}`, JSON.stringify(payment));
            }
        },
        // 获取用户最近选择的方案
        getUserScheme(projectId:string) {
            return localStorage.getItem(`scheme_user_${projectId}`);
        },
        // 保存用户最近选择的方案
        setUserScheme(projectId:string,SchemeId:string) {
            if(projectId && SchemeId) {
                localStorage.setItem(`scheme_user_${projectId}`, SchemeId);
            }
        },
    },
});
