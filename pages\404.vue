<template>
    <div>
        <div class="order_banner">
            <img class="img banner_pc" :src="banner.pc" alt="" />
            <img class="img banner_mobile" :src="banner.mb" alt="" />
        </div>
    </div>
    <div class="error_status">
        <div class="icon">
            <div class="c_status_404"></div>
        </div>
        <div class="flex_center">
            <div class="txt" @click="handleClick" v-html="goToHomeHtml"></div>
            <div class="txt">({{ count }}s)</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { useAuthStore } from '~/stores/auth.js';
const authStore = useAuthStore();
import { useUserStore } from "~/stores/user";
const { banner_pc, banner_mb } = useUserStore();
const banner = ref({
    pc: banner_pc,
    mb: banner_mb,
})

// 语言
const { locale, t } = useI18n();
const goToHomeHtml = ref("");
const count = ref(10);
// 事件委托
const handleClick = (event: any) => {
    const target = event.target;
    const action = target.getAttribute('data-action');
    if (action === 'gotohome') {
        navigateTo(`/${locale.value}/main`);
    }
};
let timer: any;
onMounted(() => {
    // authStore.hideLoading();
    goToHomeHtml.value = t('page.fetch.fail', {
        XX: `<span class="go_to_home" data-action="gotohome">`,
        YY: `</span>`,
    });

    timer = setInterval(() => {
        count.value--;
        if (count.value === 0) {
            clearInterval(timer);
            navigateTo(`/${locale.value}/main`);
        }
    }, 1000);

});
onBeforeUnmount(() => {
    clearInterval(timer);
});

</script>
<style lang="scss" scoped>
.error_status {
    padding-bottom: 40px;

    .txt {
        // margin: 0 auto;
        font-size: 20px;
        line-height: 36px;
        text-align: center;
    }

    :global(.go_to_home) {
        margin: 0 4px;
        color: #3A58A9;
        cursor: pointer;
        text-decoration: none;


    }

    :global(.go_to_home:hover) {
        text-decoration: underline;
    }

    .icon {
        width: 100%;
        max-width: 400px;
        margin: 30px auto 0;
    }
}

.order_banner {
    width: 100%;
    min-height: 15.6vw;

    .img {
        width: 100%;
    }

}
</style>
