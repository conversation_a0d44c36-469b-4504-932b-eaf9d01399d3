

// 登录态管理
import { useAuthStore } from '~/stores/auth'
interface useFetchConfig
    extends Omit<Parameters<typeof useAsyncData>[1], "default"> {
    key: string;  // 用于缓存，避免重复请求
    method?: "GET" | "POST" | "PUT" | "DELETE";
    data?: any;
    headers?: Record<string, string>;
    body?: any;
    params?: any;
    query?: any;
    lazy?: boolean;
    timeout?:number;
    interceptors?: {
        request?: (config: any) => any;
        response?: (response: any) => any;
        error?: (error: any) => any;
    };
}


/**
 * 使用自定义配置进行数据请求的 Hook 函数。
 * 此函数允许用户更灵活地控制HTTP请求，包括设置请求方法、附加数据、自定义头信息以及请求和响应拦截器。
 *
 * @example
 * ```typescript
 * interface User {
 *   id: number;
 *   name: string;
 * }
 *
 *   const { data, pending, error, refresh } = useFetchCustom(`/api/users/${userId}`, {
 *     method: "GET",
 *     interceptors: {
 *       request: (config) => {
 *         // 在发送请求前添加鉴权Token
 *         config.headers.Authorization = `Bearer ${getToken()}`;
 *         return config;
 *       },
 *       response: (response) => {
 *         // 处理响应，例如解析数据或错误码
 *         return response.data;
 *       },
 *       error: (error) => {
 *         // 统一处理请求错误
 *         console.error("Request failed:", error);
 *         throw error;
 *       },
 *     },
 *   });
 *
 * ```
 *
 * @param url 请求的 URL 地址。
 * @param config 请求的配置对象，包括请求方法、数据、头信息等。
 * @returns 返回一个包含请求状态和数据的对象，如 `{ data, error, isLoading }`。
 */
export function useFetchCustom(
    url: string,
    config: useFetchConfig,
) {
    const { method = "GET", data, headers, interceptors } = config;
    // 根据请求方法处理请求数据
    if (data) {
        if (method.toUpperCase() == "GET") {
            config.query = data;
        } else {
            config.body = JSON.stringify(data);
            config.params = data;
        }
    }
    // 调用 useFetch 时的额外配置处理，包括拦截器的定义
    return useFetch(url, {
        timeout:10000,
        headers: {
            "Content-Type": "application/json",
            "Authorization": useAuthStore().auth_token? `Bearer ${useAuthStore().auth_token}` : "",
            "Accept-Language": useAuthStore().appLang || 'en',
            ...headers,
        },
        ...config,
        // 配置请求拦截器
        onRequest({ request, options }) {
            if (config.interceptors?.request) {
                options = config.interceptors.request(options);
            }
        },
        onRequestError({ request, options, error }) {
            // 配置请求错误拦截器
            if (config.interceptors?.error) {
                error = config.interceptors.error(error);
            }
        },
        onResponse({ request, response, options }) {
            // 配置响应拦截器
            if (config.interceptors?.response) {
                response = config.interceptors.response(response);
            }
        },
        onResponseError({ request, response, options }) {
            // 配置响应错误拦截器
            if (config.interceptors?.error) {
                response = config.interceptors.error(response);
            }
        },
    });
}