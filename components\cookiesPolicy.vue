<template>
    <div class="cookies_wrap" v-if="cookiesPolicyShow" :style="{ bottom: `${cookiesPolicyBottom}px` }">
        <div class="cookies_content">
            <div class="cookies_policy_txt">{{ $t('common.policy.cookies') }}</div>
            <div class="cookies_policy_btn_wrap">
                <div class="cookies_policy_btn" @click="setCookiesAgree('yes')" data-track-id="101">{{
                    $t('cookies.policy.agree.all') }}</div>
                <div class="cookies_policy_btn" @click="setCookiesAgree('no')" data-track-id="102">{{
                    $t('cookies.policy.disagree.all') }}
                </div>
                <div class="cookies_policy_btn_txt" @click="showCookiesPop" data-track-id="103">{{
                    $t('cookies.policy.manage') }}</div>
            </div>
        </div>
    </div>

    <!-- 提交订单状态 -->
    <a-modal v-model:visible="cookiesPolicyPopShow" class="cookies_policy_pop_wrap" :footer="null"
        :destroyOnClose="true" :keyboard="true" @cancel="showCookiesFixed(true)">
        <div class="cookies_policy_pop">
            <div class="cp_logo"><img src="~/assets/images/common/play_best_blue.png" alt="PLAY BEST" /></div>
            <h3>{{ $t('cookies.policy.center') }}</h3>
            <div class="cp_line">
                <div class="left">{{ $t('cookies.policy.base.title') }}</div>
                <div class="right"><span class="blue">{{ $t('cookies.policy.base.status') }}</span></div>
            </div>
            <div class="cp_txt">{{ $t('cookies.policy.base.txt') }}</div>
            <div class="cp_line">
                <div class="left">{{ $t('cookies.policy.analysis.title') }}</div>
                <div class="right"><a-switch v-model:checked="CPAnalysisCheck" /></div>
            </div>
            <div class="cp_txt">{{ $t("cookies.policy.analysis.txt") }}</div>
            <div class="cp_line">
                <div class="left">{{ $t('cookies.policy.function.title') }}</div>
                <div class="right"><a-switch v-model:checked="CPFunctionCheck" /></div>
            </div>
            <div class="cp_txt">{{ $t("cookies.policy.function.txt") }}</div>
            <div class="c_btn_blue c_btn_a" @click="confirmCookies">{{ $t('cookies.policy.confirm.my') }}</div>
        </div>
    </a-modal>

</template>
<script lang="ts" setup>
const cookiesPolicyBottom = useState("cookiesPolicyBottom", () => 0);
const $cookiesAgree = useCookie('cookies_agree', { maxAge: 86400 * 365, path: '/' , domain: process.env.VITE_COOKIE_DOMAIN});
const CPAnalysisCookies = useCookie('pb_cookies_analysis', { maxAge: 86400 * 365, path: '/' ,domain: process.env.VITE_COOKIE_DOMAIN });
const CPFunctionCookies = useCookie('pb_cookies_function', { maxAge: 86400 * 365, path: '/' , domain: process.env.VITE_COOKIE_DOMAIN});
//是否显示Cookie 协议弹层
const cookiesPolicyShow = ref<boolean>(false);
// pop
const cookiesPolicyPopShow = ref(false);

// 分析Cookie
const CPAnalysisCheck = ref(true);
// 功能cookie
const CPFunctionCheck = ref(true);
// 确认按钮
const confirmCookies = () => {
    // 埋点: cookie弹层点击确认我的选择
    YKTrack.track('click', {
        params: {
            id: '104',
            value1: CPAnalysisCheck.value ? '1' : '0',  // cookie分析开关
            value2: CPFunctionCheck.value ? '1' : '0',  // cookie功能开关
        }
    })
    $cookiesAgree.value = "yes";
    CPAnalysisCookies.value = CPAnalysisCheck.value ? "yes" : "no";
    CPFunctionCookies.value = CPFunctionCheck.value ? "yes" : "no";
    cookiesPolicyShow.value = false;
    cookiesPolicyPopShow.value = false;
};

const showCookiesFixed = (val: boolean) => {
    cookiesPolicyShow.value = val;
};

if (!$cookiesAgree.value) {
    showCookiesFixed(true);
}
// 设置cookies
const setCookiesAgree = (val: string) => {
    $cookiesAgree.value = "yes";
    cookiesPolicyShow.value = false;
    CPAnalysisCookies.value = val;
    CPFunctionCookies.value = val;
};

const showCookiesPop = () => {
    cookiesPolicyPopShow.value = true;
    cookiesPolicyShow.value = false;
};

</script>
<style lang="scss">
.cookies_policy_pop {
    .ant-switch-checked {
        background: var(--base_color);
    }
}

.cookies_policy_pop_wrap {
    max-width: 530px;
    width: 90% !important;

    .ant-modal-body {
        padding: 30px;
    }
}


@media screen and (max-width: 640px) {
    .cookies_policy_pop_wrap {

        .ant-modal-body {
            padding: 20px;
        }
    }

}
</style>
<style lang="scss" scoped>
.c_btn_blue {
    margin-top: 60px;
}

.cookies_policy_pop {
    color: #111;

    .cp_logo {
        width: 180px;
        margin-bottom: 14px;

        img {
            width: 100%;
        }
    }

    h3 {
        font-size: 24px;
        line-height: 40px;
    }

    .cp_txt {
        margin: 10px auto 30px;
        font-size: 14px;
        color: #333;
    }
}

.cp_line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 30px;

    .left {
        color: #111;
        font-size: 18px;
    }

    .right {
        .blue {
            font-size: 14px;
            color: var(--base_color);
        }
    }
}

.cookies_wrap {
    background: rgba(17, 17, 17, 0.70);
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 998;
    width: 100%;

    .cookies_content {
        max-width: 1400px;
        margin: 0 auto;
        padding: 10px 30px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .cookies_policy_txt {
            flex: 1;
            color: #FFF;
            font-size: 14px;
            line-height: 24px;
        }

        .cookies_policy_btn_wrap {
            display: flex;
            flex-direction: column;
            margin-left: 40px;
        }

        .cookies_policy_btn {
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.40);
            background: #1A1A1A;
            line-height: 32px;
            min-width: 160px;
            color: #fff;
            text-align: center;
            padding: 0 20px;
            margin-bottom: 6px;
            cursor: pointer;
        }

        .cookies_policy_btn_txt {
            text-decoration: underline;
            text-align: center;
            color: #FFF;
            cursor: pointer;

            &:hover {
                color: #fff;
            }
        }
    }
}

@media screen and (max-width: 980px) {
    .cookies_wrap {
        .cookies_content {

            .cookies_policy_txt {
                font-size: 12px;
            }

            .cookies_policy_btn {
                line-height: 40px;
                min-width: 140px;
                margin-left: 30px;
                padding: 0 20px;
            }

        }
    }
}

@media screen and (max-width: 640px) {

    .cookies_wrap {
        .cookies_content {
            flex-direction: column;
            padding: 20px 20px 40px;

            .cookies_policy_btn {
                min-width: 200px;
                margin: 6px 0;
            }

            .cookies_policy_btn_wrap {
                margin: 0 auto;
            }

            .cookies_policy_btn,
            .cookies_policy_btn_txt {
                font-size: 12px;
            }
        }
    }

    .cookies_policy_pop {
        h3 {
            font-size: 20px;
        }

        .cp_txt {
            font-size: 12px;
        }
    }

    .cp_line {
        line-height: 24px;

        .left {
            font-size: 14px;
        }

        .right {
            .blue {
                font-size: 12px;
            }
        }
    }
}
</style>