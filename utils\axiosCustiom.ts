import axios from "axios";

// 登录态管理
import { useAuthStore } from "~/stores/auth";

export function $axios(configJson: any) {
    const authStore = useAuthStore();
    const auth_token = useCookie("auth_token", { path: "/", domain: process.env.VITE_COOKIE_DOMAIN });
    console.log("auth_token============:", auth_token.value ,"aaa", process.env.VITE_COOKIE_DOMAIN);
    const Authorization = authStore.auth_token ? `Bearer ${authStore.auth_token}`: (auth_token.value? `Bearer ${auth_token.value}`: "");
    console.log("Authorization============:", Authorization);
    // 创建 Axios 实例
    const api = axios.create({
        timeout: 10000,
        headers: {
            "content-type": "application/json;charset=UTF-8",
            Authorization,
            "Accept-Language": authStore.appLang || "en",
            // "Authorization": app && app.globalData && app.globalData.auth_token || ""
        },
    });

    // 请求拦截器
    api.interceptors.request.use(
        (config:any) => {
            console.log("config",config)
            // console.log("configJson",configJson)
            // 例如: 添加 auth_token 到请求头
            // config.headers = {
            //     ...config.headers,
            //     ...configJson.Headers,  // 请求拦截器
            // };
            return config;
        },
        (error) => {
            return Promise.reject(error);
        }
    );

    // 响应拦截器
    api.interceptors.response.use(
        (response) => {
            return response.data; // 返回 data 属性，方便使用
        },
        (error) => {
            // 例如: 处理错误，例如 401 未授权
            // if (error.response?.status === 401) {
            //     // 跳转到登录页面
            //     navigateTo("/login");
            // }
            const errorContent =
                error == null
                    ? "404 request failed！"
                    : error!.toString();
            message.error({
                content: errorContent,
                class: "c_message_big",
                style: {
                    marginTop: "20vh",
                },
                duration: 3,
            });
            console.error(errorContent);
            return Promise.reject(error);
        }
    );
    return api(configJson);
}
