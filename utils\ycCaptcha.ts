import { YC_CAPTCHA_APP_KEY } from "~/constants/common";
import { useI18n } from "vue-i18n";

declare global {
    interface Window {
        sliderVerify: any;
    }
}

// 创建一个组合式函数
export const useYcCaptcha = () => {
    const { locale } = useI18n();
    // 运行验证码
    const runYcCaptcha = (callback: Function|null=null,args?:any[]) => {
        window.sliderVerify(
            "pop",
            (res: any) => {
                // alert(JSON.stringify(res));
                // console.log(res);
                // 执行回调
                if (callback) {
                    if(typeof args === 'object' && args.length && args.length > 0){
                        console.log(args);
                        callback(...args,res.token,res.captchaVerification);
                    }else{
                        callback(res.token,res.captchaVerification);
                    }
                }
            },
            {
                appKey: YC_CAPTCHA_APP_KEY,
                locale: locale.value === "zh-Hans" ? "zh-CN" : "en-US",
            }
        );
    }
    // 初始化验证码
    const initYcCaptcha = (callback: Function|null=null,args?:any[]) => {
        if (typeof window.sliderVerify === "function") {
            runYcCaptcha(callback,args);
        } else {
            const script = document.createElement("script");
            script.src = "/js/yc_captcha.js";
            script.id = "captchaScript";
            script.onload = () => {
                runYcCaptcha(callback,args);
            };
            document.head.appendChild(script);
        }
    };

    return {
        initYcCaptcha
    };
};
