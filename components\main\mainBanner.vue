<template>
    <div class="banner-wrapper" :style="bannerStyle">
        <a-carousel ref="swiperRef" :autoplay="true" :autoplay-speed="5000" :pauseOnHover="false"
            :before-change="afterBannerChange" :fade="true">
            <template #customPaging="item">
                <div :key="item.i">
                    <div class="thumb" @click="afterBannerChange(activeCarouselIndex, item.i)">
                        <img :src="props.data[item.i].thumb">
                    </div>
                    <div :class="{ progress: true, active: activeCarouselIndex === item.i }" :key="item.i"></div>
                </div>
            </template>
            <div class="banner-box" v-for="(banner, index) of props.data" :key="index" @touchstart="onTouchStart"
                @touchend="onTouchEnd" @touchcancel="onTouchEnd">
                <img :src="banner.image_pc" class="pc-show" />
                <img :src="banner.image_mb" class="mob-show mob-banner-img" />
                <div class="title_box">
                    <h1 class="title">{{ banner.title }}</h1>
                    <p class="desc">{{ banner.desc }}</p>
                    <a :href="banner.link" target='_blank'>
                        <button class="detail_btn" :href="banner.link" v-if="banner.link">{{ $t('pages.main.banner.btn')
                            }}</button>
                    </a>
                </div>
            </div>
        </a-carousel>
    </div>
</template>

<script setup lang="ts">
import type { StyleValue } from 'vue';


const bannerStyle = ref<StyleValue>();

const swiperRef = ref<any>(null);

const activeCarouselIndex = ref(999);

const props = defineProps(['data']);

const emits = defineEmits(['onChange']);

onMounted(() => {
    nextTick(() => {
        activeCarouselIndex.value = 0;
    })
});

function onTouchStart() {

    if (!isMobile()) {
        return;
    }
    swiperRef.value?.autoplay?.(false);
}


function onTouchEnd() {
    swiperRef.value?.autoplay?.(true);
}

onUnmounted(() => {
    swiperRef.value = null
})


function afterBannerChange(from: number, current: number) {
    activeCarouselIndex.value = current;
}


function checkDetail(detailUrl: string) {
    window.location.href = detailUrl;
}


function checkScreenDetails() {
    const appHeight = window.innerHeight;
    console.log("🚀 ~ checkScreenDetails ~ appHeight:", appHeight)

    bannerStyle.value = {
        '--screen-height': `${appHeight}px`
    }
}

onMounted(() => {
    checkScreenDetails();
    // window.addEventListener('resize', checkScreenDetails);
})

onUnmounted(() => {
    // window.removeEventListener('resize', checkScreenDetails);
});



</script>


<style lang="scss">
// 移动端尺寸 pc-show的内容隐藏
@media screen and (max-width:820px) {

    .pc-show {
        display: none !important;
    }

}

// pc端尺寸 mob-show的内容隐藏
@media screen and (min-width:821px) {
    .mob-show {
        display: none !important;
    }
}
</style>


<style scoped lang="scss">
$offsetX: 2.6vw; //calc(45px/1744px*100vw);
$spaceGap: 20px;
$gap: calc($spaceGap + $offsetX);

.ant-carousel :deep(.slick-slide) {
    position: relative;
}

.ant-carousel :deep(.slick-dots) {

    position: absolute;
    height: auto;
    left: initial;
    right: max($gap, calc((100vw - 1744px)/2 + $gap));
    bottom: min(74px, 7.8vw); // calc((124px/1587px)*100vw));
    margin-right: 0;
}

.ant-carousel :deep(.slick-slide img) {
    border: none;
    display: block;
    margin: auto;
    max-width: 100%;
}

.ant-carousel :deep(.slick-arrow) {
    display: none !important;
}

.ant-carousel :deep(.slick-dots) {
    justify-content: flex-end;
}

.ant-carousel :deep(.slick-dots li) {
    width: auto;
    height: auto;
    margin: 0;
    cursor: pointer;
    margin-left: 12px;
    text-indent: 0;
}

.ant-carousel :deep(.thumb) {
    position: relative;
    max-width: 120px;
    height: 0;
    width: 6vw;
    padding-top: 100%;
    border-radius: 9px;
    overflow: hidden;
    opacity: 0.4;
    transition: opacity .5s;

    img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    &:hover {
        opacity: 1;
    }
}

.ant-carousel :deep(.slick-active .thumb) {
    opacity: 1;
}

.title_box {
    position: absolute;
    text-shadow: 2px 2px 10px #333;
    z-index: 8;
    left: max($gap, calc((100vw - 1744px)/2 + $gap));
    bottom: min(74px, 8.1vw); // calc((129px/1587px)*100vw));

    .title {
        font-size: min(70px, 4.4vw); // calc((70px/1587px)*100vw));
        font-weight: 900;
        line-height: min(95px, 5.99vw); //calc((95px/1587px)*100vw));
        text-align: left;
        color: #FFF;
        margin-bottom: min(14px, 0.88vw); // calc((14px/1587px)*100vw));
        padding: 0 4px 4px 0;
        max-width: 90vw;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .desc {
        font-size: min(30px, 1.89vw); // calc((30px/1587px)*100vw));
        font-weight: 400;
        line-height: min(42px, 2.65vw); // calc((42px/1587px)*100vw));
        text-align: left;
        padding: 0 4px 4px 0;
        color: #FFF;
        max-width: 600px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .detail_btn {
        cursor: pointer;
        width: min(272px, 17.1vw); // calc((272px/1587px)*100vw));
        height: min(82px, 5.2vw); // calc((82px/1587px)*100vw));
        margin-top: min(37px, 2.3vw); // calc((37px/1587px)*100vw));
        border-radius: min(42px, 2.65vw); //  calc((42px/1587px)*100vw));
        background-color: rgba(17, 17, 17, .8);
        border: none;
        font-size: min(26px, 1.63vw); //  calc((26px/1587px)*100vw));
        font-weight: 400;
        // line-height: min(36px, 2.3vw); // calc((36px/1587px)*100vw));
        color: #FFF;
        text-align: center;
        outline: none;

        &:hover {
            color: #fff;
            background-color: rgba(17, 17, 17, .6);
        }
    }
}

.banner-box {
    width: 100%;
    max-height: 100vh;
}

.progress {
    position: relative;
    width: 100%;
    height: min(10px, 0.63vw); // calc((10px/1587px)*100vw));
    border-radius: min(16px, 1vw); // calc((16px/1587px)*100vw));
    margin-top: min(5px, 0.3vw); // calc((5px/1587px)*100vw));
    overflow: hidden;
    opacity: 0;
    background-color: #C2C2CD;

    &::after {
        content: ' ';
        display: block;
        width: 100%;
        height: 100%;
        transform: scaleX(0);
        background-color: #FFFFFF;
        transform-origin: 0 0;
        // transition: transform 3s linear allow-discrete;
    }

    &.active {
        opacity: 1;
        transition: none;

        &::after {
            transform: scaleX(1);
            animation: progressGo 5s linear forwards;
        }
    }
}

@keyframes progressGo {
    from {
        transform: scaleX(0);
    }

    to {
        transform: scaleX(1);
    }
}

@media screen and (max-width:820px) {

    .title_box {
        padding-right: 20px;

        .title {
            font-size: 32px;
            line-height: 1.2;
        }

        .desc {
            font-size: 18px;
            white-space: break-spaces;
            line-height: 1.5;
        }

        .detail_btn {
            width: auto;
            height: auto;
            // line-height: 1;
            border-radius: 42px;
            font-size: 4.2vw;
            padding: 13px 28px;
            justify-content: center;
            display: flex;
            align-items: center;
        }
    }

    .banner-box {
        height: var(--screen-height);
        font-size: 0;
    }

    .mob-banner-img {
        height: 100%;
        width: 100%;
        object-fit: cover;
    }

    .ant-carousel {

        :deep(.slick-slide) {
            // max-height: 96vh;
        }

        .title_box {
            left: 20px;
        }

        :deep(.slick-dots) {
            left: 20px;
            right: auto;
            bottom: 0;
            margin-left: 0;
            justify-content: flex-start;

            li {
                margin-left: 0;
                margin-right: 12px;
            }
        }

        :deep(.progress) {
            opacity: 1;
            transition: none;

            &::after {
                transition: none;
                animation: none;
            }

        }

        :deep(.thumb) {
            opacity: 0.6;

            img {
                display: none;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

            &:hover {
                opacity: 1;
            }

        }
    }

}
</style>