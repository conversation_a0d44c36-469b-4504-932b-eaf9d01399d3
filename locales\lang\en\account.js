export default {
    "account.login.accountLogin": "Account Login",
    "account.login.emailLogin": "Log in with <PERSON><PERSON>",
    "account.login.pleaseEnterEmail": "Email Address",
    "account.login.pleaseEnterPassword": "Password",
    "account.login.forgotPassword": "Forgot password",
    "account.login.registerNow": "Sign up",
    "account.login.cancelLoginAuthorization": "Revoke login authorization",
    "account.login.confirm": "Confirm",
    "account.login.return": "Return",
    "account.login.onlyOfficialGameEmail": "Currently, only official game email accounts are supported for login on this website.",
    "account.login.notSupportEmailVerificationCode": "Login using email verification code is not supported at this time.",
    "account.login.useEmailVerificationCodeOrThirdParty": "To use email verification code or other third-party accounts (such as Google, Facebook, Apple) for login, please bind your email account within the game first.",
    "account.login.pleaseEnterVerificationCode": "Please enter the verification code",
    "account.login.get": "Get",
    "account.login.sent": "Sent",
    "account.register.accountRegistration": "Sign up",
    "account.register.enterEmail": "Email Address",
    "account.register.passwordRequirements": "Password must be 8-20 characters, and must include a combination of letters and numbers.",
    "account.register.confirmPassword": "Please re-enter your password.",
    "account.register.register": "Register",
    "account.register.alreadyHaveAccount": "I've got an account. Log in",
    "account.register.agreeTerms": "I have read and agreed to the Game Service Agreement and Privacy Policy.",
    "account.register.ageConfirmation": "I am over 14 years old.",
    "account.register.termsAndAgeConfirmation": "To protect your rights, please confirm that you have read and agreed to the Game Service Agreement and Privacy Policy, and that you are at least 14 years old.",
    "account.register.termsConfirmation": "To protect your rights and interests, please ensure that you have read and agreed to the Game Service Agreement and Privacy Policy.",
    "account.common.confirm": "Confirm",
    "account.common.return": "Return",
    "account.forgotPassword.title": "Forgot password",
    "account.forgotPassword.enterEmail": "Email Address",
    "account.forgotPassword.nextStep": "Next step",
    "account.forgotPassword.noSecurityVerification": "The account has not been security verified",
    "account.forgotPassword.contactSupport": "To retrieve your password, you need to verify your email address or mobile phone number. If you have not verified,",
    "account.forgotPassword.contactSupport2": " please contact the Customer Service.",
    "account.forgotPassword.cannotModify": "To reset your password, you need to verify your email or mobile number. If you haven't verified, you cannot modify your password for now.",
    "account.securityVerification.title": "Security Verification",
    "account.securityVerification.methods": "You can verify using the following methods",
    "account.securityVerification.phoneVerification": "Mobile Number Verification",
    "account.securityVerification.emailVerification": "Email verification",
    "account.securityVerification.phoneInstructions2": "Click 'Send' to send a verification code at your mobile number { XX }",
    "account.securityVerification.emailInstructions2": "Click 'Send' to send to verification code to your email { XX }.",
    "account.securityVerification.enterVerificationCode": "Enter Verification Code",
    "account.resetPassword.setNewPassword": "Set new password",
    "account.resetPassword.success": "Password changed successfully. Please log in again.",
    "account.profile.accountInformation": "Account Information",
    "account.profile.emailVerificationAvailable": "Email verification is now available.",
    "account.profile.emailVerificationImportance": "We take your account security very seriously. Please verify your email address to protect your account assets.",
    "account.profile.skipVerification": "Skip for now",
    "account.profile.startEmailVerification": "Start email verification",
    "account.profile.nickname": "Nickname",
    "account.profile.accountName": "Account Name",
    "account.profile.accountID": "Account ID",
    "account.profile.verifyEmail": "Verification Email",
    "account.profile.phoneNumber": "Mobile Number",
    "account.profile.notBound": "Unbound",
    "account.profile.notVerified": "Unverified",
    "account.profile.notSet": "Not set",
    "account.profile.verify": "Verify",
    "account.profile.bind": "Bind",
    "account.profile.bound": "Bound",
    "account.profile.changeBind": "Change Binding",
    "account.profile.edit": "Edit",
    "account.securityCheck.title": "Security Check",
    "account.securityCheck.bindAccountName": "Bound Account Name",
    "account.securityCheck.accountNameBound": "Account Name Bound",
    "account.securityCheck.verifyEmail": "Verify Email",
    "account.securityCheck.emailVerified": "Email Verified",
    "account.securityCheck.fillPersonalInfo": "Provide your personal info",
    "account.securityCheck.personalInfoFilled": "Personal info submitted",
    "account.securityCheck.bindPhoneNumber": "Bind Mobile Number",
    "account.securityCheck.phoneNumberBound": "Mobile Number Bound",
    "account.securityCheck.completionProgress": "Completion Progress",
    "account.personalInfo.title": "Personal Information",
    "account.personalInfo.name": "Name",
    "account.personalInfo.birthday": "Birthday",
    "account.personalInfo.gender": "Gender",
    "account.personalInfo.notSet": "Not set",
    "account.personalInfo.edit": "Edit",
    "account.personalInfo.modify": "Modify",
    "account.editNickname.title": "Edit your nickname",
    "account.editNickname.changeLimit": "Once you set your nickname, you cannot change it again for 180 days.",
    "account.editNickname.currentNickname": "Current Nickname",
    "account.editNickname.notSet": "Not set",
    "account.editNickname.enterNewNickname": "Please enter new nickname",
    "account.editNickname.nicknameLength": "Nickname must be at least 3 characters long",
    "account.editNickname.privacyWarning": "To protect your privacy, please do not use your real name, address, phone number, or other personal information.",
    "account.common.confirm": "Confirm",
    "account.common.cancel": "Cancel",
    "account.setEmailAccount.title": "Set email account",
    "account.setEmailAccount.description": "The verified email address will be used as your account name; after successful setup, you can log in using your email.",
    "account.setEmailAccount.agreement": "By clicking \"Confirm\", you agree to allow Playbest to save this email address and use it for sending notifications and security verification",
    "account.setEmailAccount.enterEmail": "Enter Email",
    "account.setEmailAccount.enterVerificationCode": "Enter Verification Code",
    "account.common.get": "Get",
    "account.common.sent": "Sent",
    "account.setEmailAccount.passwordRequirements": "Password must be 8-20 characters, and must include a combination of letters and numbers.",
    "account.setEmailAccount.confirmPassword": "Please re-enter your password.",
    "account.common.confirm": "Confirm",
    "account.verifyEmail.title": "Verify Email",
    "account.verifyEmail.agreement": "By clicking \"Verify\", you agree to allow Playbest to save this email address and use it for sending notifications and security verification",
    "account.verifyEmail.enterEmail": "Enter Email",
    "account.verifyEmail.enterVerificationCode": "Enter Verification Code",
    "account.common.get": "Get",
    "account.common.sent": "Sent",
    "account.verifyEmail.verify": "Verify",
    "account.bindPhoneNumber.title": "Bind Mobile Number",
    "account.bindPhoneNumber.description": "To ensure account security, please bind your mobile number",
    "account.bindPhoneNumber.agreement": "By clicking \"Bind\", you agree to allow Playbest to save this mobile number and use it for sending notifications and security verification.",
    "account.bindPhoneNumber.select": "Select",
    "account.bindPhoneNumber.enterPhoneNumber": "Enter Mobile Number",
    "account.bindPhoneNumber.enterVerificationCode": "Enter Verification Code",
    "account.common.get": "Get",
    "account.common.sent": "Sent",
    "account.bindPhoneNumber.bind": "Bind",
    "account.changeEmail.title": "Change email",
    "account.changeEmail.currentEmail": "Current email address",
    "account.changeEmail.description": "Accept notification and verification mails after changing to the new email address.",
    "account.securityVerification.title": "Security Verification",
    "account.securityVerification.methods": "You can verify using the following methods",
    "account.securityVerification.phoneVerification": "Mobile Number Verification",
    "account.securityVerification.emailVerification": "Email verification",
    "account.securityVerification.emailInstructions": "Click \"Send\" to send to verification code to your email.",
    "account.securityVerification.enterVerificationCode": "Enter Verification Code",
    "account.common.get": "Get",
    "account.common.sent": "Sent",
    "account.common.nextStep": "Next step",
    "account.changeEmail.agreement": "By clicking \"Verify\", you agree to allow Playbest to save this email address and use it for sending notifications and security verification",
    "account.changeEmail.enterEmail": "Enter Email",
    "account.changePhoneNumber.title": "Change mobilephone number",
    "account.changePhoneNumber.currentPhoneNumber": "Current mobilephone number",
    "account.changePhoneNumber.description": "After changing your mobile number, all notifications and verification messages will be sent to the new number.",
    "account.securityVerification.methods": "You can verify using the following methods",
    "account.securityVerification.phoneVerification": "Mobile Number Verification",
    "account.securityVerification.phoneInstructions": "Click \"Send\" to send a verification code at your mobile number",
    "account.securityVerification.enterVerificationCode": "Enter Verification Code",
    "account.common.get": "Get",
    "account.common.sent": "Sent",
    "account.common.nextStep": "Next step",
    "account.changePhoneNumber.agreement": "By clicking \"Bind\", you agree to allow Playbest to save this mobile number and use it for sending notifications and security verification.",
    "account.resetPassword.setNewPassword": "Set new password",
    "account.resetPassword.enterOriginalPassword": "Please enter your original password",
    "account.resetPassword.verifyOriginalPassword": "Verify original password",
    "account.resetPassword.setNewPassword": "Please set a new password.",
    "account.personalInfo.enterFirstName": "Please enter your first name",
    "account.personalInfo.enterLastName": "Please enter your last name",
    "account.personalInfo.selectGender": "Please select your gender",
    "account.personalInfo.gender": "Gender",
    "account.personalInfo.selectBirthday": "Please select your birthday",
    "account.personalInfo.birthdayWarning": "Birthday information can only be modified once after submission, please fill it in carefully.",
    "account.personalInfo.male": "Male",
    "account.personalInfo.female": "Female",
    "account.personalInfo.secret": "Not specified",
    "account.personalInfo.other": "Other",
    "account.common.save": "Save",
    "account.avatar.selectAvatar": "Choose an avatar",
    "account.avatar.pleaseSelectAvatar": "Please choose an avatar",
    "account.security.title": "Security Management",
    "account.security.passwordManagement": "Password Management",
    "account.security.passwordUpdateRecommendation": "We recommend that you change your password regularly to protect your PLAYBEST account from unauthorized access.",
    "account.security.lastPasswordUpdateTime": "Last password update",
    "account.security.changePassword": "Change password",
    "account.common.modify": "Modify",
    "account.common.set": "Set",
    "account.orders.myOrders": "My Orders",
    "account.orders.all": "All",
    "account.orders.pendingPayment": "Pending Payment",
    "account.orders.pendingShipment": "Awaiting Shipment",
    "account.orders.completed": "Completed",
    "account.orders.closed": "Closed",
    "account.orders.allGames": "All Games",
    "account.orders.orderNumber": "Order Number",
    "account.orders.orderTime": "Order Time",
    "account.orders.cancelOrder": "Cancel Order",   
    "account.orders.paymentMethod": "Payment Method",
    "account.orders.noOrders": "You have no orders yet",
    // Registration related
    "account.register.emailEmpty": "Please enter your email address",
    "account.register.passwordEmpty": "Please enter password",
    "account.register.confirmPasswordEmpty": "Password confirmation cannot be empty.",
    "account.register.emailInvalid": "Please enter a valid email address",
    "account.register.emailAlreadyRegistered": "Email has already been registered.",
    "account.register.passwordLengthInvalid": "Password must be between 8-20 characters",
    "account.register.passwordTooSimple": "Password must contain a combination of at least two of the following: letters, numbers, and special characters",
    "account.register.passwordInvalidChars": "Password contains unsupported special characters",
    "account.register.passwordMismatch": "The two passwords entered do not match, please re-enter",

    // Login related
    "account.login.emailEmpty": "Please enter your email address",
    "account.login.passwordEmpty": "Please enter password",
    "account.login.emailInvalid": "Please enter a valid email",
    "account.login.emailNotRegistered": "Player not found.",
    "account.login.credentialsInvalid": "Incorrect email or password, please try again.",
    "account.login.tooManyAttempts": "You've failed too many times. Please try again later.",

    // Verification code related
    "account.verification.emailEmpty": "Please enter your email address",
    "account.verification.passwordEmpty": "Please enter password",
    "account.verification.emailInvalid": "Please enter a valid email",
    "account.verification.codeSent": "Verification code sent",
    "account.verification.noSecurityEmail": "This account has not been verified with a security email.",
    "account.verification.emailNotFound": "Player not found.",
    "account.verification.codeInvalid": "Please enter the correct verification code.",
    "account.verification.codeReused": "The verification code has expired. Please try again",

    // Email binding related
    "account.email.alreadyBound": "Email has already been registered.",
    "account.email.bindSuccess": "Account name binding successful.",
    "account.email.accountBindSuccess": "You have successfully set your account name and verified your email",
    "account.email.empty": "Please enter your email address",
    "account.email.passwordEmpty": "Please enter password",
    "account.email.invalid": "Please enter a valid email",
    "account.email.codeEmpty": "Please re-enter the verification code.",
    "account.email.codeSent": "Verification code sent",
    "account.email.verificationSuccess": "Email verification successful",

    // Common errors
    "account.error.gameLimitExceeded": "The maximum number of accounts linked to this email has been reached.",
    "account.error.tooManyRequests": "Verification code requests are too frequent, please try again later",
    "account.error.dailyLimitExceeded": "You have reached the daily limit for verification code requests",
    "account.error.codeExpired": "The verification code has expired. Please try again",

    // Phone binding related
    "account.phone.empty": "Please enter your mobile number.",
    "account.phone.invalid": "Please enter a valid mobile number",
    "account.phone.bindSuccess": "Mobile Number Bound Successfully",
    "account.phone.changeBindSuccess": "Mobile number binding successful",
    "account.phone.areaCodeEmpty": "Please enter the area code",    
    "account.phone.areaCode": "Area Code",

    // Password related
    "account.password.originalEmpty": "Please enter your original password.",
    "account.password.newEmpty": "Please enter a new password.",
    "account.password.originalIncorrect": "The original password you entered is incorrect. Please try again.",
    "account.password.sameAsCurrent": "The new password is the same as the current password.",
    "account.password.mismatch": "The new passwords do not match. Please re-enter.",
    "account.password.changeSuccess": "Password changed successfully. Please log in again.",

    // Forgot password
    "account.forgotPassword.noSecurityContact": "The account has not been security verified",

    // Nickname related
    "account.nickname.empty": "Please enter your new nickname",
    "account.nickname.tooShort": "Nickname must be at least 3 characters long",
    "account.nickname.tooLong": "Nickname is too long. Please try again.",
    "account.nickname.sameAsCurrent": "The nickname you entered is the same as your current one. Please try again.",
    "account.nickname.containsSensitiveWords": "Nickname contains sensitive words. Please re-enter.",
    "account.nickname.editLimitReached": "Nickname change is unavailable for 180 days. Please wait.",
    "account.nickname.setSuccess": "Nickname set successfully.",

    // Other errors
    "account.error.network": "Your network seems unstable. Please try again later.",
    "account.error.loginExpired": "Your login information has expired. Please log in again.",

    // 注销账号
    ...{
        "account.security.deregister.s10.t1": "Deleting account...",
        "account.security.deregister.s8.t1": "Account deletion rejected",
        "account.security.deregister.s9.t1": "Account deletion under review",
        "account.security.deregister": "Delete Account",
        "account.security.deregister.apply": "Request Account Deletion",
        "account.security.deregister.desc": "Account deletion will be completed within 30 days of request.",
        "account.security.deregister.popup.title": "Delete your Playbest account",
        "pages.security.deregister.8": "Delete your account of {gamename}",
        "account.security.deregister.s1.title": "Attention! You are applying to delete your Playbest Account!",
        "account.security.deregister.s1.p1": "Account:{XXX}",
        "account.security.deregister.s1.p2": "Once your account is deleted, it will be permanently removed and cannot be restored.",
        "account.security.deregister.s1.p3": "All in-game items, virtual currency balances, unused services, personal data, and history will be permanently deleted.",
        "account.security.deregister.s1.f1": "If you confirm to delete your Playbest Account, please follow the steps provided to complete the deletion process.",
        "account.security.deregister.nextStep": "Next",
        "account.security.deregister.s3.btn1": "Next({XXX}s)",
        "account.security.deregister.s3.t1": "Read the Account Deletion Terms and check the box at the bottom of the page",
        "account.security.deregister.s4.t1": "Please read the Account Deletion Notice and check the box to confirm at the bottom of the page.",
        "account.security.deregister.s3.check": "I have thoroughly read the Account Deletion Terms and met the requirements.",
        "account.security.deregister.s4.check": "I have thoroughly read and agree to the Account Deletion Notice.",
        "account.security.deregister.s3.p1": "Dear player, we regret to inform you that your account deletion request cannot be processed at this time. To ensure the safety of your account and assets, please make sure the following conditions are met before your account deletion request can be approved. Our game team also reserves the right to verify the following:",
        "account.security.deregister.s3.p2": "1. The account is in a secure state: The account is being used normally, not under any violation-related actions (such as being banned due to violations of the game's terms and rules), and is not at risk of being stolen or compromised.\n2. Game profits have been settled or properly handled: This includes but is not limited to in-game currency, virtual items, and other game value-added services (collectively referred to as \"game profits\"). Please handle these properly; failure to do so may result in the voluntary forfeiture of these game profits.\n3. There are no pending orders on the account: This includes purchases of in-game currency, virtual items, and other related goods.\n4. There are no disputes associated with the account: This includes complaints, reports, arbitrations, litigations, etc.\n5. The account is free of risks: There have been no abnormal login activities on the account in the past month and no other security risks.\n6. There are no ongoing transactions on the account: This includes any incomplete orders or transactions within the account, including those in the platform's store.\n7. There are no outstanding contracts related to the account: There are no unresolved contractual relationships or disputes arising from the deletion of the account.\n8. Other conditions that need to be met.\n*Friendly reminder: There is a 30-day cooling-off period for account deletion. During the review period, your account will be frozen. If you wish to cancel the account deletion request, please log in to the game to unfreeze your account or contact us during the frozen period. Your account will be permanently deleted after the freezing period.\nAs users can create characters in one or more game regions, if you have created characters in multiple regions, the deletion of your game account will result in the deletion of all regions and characters under this account.",
        "account.security.deregister.s4.p1": "1. Before deleting your Playbest Account, you must ensure that your account meets the following conditions. If any of these conditions are not met, Playbest reserves the right to reject your account deletion request.\na) At the time of the deletion request, your Playbest Account must be in normal use and not have any potential security risks (including but not limited to banned or frozen Playbest accounts, abnormal login behavior, abnormal usage behavior, etc.).\nb) There must be no ownership disputes, illegal use of other people's accounts, sharing of accounts with third parties, or any other situations that may cause disputes over account use rights or damage the legitimate rights and interests of third parties.\n\n2. After you have read and agreed to the Account Deletion Notice and passed the account security verification and personal information verification, your account will officially enter the deletion freezing period (the \"Freezing Period\"), which lasts for 30 days. If you log in to the account or use any Playbest services during the Freezing Period, you can reactivate the account by clicking the \"Activate\" button on the login page (i.e., the account deletion process will immediately terminate, and the account will be restored to normal use). If you do not log in or use any Playbest services during the Freezing Period, the account will be deleted after the period ends.\n\n3. Consequences of Deleting a Playbest Account\na) After your Playbest Account is deleted, you will no longer be able to log in or use any Playbest services with that account, nor will you be able to recover any content or information set in that account, even if you register a Playbest Account again with the same registration information and use Playbest services.\nb) All content, information, data, characters, and records generated by logging in and using Playbest services through that account will be deleted or anonymized (unless otherwise required by laws and regulations). You will not be able to access, transfer, retrieve, continue to use, or recover the aforementioned deleted or anonymized content, information, data, or records, nor do you have the right to request Playbest to recover them.\nc) Any recharged balance, virtual items obtained through recharges, historical match records, various coupons, VIP membership cards, monthly or weekly cards, unexpired items, unfinished tasks, and any other rights generated but not yet consumed in that account, and/or any rights that may be expected to be generated, will be considered forfeited and will not be available for use or recovery.\nd) Once the account deletion is complete, all agreements signed with us related to the registration and use of the account and related Playbest services will be terminated, except for the parts of the agreement that have been agreed to remain effective with you or as otherwise required by laws and regulations.\ne) Deleting a Playbest Account does not exempt or reduce any responsibilities or actions taken before the account deletion.\n\n4. After the Playbest Account deletion is complete, your personal information and data associated with the account will be deleted or anonymized. You understand and agree that, to the maximum extent permitted by applicable laws and regulations, we may not be able to delete your personal data in certain situations. These situations include:\na) To protect the company's business, systems, and users from fraudulent activities;\nb) To address technical issues that damage existing functionalities;\nc) When necessary to exercise the rights of other users;\nd) To comply with law enforcement requests in accordance with legal procedures;\ne) For scientific or historical research purposes;\nf) For internal purposes reasonably related to your relationship with the company or to fulfill legal obligations.",
        "pages.security.deregister.23": null,
        "pages.security.deregister.24": "Next",
        "pages.security.deregister.25": "I don't want to delete.",
        "account.security.deregister.s5.t1": "Reasons for deletion",
        "pages.security.deregister.27": "Account Deletion",
        "account.security.deregister.s5.t3": "Multiple choice, select up to three items",
        "account.security.deregister.s5.p1": "The game is not fun",
        "account.security.deregister.s5.p2": "Insufficient game benefits",
        "account.security.deregister.s5.p3": "Top-up issues",
        "account.security.deregister.s5.p4": "I don't want to play this game anymore",
        "account.security.deregister.s5.p5": "I won't be using this account anymore",
        "account.security.deregister.s5.p6": "Other",
        "account.security.deregister.s5.p7": "Other reasons for deletion (maximum 30 characters)",
        "account.security.deregister.s5.b1": "Submit",
        "account.security.deregister.s6.t1": "Are you sure you want to delete your account?",
        "account.security.deregister.s6.p1": "Dear Player, thank you for your support.",
        "account.security.deregister.s6.p2": "Once the deletion process is complete, your account will be deleted. Please proceed with caution.",
        "account.security.deregister.s6.p3": "Please be aware that after completing the deletion process, all your account data, and any data generated by logging into our products by using this account, will be permanently deleted and cannot be recovered. Please choose carefully and proceed with caution.",
        "account.security.deregister.s6.b1": "Confirm Deletion",
        "account.security.deregister.s6.b2": "Think about it again",
        "account.security.deregister.s7.t1": "Your account deletion request has been successfully submitted",
        "account.security.deregister.s7.t1-1": "Thank you for your support of our game",
        "pages.security.deregister.45": "Account:",
        "account.security.deregister.s7.p2": "Request Time:{XXX}",
        "account.security.deregister.s7.p3": "Except as provided by laws and regulations or the deletion agreement, the deletion review will be completed within 30 working days from the date you submit the application. ",
        "account.security.deregister.s7.d2": "During the review period, your account will be frozen. If you wish to cancel the deletion request, please log into the game during the account freezing period to unfreeze or contact us. Your account will be completely deleted after the freeze period ends.",
        "account.security.deregister.s7.d3": "Click \"I know\" to log out of your current account.",
        "account.security.deregister.s7.b1": "I know",
        "account.security.deregister.s3.btn2": "Cancel Deletion",
        "pages.security.deregister.52": "I've changed my mind",
        "pages.security.deregister.53": "Deleting account...",
        "account.security.deregister.s5.t2": "Account:{XXX}",
        "pages.security.deregister.55": "Request Time:",
        "pages.security.deregister.56": "Your account deletion request is under review for deletion. ",
        "account.security.deregister.s10.t2": "The deletion process will be completed in 30 days after you applied",
        "pages.security.deregister.58": "Except as provided by laws and regulations or the deletion agreement, the deletion review will be completed within 30 working days from the date you submit the application. ",
        "account.security.deregister.s8.p4": "If you want to cancel the account deletion process, please click \"Cancel Deletion\". If you are certain you want to delete this account, please click \"Change Account\" and log in with a different account.",
        "pages.security.deregister.60": "Cancel Deletion",
        "account.security.deregister.s8.b1": "Switch Account",
        "pages.security.deregister.62": "Your account deletion request has been rejected",
        "pages.security.deregister.63": "Account:",
        "pages.security.deregister.64": "Request Time:",
        "account.security.deregister.s8.t2": "Dear player, after review, the following risks were found in your account, so it does not meet the deletion conditions",
        "pages.security.deregister.67": "If you want to cancel the deletion process, click \"Cancel Deletion\", We will enter the game for you; if you confirm to delete this account, click \"Switch Account\" to log in with another account.",
        "pages.security.deregister.68": "Cancel Deletion",
        "pages.security.deregister.69": "Switch Account",
        "pages.security.deregister.70": "Your account cancellation application is currently under verification",
        "pages.security.deregister.71": "Account:",
        "pages.security.deregister.72": "Request Time:",
        "pages.security.deregister.73": "Account deletion under review",
        "account.security.deregister.s9.p3": "Thank you for your patience. We have received your account deletion request. Due to an abnormal situation detected with your account, we have forwarded your request to the manual review process.",
        "account.security.deregister.s9.p4": "The manual review process may take some time, so please be patient. Thank you for your understanding and support.",
        "account.security.deregister.s9.p5": "If you want to cancel the deletion process, click \"Cancel Deletion\", We will enter the game for you; if you confirm to delete this account, click \"Switch Account\" to log in with another account.",
        "pages.security.deregister.77": "Cancel Deletion",
        "pages.security.deregister.78": "Switch Account",
        "account.security.deregister.msg2": "You have canceled the account deletion.",
        "account.security.deregister.msg1": "Please select at least one reason for deletion.",
        "pages.security.deregister.81": "Only one account can be deleted per day. Please try again tomorrow.",
        "pages.security.deregister.82": "Account deletion in progress. Payment is temporarily unavailable.",
        "pages.security.deregister.83": "Contact customer support if you have any questions.",
        "pages.security.deregister.84": "Deleting account...Contact customer support  or log in to the account center for help.",
        "pages.security.deregister.85": "Account deletion rejected. Contact customer support  or log in to the account center for help.",
        "pages.security.deregister.86": "Account deletion under review. Contact customer support  or log in to the account center for help.",
        "pages.security.deregister.s7.app": "Closing the page will logout the current account."
    },

    // web-view 内嵌账号中心
    "account.inner.lastName": "First Name",
    "account.inner.firstName": "Last Name",
    "account.inner.goToSetting": "Settings",
    "account.inner.nicknameNotSet": "Nickname not set",
    "account.inner.bind": "Bind",
    "account.inner.verify": "Verify",
    "account.inner.changeBinding": "Change Binding",
    "account.inner.bound": "Bound",
    "account.inner.passwordManagement": "Password Management",
    "account.inner.lastUpdate": "Last Update",
    "account.inner.update": "Update",
    "account.inner.switchAccount": "Switch Account",
    "account.inner.deleteAccount": "Delete Account",
    "account.inner.unbind": "Unbind",
    "account.inner.unbindConfirm": "Would you like to unbind your {media_name} account?",
    "account.inner.unbindConfirmDesc": "Unbinding will prevent you from using your {media_name} account {nickname} to log in to this game.",
    "account.inner.hint":"Notice",
    "account.inner.confirmSwitchAccount": "Are you sure you want to switch accounts?",
    "account.inner.step.verify": "Verification",
    "account.inner.step.finished": "Done！",
    "account.inner.step.verifySuccess": "Verification Success",
    "account.inner.step.ok":"OK",
    "account.inner.bindAccount.desc": "Once you've completed the setup, you can log in using your email address and password.",
    "account.inner.unbindAccount.onlyOne": "To ensure a smooth login experience, please keep at least one third-party login method. If you want to unbind, please bind another account first.",
    "account.inner.unbindAccount.unbindSuccess": "Unbind Success",
    "account.inner.setAccountName.title": "Set Account Name",
    "account.inner.setAccountName.success": "Setup Success",
    "account.inner.setAccountName.desc": "Your account name has been successfully set",
    "account.inner.emailVerificationSuccess": "Email verification successful",
    "account.inner.emailVerificationSuccessDesc": "Email verification successful.\nNotifications and verification messages will be sent to this email address.",
    "account.inner.changeEmailSuccess": "Email change verification successful",
    "account.inner.changeEmailSuccessDesc": "Email change verification successful.\nNotifications and verification messages will be sent to the new email address.",
    "account.inner.phoneBindSuccess": "Mobile number binding successful",
    "account.inner.phoneBindSuccessDesc": "Mobile number binding successful.\nNotifications and verification messages will be sent to this mobile number.",
    "account.inner.changePhoneSuccess": "Mobile number change verification successful",
    "account.inner.changePhoneSuccessDesc": "Mobile number change verification successful.\nNotifications and verification messages will be sent to the new mobile number.",
    "account.inner.changePasswordSuccess": "Password Updated",
    "account.inner.changePasswordSuccessDesc": "Please log in again",
    "account.inner.bindSuggestion": "Dear guest, to ensure the security of your account and ease of use, we recommend that you bind your email address or social media account as soon as possible. Unbound accounts may have limited functionality.",
    "account.inner.bindSuccess": "Binding Successful",
    "account.profile.editProfile": "Edit Profile",
}
