<template>
    <div class="title-box-wrapper" :class="{ [`title-box-wrapper-align-${props.align}`]: props.align }">
        <p class="title-box-name" v-if="props.name">{{ props.name }}</p>
        <p class="title-box-title" v-if="props.title">{{ props.title }}</p>
        <p class="title-box-desc" v-if="props.desc">{{ props.desc }}</p>
        <div class="title-box-divider" v-if="props.showDivider">
            <svg xmlns="http://www.w3.org/2000/svg" width="82" height="4" viewBox="0 0 82 4" fill="none">
                <path
                    d="M0 2C0 0.895431 0.895431 0 2 0H54C55.1046 0 56 0.895431 56 2C56 3.10457 55.1046 4 54 4H2C0.89543 4 0 3.10457 0 2Z"
                    fill="#4361EE" />
                <path
                    d="M64 2C64 0.895431 64.8954 0 66 0H80C81.1046 0 82 0.895431 82 2C82 3.10457 81.1046 4 80 4H66C64.8954 4 64 3.10457 64 2Z"
                    fill="#4361EE" />
            </svg>
        </div>
    </div>
</template>

<script setup>

const props = defineProps({
    align: {
        type: String,
        default: 'center'
    },
    title: {
        type: String
    },
    desc: {
        type: String
    },
    name: {
        type: String
    },
    showDivider: {
        type: Boolean,
        default: true
    }
});

</script>

<style lang="scss" scoped>
.title-box-wrapper {
    display: flex;
    flex-direction: column;

    &-align-left {
        text-align: left;
        align-items: start;
    }

    &-align-center {
        text-align: center;
        align-items: center;
    }

    &-align-right {
        text-align: right;
        align-items: end;
    }
}

.title-box-name {
    color: #4361EE;
    text-align: inherit;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0;
}

.title-box-title {
    text-align: inherit;
    max-width: 550px;
    color: #111;
    font-size: 30px;
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 10px;
    margin-bottom: 12px;
}

.title-box-desc {
    text-align: inherit;
    max-width: 550px;
    color: #111;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    margin-bottom: 0;
    /* 24px */
}

.title-box-divider {
    text-align: inherit;
    margin-top: 34px;
    margin-bottom: 33px;
}
</style>