<template>
    <a-modal v-model:visible="showEdit" :footer="null" class="ac_header_choice_pop" @onCancel="closeEdit"
        @onOk="closeEdit" :destroyOnClose="true" :keyboard="true" :afterClose="() => { closeEdit(); }">
        <!-- 绑定邮箱 -->
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone" v-if="modifyEmailPop == 'email_step3'">
            <div class="h3">
                {{ operation == 4 ? $t("account.verifyEmail.title") : $t("account.changeEmail.title") }}
            </div>
            <div class="ac_profile_edit_form">
                <p class="ac_profile_edit_desc">
                    {{ $t("account.verifyEmail.agreement") }}
                </p>
                <div class="input_box_wrap">
                    <div class="input_box">
                        <a-input class="input" tabindex="1" v-model:value="oComfirmEmail"
                            :placeholder="$t('account.verifyEmail.enterEmail')" :maxlength="64" allow-clear>
                        </a-input>
                    </div>
                    <div class="input_box">
                        <a-input v-model:value="oEmailCaptcha" :placeholder="$t('account.verifyEmail.enterVerificationCode')
                            " size="large" :maxlength="6">
                        </a-input>
                        <a-button class="captcha_ant_btn" :disabled="!!countDownEmailCaptchaBtnText"
                            @click="getEmailCaptcha(oComfirmEmail)">{{
                                countDownEmailCaptchaBtnText
                                    ? $t("account.common.sent") +
                                    `（${countDownEmailCaptchaBtnText}s）`
                                    : $t("account.common.get")
                            }}</a-button>
                    </div>
                </div>
                <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
                    {{ oErrorHint }}
                </div>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="submitNewEmailOrPhone('email')">{{ $t("account.verifyEmail.verify")
                    }}</a-button>
            </div>
        </div>
        <!-- 更换邮箱 -->
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone ac_modify_email"
            v-if="modifyEmailPop == 'email_step1'">
            <div class="h3">{{ $t("account.changeEmail.title") }}</div>
            <div class="ac_profile_edit_form">
                <div class="h2">
                    {{ $t("account.changeEmail.currentEmail") }}
                </div>
                <div class="txt">{{ userInfo.email }}</div>
                <p class="ac_profile_edit_desc">
                    {{ $t("account.changeEmail.description") }}
                </p>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="modifyEmailPop = 'email_step2'">{{ $t("account.profile.changeBind")
                    }}</a-button>
            </div>
        </div>
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone" v-if="modifyEmailPop == 'email_step2'">
            <div class="h3">{{ $t("account.securityVerification.title") }}</div>
            <div class="ac_profile_edit_form">
                <p class="ac_profile_edit_desc">
                    {{ $t("account.securityVerification.methods") }}
                </p>
                <div class="input_box">
                    <a-select class="check_method_select" ref="select" :disabled="checkMethodChoiceList.length <= 1"
                        v-model:value="checkMethodChoiceValue" @change="checkMethodChoice">
                        <a-select-option v-for="(item, index) in checkMethodChoiceList" :key="index" value="">{{ item
                            }}</a-select-option>
                    </a-select>
                </div>
                <p class="ac_profile_edit_desc">
                    {{
                        $t("account.securityVerification.emailInstructions2", {
                            XX: userInfo.email,
                        })
                    }}
                </p>
                <div class="input_box">
                    <a-input v-model:value="oldEmailCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxlength="6">
                    </a-input>
                    <a-button class="captcha_ant_btn" :disabled="captchaLoading || !!unBindcountDownEmailCaptchaBtnText"
                        :loading="captchaLoading" @click="getOldEmailOrPhoneCaptcha(24, 4)">{{
                            unBindcountDownEmailCaptchaBtnText
                                ? $t("account.common.sent") +
                                `（${unBindcountDownEmailCaptchaBtnText}s）`
                                : $t("account.common.get")
                        }}</a-button>
                </div>

                <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
                    {{ oErrorHint }}
                </div>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="unbindEmailOrPhone(oldEmailCaptcha)">{{ $t("account.common.nextStep")
                    }}</a-button>
            </div>
        </div>

        <!-- 绑定手机 -->
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone" v-if="modifyEmailPop == 'phone_step3'">
            <div class="h3">
                {{
                    operation == 3
                        ? $t("account.bindPhoneNumber.title")
                        : $t("account.changePhoneNumber.title")
                }}
            </div>
            <div class="ac_profile_edit_form">
                <p class="ac_profile_edit_desc">
                    {{ $t("account.bindPhoneNumber.agreement") }}
                </p>
                <div class="input_box_wrap">
                    <div class="input_box">
                        <a-input-group class="phone_input_group" compact>
                            <a-select v-model:value="phoneCountryNo" class="phone_country_no"
                                :placeholder="t('account.phone.areaCode')">
                                <a-select-option v-for="item in phoneCountryNoList" :key="item" :value="item">{{ item
                                    }}</a-select-option>
                            </a-select>
                            <a-input class="input" tabindex="1" v-model:value="oComfirmPhone" :placeholder="$t(
                                'account.bindPhoneNumber.enterPhoneNumber'
                            )
                                " :maxlength="64" style="flex: 1" allow-clear />
                        </a-input-group>
                    </div>
                    <div class="input_box">
                        <a-input v-model:value="oPhoneCaptcha" :placeholder="$t(
                            'account.bindPhoneNumber.enterVerificationCode'
                        )
                            " size="large" :maxlength="6">
                        </a-input>
                        <a-button class="captcha_ant_btn" :disabled="captchaLoading || !!countDownPhoneCaptchaBtnText"
                            :loading="captchaLoading" @click="getPhoneCaptcha(oComfirmPhone)">{{
                                countDownPhoneCaptchaBtnText
                                    ? $t("account.common.sent") +
                                    `（${countDownPhoneCaptchaBtnText}s）`
                                    : $t("account.common.get")
                            }}</a-button>
                    </div>
                </div>
                <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
                    {{ oErrorHint }}
                </div>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="submitNewEmailOrPhone('phone')">{{ $t("account.bindPhoneNumber.bind")
                    }}</a-button>
            </div>
        </div>

        <!-- 更换手机 -->
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone ac_modify_email"
            v-if="modifyEmailPop == 'phone_step1'">
            <div class="h3">{{ $t("account.changePhoneNumber.title") }}</div>
            <div class="ac_profile_edit_form">
                <div class="h2">
                    {{ $t("account.changePhoneNumber.currentPhoneNumber") }}
                </div>
                <div class="txt">{{ userInfo.phone }}</div>
                <p class="ac_profile_edit_desc">
                    {{ $t("account.changePhoneNumber.description") }}
                </p>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="modifyEmailPop = 'phone_step2'">{{ $t("account.profile.changeBind")
                    }}</a-button>
            </div>
        </div>
        <div class="ac_common_box ac_c_mb20 ac_c_r20 ac_check_email_phone" v-if="modifyEmailPop == 'phone_step2'">
            <div class="h3">{{ $t("account.securityVerification.title") }}</div>
            <div class="ac_profile_edit_form">
                <p class="ac_profile_edit_desc">
                    {{ $t("account.securityVerification.methods") }}
                </p>
                <div class="input_box">
                    <a-select class="check_method_select" ref="select" :disabled="checkMethodChoiceList.length <= 1"
                        v-model:value="checkMethodChoiceValue" @change="checkMethodChoice">
                        <a-select-option v-for="(item, index) in checkMethodChoiceList" :key="index" value="">{{ item
                            }}</a-select-option>
                    </a-select>
                </div>
                <p class="ac_profile_edit_desc">
                    {{
                        $t("account.securityVerification.phoneInstructions2", {
                            XX: userInfo.phone,
                        })
                    }}
                </p>
                <div class="input_box">
                    <a-input v-model:value="oldPhoneCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxlength="6">
                    </a-input>
                    <a-button class="captcha_ant_btn" :disabled="!!unBindcountDownPhoneCaptchaBtnText"
                        @click="getOldEmailOrPhoneCaptcha(23, 3)">{{
                            unBindcountDownPhoneCaptchaBtnText
                                ? $t("account.common.sent") +
                                `（${unBindcountDownPhoneCaptchaBtnText}s）`
                                : $t("account.common.get")
                        }}</a-button>
                </div>

                <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
                    {{ oErrorHint }}
                </div>
            </div>
            <div class="ac_pop_btn_line">
                <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                    type="primary" @click="unbindEmailOrPhone(oldPhoneCaptcha)">{{ $t("account.common.nextStep")
                    }}</a-button>
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { useAccountStore } from "~/stores/account";
import { PHONE_COUNTRY_NO } from "~/constants/common";
import {
    postAuthStep1,
    postAuthStep2,
    postAuthStep3,
    postAuthStep4,
} from "~/api/login";
// 引入验证码
import { useYcCaptcha } from "~/utils/ycCaptcha";
const { initYcCaptcha } = useYcCaptcha();
const { locale, t } = useI18n();
const { $validator } = useNuxtApp();
const $v: any = $validator;
const accountStore = useAccountStore();
// 控制弹窗
const showEdit = ref<boolean>(false);

// 当前操作类型
const curActionType = ref<string>("");

// 绑定或者 更换密保邮箱pop
const modifyEmailPop = ref<string>(""); // step1 换绑老邮箱  step2 换绑老邮箱验证码  step3 绑定新邮箱



// 用户信息
const userInfo: any = computed(() => accountStore.userInfo);

// 错误提示
const oErrorHint = ref<string>("");
const oErrorHintSuccess = ref<boolean>(false);
// 提交状态
const submitLoading = ref<boolean>(false);
const captchaLoading = ref<boolean>(false);
// 获取验证码倒计时
const countDownEmailCaptchaBtnText = ref<number | boolean>(false);
// 手机验证码倒计时
const countDownPhoneCaptchaBtnText = ref<number | boolean>(false);
// 刷新页面
const $emit = defineEmits(["refreshUserInfo", "closeEdit"]);

// ==============================24 更换密保邮箱
// 选择验证方式
const checkMethodChoiceValue = ref<string>("");
const checkMethodChoiceList = ref<string[]>([]);
// 换绑老邮箱验证码
const oldEmailCaptcha = ref<string>("");
// 换绑老手机验证码
const oldPhoneCaptcha = ref<string>("");
// 换绑邮箱倒计时
const unBindcountDownEmailCaptchaBtnText = ref<number | boolean>(false);
// 换绑手机验证码倒计时
const unBindcountDownPhoneCaptchaBtnText = ref<number | boolean>(false);

// 手机区号
const phoneCountryNo = ref<string | undefined>(undefined);
const phoneCountryNoList = ref<string[]>(PHONE_COUNTRY_NO);

const $props = defineProps({
    operation: {
        type: Number,
        default: 0,
    },
});

// 绑定手机号码
const oComfirmPhone = ref<string>("");
const oPhoneCaptcha = ref<string>("");
// 绑定邮箱
const oComfirmEmail = ref<string>("");
const oEmailCaptcha = ref<string>("");
// 当前步骤的guid
const guid = ref<string>("");

// 全局设置
const operation = ref<number>(0);
const auth_type = ref<number>(0);

// 关闭弹窗
const closeEdit = () => {
    showEdit.value = false;
    $emit("closeEdit");
};
/**
 * 错误处理
 * @param res 
 * @param ycCaptchaCallback 验证码回调
 */
const errorHandle = (res: any, ycCaptchaCallback?: Function) => {
    oErrorHint.value = res.message;
    // 401 未登录
    if (res.code == 401) {
        message.error({
            content: res.message,
            class: "c_message_big",
            style: {
                marginTop: "20vh",
            },
            duration: 3,
            onClose: () => {
                navigateTo(`/${locale.value}/account/login`);
            },
        });
    } else if (res.code == 2044) {  // 是否需要图形验证码
        ycCaptchaCallback && ycCaptchaCallback();
    }
}

/**
 * step1 验证配置
 * @param operation 操作类型
 */
const postAuthStep1Fun = async (operation: number, auth_type: number = 0, token: string = '', captcha_verification: string = '') => {
    try {
        const res: any = await postAuthStep1({
            operation,
            auth_type,
            token,
            captcha_verification
        });
        console.log(res);
        if (res.code == 200 || res.code == 0) {
            guid.value = res.data.guid;
        }
        return res;
    } catch (error) {
        console.log(error);
    }
};

// step2
const postAuthStep2Fun = async (password: string) => {
    try {
        const res: any = await postAuthStep2({
            guid: guid.value,
            password,
            operation: operation.value,
            auth_type: auth_type.value,
        });
        console.log(res);
        return res;
    } catch (error) {
        console.log(error);
    }
};

// step3
const postAuthStep3Fun = async (data: any) => {
    console.log("postAuthStep3Fun", data);
    try {
        const res: any = await postAuthStep3({
            guid: guid.value,
            operation: operation.value,
            auth_type: auth_type.value,
            ...data,
        });
        console.log(res);
        oErrorHintSuccess.value = false;
        if (res.code == 200 || res.code == 0) {
            guid.value = res.data.guid;
        }
        return res;
    } catch (error) {
        console.log(error);
    }
};
// step4
const postAuthStep4Fun = async (password: string) => {
    try {
        const res: any = await postAuthStep4({
            guid: guid.value,
            password,
            operation: operation.value,
            auth_type: auth_type.value,
        });
        return res;
    } catch (error) {
        console.log(error);
    }
};

// 获取邮箱验证码
const getEmailCaptcha = async (email: string, token: string = '', captcha_verification: string = '') => {
    oErrorHintSuccess.value = false;
    if (!!countDownEmailCaptchaBtnText.value) {
        return;
    }
    email = email.trim();
    if (!(email && $v.isEmail(email))) {
        oErrorHint.value = t("account.email.invalid");
        return;
    }
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    captchaLoading.value = true;
    const res: any = await postAuthStep3Fun({ email, token, captcha_verification });
    captchaLoading.value = false;
    if (res.code == 200 || res.code == 0) {
        oErrorHint.value = t("account.verification.codeSent");
        oErrorHintSuccess.value = true;
        // 倒计时
        countDownEmailCaptchaBtnText.value = 60;
        const timer = setInterval(() => {
            (countDownEmailCaptchaBtnText.value as number)--;
            if ((countDownEmailCaptchaBtnText.value as number) <= 0) {
                clearInterval(timer);
                countDownEmailCaptchaBtnText.value = false;
            }
        }, 1000);
    } else {
        errorHandle(res, () => {
            initYcCaptcha(getEmailCaptcha, [email]);
        });
    }
};

// 获取手机验证码
const getPhoneCaptcha = async (phone: string, token: string = '', captcha_verification: string = '') => {
    oErrorHintSuccess.value = false;
    if (!!countDownPhoneCaptchaBtnText.value) {
        return;
    }
    console.log("getPhoneCaptcha", phone);
    phone = phone.trim();
    if (!phone) {
        oErrorHint.value = t("account.phone.empty");
        return;
    };
    if (!phoneCountryNo.value) {
        oErrorHint.value = t("account.phone.areaCodeEmpty");
        return;
    }
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    captchaLoading.value = true;
    const res: any = await postAuthStep3Fun({ phone, area_code: phoneCountryNo.value, token, captcha_verification });
    captchaLoading.value = false;
    if (res.code == 200 || res.code == 0) {
        oErrorHint.value = t("account.email.codeSent");
        oErrorHintSuccess.value = true;
        // 倒计时
        countDownPhoneCaptchaBtnText.value = 60;
        const timer = setInterval(() => {
            (countDownPhoneCaptchaBtnText.value as number)--;
            if ((countDownPhoneCaptchaBtnText.value as number) <= 0) {
                clearInterval(timer);
                countDownPhoneCaptchaBtnText.value = false;
            }
        }, 1000);
    } else {
        errorHandle(res, () => {
            initYcCaptcha(getPhoneCaptcha, [phone]);
        });
    }
};

// 提交新邮箱和手机
const submitNewEmailOrPhone = async (type: string) => {
    oErrorHintSuccess.value = false;
    let _captcha = "";
    if (type == "email") {
        oComfirmEmail.value = oComfirmEmail.value.trim();
        if (!oComfirmEmail.value) {
            oErrorHint.value = t("account.email.empty");
            return;
        }
        if (!$v.isEmail(oComfirmEmail.value)) {
            oErrorHint.value = t("account.email.invalid");
            return;
        }
        _captcha = oEmailCaptcha.value;
    }
    if (type == "phone") {
        oComfirmPhone.value = oComfirmPhone.value.trim();
        if (!oComfirmPhone.value) {
            oErrorHint.value = t("account.phone.invalid");
            return;
        }
        _captcha = oPhoneCaptcha.value;
    }
    if (!(_captcha && _captcha.length == 6)) {
        oErrorHint.value = t("account.verification.codeInvalid");
        return;
    }
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    submitLoading.value = true;
    const res: any = await postAuthStep4Fun(_captcha);
    submitLoading.value = false;
    if (res.code == 200 || res.code == 0) {
        if (type == "email") {
            message.success(t("account.email.verificationSuccess"));
        }
        if (type == "phone") {
            message.success(t("account.securityCheck.phoneNumberBound"));
        }
        $emit("refreshUserInfo");
        closeEdit();
    } else {
        errorHandle(res);
    }
};

//==============================================换绑流程
// 解绑老的邮箱手机的验证码
const getOldEmailOrPhoneCaptcha = async (
    actionType: number,
    auth_type: number,
    token: string = '',
    captcha_verification: string = ''
) => {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    try {
        const res: any = await postAuthStep1Fun(actionType, auth_type, token, captcha_verification);
        if (res.code == 200 || res.code == 0) {
            oErrorHint.value = t("account.verification.codeSent");
            oErrorHintSuccess.value = true;
            // 邮箱倒计时
            if (curActionType.value == "email") {
                unBindcountDownEmailCaptchaBtnText.value = 60;
                const timer = setInterval(() => {
                    (unBindcountDownEmailCaptchaBtnText.value as number)--;
                    if ((unBindcountDownEmailCaptchaBtnText.value as number) <= 0) {
                        clearInterval(timer);
                        unBindcountDownEmailCaptchaBtnText.value = false;
                    }
                }, 1000);
            }
            // 手机倒计时
            if (curActionType.value == "phone") {
                unBindcountDownPhoneCaptchaBtnText.value = 60;
                const timer = setInterval(() => {
                    (unBindcountDownPhoneCaptchaBtnText.value as number)--;
                    if ((unBindcountDownPhoneCaptchaBtnText.value as number) <= 0) {
                        clearInterval(timer);
                        unBindcountDownPhoneCaptchaBtnText.value = false;
                    }
                }, 1000);
            }
        } else {
            errorHandle(res, () => {
                initYcCaptcha(getOldEmailOrPhoneCaptcha, [actionType, auth_type]);
            });
        }
    } catch (error) {
        console.log(error);
    }
};

//解绑邮箱
const unbindEmailOrPhone = async (Captcha: string) => {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    if (!(Captcha && Captcha.length == 6)) {
        oErrorHint.value = t("account.verification.codeInvalid");
        return;
    }
    try {
        submitLoading.value = true;
        const res: any = await postAuthStep2Fun(Captcha);
        submitLoading.value = false;
        if (res.code == 200 || res.code == 0) {
            // message.success("解绑成功");
            modifyEmailPop.value =
                curActionType.value == "phone" ? "phone_step3" : "email_step3";
        } else {
            errorHandle(res);
        }
    } catch (error) {
        console.log(error);
    }
};

// 选择验证方式
const checkMethodChoice = () => {
    console.log("选择验证方式", checkMethodChoiceValue.value);
};
// 重置数据
const resetData = () => {
    oComfirmPhone.value = "";
    oPhoneCaptcha.value = "";
    oComfirmEmail.value = "";
    oEmailCaptcha.value = "";
    oldEmailCaptcha.value = "";
    oldPhoneCaptcha.value = "";
    operation.value = 0;
    auth_type.value = 0;
};
onMounted(() => {
    watch(
        () => $props.operation,
        (newVal) => {
            oErrorHint.value = "";
            oErrorHintSuccess.value = false;
            resetData();
            operation.value = newVal;
            // 绑定邮箱
            if (newVal == 4) {
                showEdit.value = true;
                modifyEmailPop.value = "email_step3";
                curActionType.value = "email";
                auth_type.value = 4;
                oComfirmEmail.value =
                    (userInfo.value.plaintext_username as string) || "";
                postAuthStep1Fun(newVal, 4);
            }
            // 更换密保邮箱
            if (newVal == 24) {
                showEdit.value = true;
                modifyEmailPop.value = "email_step1";
                curActionType.value = "email";
                auth_type.value = 4;
                checkMethodChoiceList.value = [
                    t("account.securityVerification.emailVerification"),
                ];
            }
            // 绑定手机
            if (newVal == 3) {
                showEdit.value = true;
                modifyEmailPop.value = "phone_step3";
                curActionType.value = "phone";
                auth_type.value = 3;
                postAuthStep1Fun(newVal, 3);
            }
            // 更换密保手机
            if (newVal == 23) {
                showEdit.value = true;
                modifyEmailPop.value = "phone_step1";
                curActionType.value = "phone";
                auth_type.value = 3;
                checkMethodChoiceList.value = [
                    t("account.securityVerification.phoneVerification"),
                ];
            }
        }
    );
});
</script>
<style lang="scss">
@import url(~/assets/styles/account/checkEmailAndPhoneAll.scss);
</style>
<style lang="scss" scoped>
@import url(~/assets/styles/account/common.scss);
@import url(~/assets/styles/account/checkEmailAndPhone.scss);
</style>
