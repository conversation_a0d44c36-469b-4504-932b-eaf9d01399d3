
.login_pop_main {
    position: relative;
    z-index: 99;
    .c_logo_blue {
        margin: 0 auto;
    }
    a {
        text-decoration: underline;
        margin: 0 5px;
    }
}

.tab_name {
    color: #111;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-weight: bold;
    line-height: 54px;
    padding-top:20px;
    // border-bottom: 1px solid var(--gray_color);
}

.input_wrap {
    padding-top: 20px;

    .input_box {
        height: 50px;
        border: 1px solid var(--gray_color);
        border-radius: 10px;
        margin-bottom: 16px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        &:last-child {
            margin-bottom: 4px;
        }
        .input {
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            color: #111;
            border: none;
            margin-left: 10px;
            flex: 1;

            &.ant-input-status-error {
                border-color: #ff4d4f;
            }

            &::placeholder {
                color: #999;
            }

            &:focus {
                border: none;
                background: #f0f0f0;
            }
        }
    }
}

.login_error_hint {
    min-height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #ff4d4f;
}

.login_btn {
    cursor: pointer;
    background: var(--base_color);
    height: 56px;
    width: 100%;
    border-radius: 28px;
    text-align: center;
    font-family: "PingFang SC";
    color: #fff;
    font-size: 20px;
    line-height: 56px;
    transition: all 0.5s ease;
    padding: 0;
    &:hover {
        background: #5a7adb;
        transform: translateY(-2px)
    }
}


.login_other {
    line-height: 22px;
    padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--base_color);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;

    .btn {
        margin: 0 10px;
        cursor: pointer;
    }
}

.login_hint {
    padding-top: 30px;
    font-size: 14px;
    line-height: 24px;
    text-align: center;

    h4 {
        color: #1A1A1A;
    }

    p {
        color: #999;
    }
}

.pop_doubleHint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    background: #fff;
    max-width: 420px;
    width: 95%;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 0 20px #ddd;
    z-index: 999;
    p {
        font-size: 14px;
        line-height: 24px;
    }
    .pop_btn_line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 20px;
        .c_small_btn {
            margin: 10px 0;
        }
    }
}

.login_hint_a {
    cursor: pointer;
    color: var(--base_color);
    padding-top: 30px;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
}
@media screen and (max-width: 768px) {

    .tab_name {
        line-height: 40px;
        padding-top: 20px;
    }

    .input_wrap {
        padding-top: 20px;

        .input_box {
            height: 40px;
            padding: 0 10px;

            .input {
                height: 30px;
                line-height: 30px;
            }
        }
    }

    .login_btn {
        margin-top: 10px;
        height: 40px;
        line-height: 40px;
    }

    .login_hint {
        padding-top: 10px;
        font-size: 12px;
        line-height: 24px;
    }


    .pop_doubleHint {
        .pop_btn_line {
            justify-content: center;
            .c_small_btn {
                margin: 10px 0;
                width: 100%;
            }
        }
    }
}

