import URLS from "~/api/uri";
import { useAuthStore } from '~/stores/auth';
import { mediaIconList } from "~/constants/common";

// const PB_APP_ID = process.env.VITE_PB_APP_ID;
let PB_APP_ID = 0;
if(typeof window !== "undefined") {
    // const domain = 'funtales.com';
    // if(window.location.href.indexOf('oauth') > -1){
        const domain = window.location.origin;
        if(process.env.NODE_ENV === "development"){
            PB_APP_ID = ********
        }else if(domain.indexOf('funtales.com') > -1){
            PB_APP_ID = ********
        }else if(domain.indexOf('playbest.net') > -1){
            PB_APP_ID = ********
        }
    // }
}
export const pbAppId = PB_APP_ID;

// 钩子函数 无法放在模块顶层
const axios = (options: any) => {
    const { $axios } = useNuxtApp() as any;
    return $axios(options);
};
import { $axios } from "~/utils/axiosCustiom";
// /**
//  * @function 用户登录
//  * @method post
//  * @param account 账号(邮箱)
//  * @param password 密码
//  * @param locale 语言标识
//  * @returns
//  */
// const login = (account: string, password: string, locale: string = "en") => {
//     let url = URLS.Login.login;
//     //console.log(url)
//     return $axios({
//         url,
//         method: "post",
//         data: {
//             account,
//             password,
//             locale,
//         },
//     });
//     // return $axios.post(url, {})
// };

/**
 * @function 用户登录
 * @method post
 * @param username 账号(邮箱)
 * @param password 密码
 * @param locale 语言标识
 * @returns
 */
const login = (username: string, password: string, locale: string = "en",token:string = '',captcha_verification:string = '',game_id:string = '') => {
    let url = URLS.Login.login_v2;
    //console.log(url)
    return $axios({
        url,
        method: "post",
        data: {
            username,
            password,
            locale,
            app_id: PB_APP_ID || 0,
            token,
            captcha_verification,
            game_id, // 给游戏用的中转页一定要传 自己用的pb和fun不用传
        },
    });
    // return $axios.post(url, {})
};

/**
 * @function 用户注册
 * @param username
 * @param password
 * @param confirm_password
 * @returns
 */
const postRegister = (
    username: string,
    password: string,
    confirm_password: string,
    token: string = "",
    captcha_verification: string = "",
    game_id: string = "",
) => {
    let url = URLS.Login.postRegister;
    //console.log(url)
    return $axios({
        url,
        method: "post",
        data: {
            username,
            password,
            confirm_password,
            app_id: PB_APP_ID || 0,
            token,
            captcha_verification,
            game_id, // 给游戏用的中转页一定要传 自己用的pb和fun不用传
        },
    });
};

/**
 * @function 用户注册
 * @param username
 * @param password
 * @param confirm_password
 * @returns
 */
const getUserInfo = (systemType: string = '') => {
    let url = URLS.Login.getUserInfo;
    //console.log(url)
    return $axios({
        url,
        headers: {
            'X-System-Type': systemType,
        },
        method: "get",
    });
};

/**
 * @function 获取头像选择列表
 * @returns
 */
const getAvatarList = () => {
    let url = URLS.Login.getAvatarList;
    //console.log(url)
    return $axios({
        url,
        method: "get",
    });
};

interface UserInfo {
    /** 头像 */
    avatar?: string;
    /** 生日 */
    birth_date?: string;
    /** 名 */
    first_name?: string;
    /** 性别: 1男性，2女性，3保密，4其他 */
    gender?: 1 | 2 | 3 | 4;
    /** 姓 */
    last_name?: string;
    /** 昵称 */
    nick_name?: string;
}

/**
 * @function 更新用户信息
 * @param data 用户信息
 * @returns
 */
const postUpdateUserInfo = (data: UserInfo) => {
    let url = URLS.Login.postUpdateUserInfo;
    //console.log(url)
    return $axios({
        url,
        method: "post",
        data,
    });
};

/**
 * @function 修改昵称
 * @param data 昵称
 * @returns
 */
const postUpdateNickname = (nick_name: string) => {
    let url = URLS.Login.postUpdateNickname;
    return $axios({
        url,
        method: "post",
        data: {
            nick_name,
        },
    });
};

/**
 * @function 改密、绑定、换绑、解绑接口1
 * @param operation 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31
 * @param auth_type 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）
 * @returns
 */
interface AuthStep1Data {
    /** 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31 */
    operation: number;
    /** 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）	 */
    auth_type: number;
    /** 图形验证码token */
    token: string;
    /** 图形验证码验证 */
    captcha_verification: string;
}
const postAuthStep1 = (data: AuthStep1Data) => {
    let url = URLS.Login.postAuthStep1;
    return $axios({
        url,
        method: "post",
        data:{
            proxy_app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};

/**
 * @function 改密、绑定、换绑、解绑接口2
 * @param data 数据
 * @returns
 */
interface AuthStep2Data {
    /** 若为绑定邮箱或手机则为账号密码，修改密码则为验证码或原密码 */
    password: string;
    /** 流程id */
    guid: string;
    /** 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31 */
    operation: number;
    /** 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）	 */
    auth_type: number;
}
const postAuthStep2 = (data: AuthStep2Data) => {
    let url = URLS.Login.postAuthStep2;
    return $axios({
        url,
        method: "post",
        data:{
            proxy_app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};

interface AuthStep3Data {
    /** 流程id */
    guid: string;
    /** 确认密码，修改密码时该字段必传，其他情况不填 */
    confirm_password?: string;
    /** 绑定邮箱时填邮箱，其他情况不填 */
    email?: string;
    /** 修改密码时填新密码，其他情况不填 */
    password?: string;
    /** 绑定手机时填手机号码，其他情况不填 */
    phone?: string;
    /** 注销账号时填写注销理由 */
    reason?: string;
    /** 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31 */
    operation: number;
    /** 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）	 */
    auth_type: number;
    /** 手机区号 */
    area_code?: string;
    /** 图形验证码token */
    token?: string;
    /** 图形验证码验证 */
    captcha_verification?: string;
}
/**
 * @function 改密、绑定、换绑、解绑接口3
 * @param data 数据 @interface AuthStep3Data
 * @returns
 */
const postAuthStep3 = (data: AuthStep3Data) => {
    let url = URLS.Login.postAuthStep3;
    return $axios({
        url,
        method: "post",
        data:{
            proxy_app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};

/**
 * @function 改密、绑定、换绑、解绑接口4
 * @param data 数据 @interface AuthStep4Data
 * @returns
 */
interface AuthStep4Data {
    /** 流程id */
    guid: string;
    /** 邮箱或手机验证码，绑定邮箱或手机时需要进行验证码确认 */
    password: string;
    /** 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31 */
    operation: number;
    /** 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）	 */
    auth_type: number;
}
const postAuthStep4 = (data: AuthStep4Data) => {
    let url = URLS.Login.postAuthStep4;
    return $axios({
        url,
        method: "post",
        data:{
            proxy_app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};

/**
 * @function 获取用户邮箱账号是否可以找回密码
 * @param username 邮箱账号
 * @returns
 */
const postResetPasswordMethod = (username: string) => {
    let url = URLS.Login.postResetPasswordMethod;
    return $axios({
        url,
        method: "post",
        data: {
            username,
            app_id: PB_APP_ID || 0,
        },
    });
};


interface noLoginStep1Data {
    auth_type: number; // 选择验证方式（修改密码时可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）
    operation: number; // 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31
    username: string; // 用户名
    /** 图形验证码token */
    token?: string;
    /** 图形验证码验证 */
    captcha_verification?: string;
}
/**
 * @function 【无需登录】改密、绑定、换绑、解绑接口1
 * @param data 数据 @interface noLoginStep1Data
 * @returns
 */
const postNoLoginStep1 = (data:noLoginStep1Data ) => {
    let url = URLS.Login.postNoLoginStep1;
    return $axios({
        url,
        method: "post",
        data: {
            app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};


interface noLoginStep2Data {
    guid: string; // 流程id
    password: string; // 若为绑定邮箱或手机则为账号密码，修改密码则为验证码或原密码
    snda_id: string; // 账号id
    auth_type: number; // 选择验证方式（修改密码时可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）
    operation: number; // 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31
    username: string; // 绑定邮箱或手机时填邮箱或手机，其他情况不填
}
/**
 * @function 【无需登录】改密、绑定、换绑、解绑接口2
 * @param data 数据 @interface noLoginStep2Data
 * @returns
 */
const postNoLoginStep2 = (data:noLoginStep2Data ) => {
    let url = URLS.Login.postNoLoginStep2;
    return $axios({
        url,
        method: "post",
        data: {
            app_id: PB_APP_ID || 0,
            ...data,
        },
    });
};

interface noLoginStep3Data {
    confirm_password?: string; // 确认密码，修改密码时该字段必传，其他情况不填
    email?: string; // 绑定邮箱时填邮箱，其他情况不填
    guid: string; // 流程id
    password?: string; // 修改密码时填新密码，其他情况不填
    phone?: string; // 绑定手机时填手机号码，其他情况不填
    reason?: string; // 注销账号时填写注销理由
    snda_id: string; // 账号id
    auth_type: number; // 选择验证方式（修改密码时可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）
    operation: number; // 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31
    username: string; // 绑定邮箱或手机时填邮箱或手机，其他情况不填
    area_code?: string; // 手机区号
    /** 图形验证码token */
    token?: string;
    /** 图形验证码验证 */
    captcha_verification?: string;
}

/**
 * @function 【无需登录】改密、绑定、换绑、解绑接口3
 * @param data 数据 @interface noLoginStep3Data
 * @returns
 */
const postNoLoginStep3 = (data:noLoginStep3Data ) => {
    let url = URLS.Login.postNoLoginStep3;
    return $axios({
        url,
        method: "post",
        data: {
            app_id: PB_APP_ID || 0,
            ...data,
        },
    });
}

interface noLoginStep4Data {
    guid: string; // 流程id
    password: string; // 邮箱或手机验证码，绑定邮箱或手机时需要进行验证码确认
    snda_id: string; // 账号id
    auth_type: number; // 选择验证方式（修改密码时可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）
    operation: number; // 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31
}
/**
 * @function 【无需登录】改密、绑定、换绑、解绑接口4
 * @param data 数据 @interface noLoginStep4Data
 * @returns
 */
const postNoLoginStep4 = (data:noLoginStep4Data ) => {
    let url = URLS.Login.postNoLoginStep4;
    return $axios({
        url,
        method: "post",
        data: {
            app_id: PB_APP_ID || 0,
            ...data,
        },
    });
}


// 定义第三方登录配置列表的请求参数类型接口
interface postThirdConfigListType {
    login_id?: string; // 可选的三方登录方式ID
    game_id?: string; // 可选的游戏ID
    channel_id?: string; // 可选的渠道ID
}

/**
 * 获取三方登录配置列表
 * @param config
 * @returns
 */
const postThirdConfigList = (config: postThirdConfigListType = {}) => {
    return $axios({
        method: "POST",
        url: `${URLS.Login.postThirdConfigList}`,
        data: {
            app_id: PB_APP_ID || 0,
            ...config,
        },
    });
};

// 拉取第三方配置
const getThirdConfigList = async (config: postThirdConfigListType = {}) => {
    try {
        const res: any = await postThirdConfigList(config);
        console.log(res);
        if (res.code == 0) {
            const { third } = res.data;
            third.forEach((item: any) => {
                item.mediaIcon =
                    mediaIconList[item.login_id as keyof typeof mediaIconList];
                item.game.forEach((game: any) => {
                    game.game_name = JSON.parse(game.game_name);
                    game.login_id = item.login_id;
                });
            });
            const authStore = useAuthStore();
            authStore.setLoginThirdConfig(third);
            authStore.setAccountPassSwitch(res.data.account_pass_switch);
        }
    } catch (error) {
        console.error(error);
    }
};

export {
    login,
    postRegister,
    getUserInfo,
    getAvatarList,
    postUpdateUserInfo,
    postUpdateNickname,
    postAuthStep1,
    postAuthStep2,
    postAuthStep3,
    postAuthStep4,
    postResetPasswordMethod,
    postNoLoginStep1,
    postNoLoginStep2,
    postNoLoginStep3,
    postNoLoginStep4,
    postThirdConfigList, 
    getThirdConfigList,
};
