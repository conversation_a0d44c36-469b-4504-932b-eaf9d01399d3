<template>
    <div class="content">
        <!-- 顶部banner -->
        <div class="header-banner">
            <img src="~/assets/images/main/game_list_banner_bg.png" alt="PLAY BEST" class="pc-show" />
            <img src="~/assets/images/support/banner.png" alt="PLAY BEST" class="mob-show" />
            <div>{{ $t("pages.index.game.list.banner") }}</div>
        </div>

        <!-- 顶部列表区标题
        <div class="title-wrapper">
            <main-title-box :name="titleContent.name" :title="titleContent.title" :desc="titleContent.desc"
                align='left'></main-title-box>
        </div> -->

        <!-- 游戏列表 -->
        <div class="games-box-wrapper">
            <main-game-box :data="games" size="large"></main-game-box>
        </div>


    </div>
</template>

<script setup lang="ts">
import { getGameList } from '~/api/main';
const { locale } = useI18n();

const games = ref<any>([]);


try {
    const gamesList = await getGameList(locale.value);
    games.value = computed(() => formatCmsData(gamesList, {
        "logo": "mob_thumb",
        "painting": "pc_thumb",
        "figure": "ad_thumb",
        "name": "title",
        "desc": "ad_desc",
        "link": "ad_url"
    })?.filter?.(item => {
        return item.isChoice
    })).value;

    console.log("🚀 ~ games.value:", games.value)
} catch (e) {
    console.log("🚀 ~ e:", e)

}


</script>


<style lang="scss">
// 移动端尺寸 pc-show的内容隐藏
@media screen and (max-width:820px) {

    .pc-show {
        display: none !important;
    }

}

// pc端尺寸 mob-show的内容隐藏
@media screen and (min-width:821px) {
    .mob-show {
        display: none !important;
    }
}
</style>

<style lang="scss" scoped>
.header-banner {
    max-height: 350px;
    overflow: hidden;
    position: relative;

    >img {
        width: 100%;
        height: 100%;
        display: block;
    }

    >div {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding-top: 60px;
        display: flex;
        align-items: center;
        justify-content: center;

        color: #FFF;
        text-align: center;
        font-size: 50px;
        font-style: normal;
        font-weight: 900;
        line-height: 1.2;
    }
}

.games-box-wrapper {
    background: url("~/assets/images/main/main_page_bg.png") center top repeat-y;
    background-size: 100% auto;
    padding-top: 60px;
    padding-bottom: 80px;
}

.title-wrapper {
    width: 100%;
    padding: 73px calc(2.9vw + 35px) 60px;
    max-width: 1544px;
    margin: 0 auto;

    :deep(.title-box-title) {
        max-width: 100%;
    }

    :deep(.title-box-divider) {
        margin-bottom: 0;
    }
}

.content {

    &:deep(.game-box-wrapper) {
        :deep(.game-box) {
            width: calc((100% - 35px + 2.8669724771vw) * 2) / 2;
        }
    }

}



@media screen and (max-width:820px) {
    .header-banner {
        height: 300px;

        >div {
            font-size: 38px;
        }

    }

    .games-box-wrapper {
        padding-top: 50px;
    }
}
</style>