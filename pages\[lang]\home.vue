<template>
  <div class="content">
    <div class="banner">
      <img class="img banner_pc" :src="bannerHome.pc" alt="" />
      <img class="img banner_mobile" :src="bannerHome.mb" alt="" />
    </div>
    <div class="goods_wrap">
      <template v-for="(item, index) in projectList" :key="index">
        <div class="goods_item">
          <div class="goods_main" @click="goToPay(item.id)">
            <div class="goods_main_box">
              <div class="top" :style="{
                background: `url(${item.Icon}) top center/100% 100% no-repeat`,
              }">
                <div class="txt">{{ item.name[locale] }}</div>
              </div>
              <div class="bottom">
                <!-- <NuxtLink :to='`/${locale}/pay/${item.id}`' class="pay_btn">
									{{ $t('pages.index.pay') }}
								</NuxtLink> -->
                <div class="pay_btn">{{ $t("pages.index.pay") }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- <div class="goods_item">
				<div class="goods_main">
					<div class="goods_main_box">
						<div class="top"
							:style="{ background: `url(/_nuxt/assets/images/index/demo_goods.jpg) top center/100% 100% no-repeat` }">
							<div class="txt">三国杀</div>
						</div>
						<div class="bottom">
							<div class="pay_btn">购买</div>
						</div>
					</div>
				</div>
			</div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
// import bannerIndex from '~/assets/images/index/demo_goods.jpg';
import { getProjectList } from "~/api/pay";
import { useAuthStore } from '~/stores/auth.js';
import { useUserStore } from '~/stores/user.js';

import banner_pc from "~/assets/images/index/banner.jpg"
import banner_mb from "~/assets/images/index/banner_m.jpg"
import ogImg from '~/assets/images/common/google_share_img.jpg';
const { locale, t: $t } = useI18n();

const userStore = useUserStore();
const authStore = useAuthStore();

if (import.meta.env.VITE_NODE_ENV == "production") {

  const keywords = $t('pages.payment.meta.keywords');
  const description = $t('pages.payment.meta.description');

  // 配置 seo
  useSeoMeta({
    description,
    keywords,
    ogTitle: 'PLAYBEST',
    ogDescription: description,
    ogSiteName: "PLAYBEST",
    ogImage: ogImg,
    twitterCard: 'summary_large_image',
  })
}


// 数据 钩子
interface Project {
  Icon: string;
  id: number;
  name: { [key: string]: string };
}
const projectList = ref<Project[]>([]);
const bannerHome = ref<{ [key: string]: any }>({})
// 获取页面列表
try {
  const {
    data: getJsonResult,
    pending,
    error,
    refresh,
  } = await getProjectList();
  let result: any = getJsonResult.value;
  // console.log(error.value);
  if (result) {
    if (result.code == 200 || result.code == 0) {
      const { data } = result;
      // console.error('data.projects', data.projects);
      projectList.value = data.projects;
      bannerHome.value = {
        pc: data.banner && data.banner[locale.value] && data.banner[locale.value].pc || banner_pc,
        mb: data.banner && data.banner[locale.value] && data.banner[locale.value].mb || banner_mb,
      }

      if (process.browser) {
        userStore.setProjects(data.projects);
        if (data.projects && data.projects != undefined) {
          localStorage.setItem("projectList", JSON.stringify(data.projects));
        }
      }
    } else {
      message.error({
        content: result.message,
        class: 'c_message_big',
        style: {
          marginTop: '20vh',
        },
        duration: 3,
      });
    }
  } else {
    console.log('error', error)
    // console.log('error.value', error.value)
    // 错误弹窗
    const errorContent = error == null ? "404 request failed！" : error.value && error.value!.toString() || error.toString();
    message.error({
      content: errorContent,
      class: 'c_message_big',
      style: {
        marginTop: '20vh',
      },
      duration: 3,
    });
    console.error(errorContent);
  }
} catch (err) {
  console.error(err);
}

// 跳转 pay详情页
const goToPay = async (id: number) => {
  await navigateTo(`/${locale.value}/pay/${id}`);
  // 埋点: 点击游戏卡片
  YKTrack.track('click', {
    params: {
      id: '702',
    }
  })
};

onMounted(() => {
  // authStore.hideLoading();
  // 埋点: 游戏列表加载结果 0:成功 1:失败
  YKTrack.track('view', {
    params: {
      id: '701',
      value1: 0,
    }
  })
});

// onServerPrefetch(async () => {
// 	// 组件作为初始请求的一部分被渲染
// 	// 在服务器上预抓取数据，因为它比在客户端上更快。
// 	const { data: getJsonResult, pending, error, refresh } = await useFetch('/useApi/v1/projectList');
// 	let result = getJsonResult.value;
// 	if (result.code == 200) {
// 		const { data } = result;
// 		projectList.value = data.projects;
// 	} else {

// 	}
// })
// const getList = async () => {
// 	const aaa = await $fetch("/useApi/v1/projectList");
// 	console.log(aaa);
// };
</script>
<style lang="scss" scoped>
.content {
  position: relative;
  z-index: 1;
}

.banner {
  width: 100%;
  max-height: 20vw;
  overflow: hidden;
  display: flex;
  align-items: center;

  .img {
    width: 100%;
    vertical-align: top;
  }
}

.goods_wrap {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  margin-bottom: 50px;

  &::after {
    content: "";
    width: 25%;
  }

  .goods_item {
    width: 25%;
    padding: 0 10px;

    .goods_main {
      margin-top: 40px;
      width: 100%;
      height: 0;
      padding-top: 76%;
      border-radius: 16px;
      overflow: hidden;
      background: #fff;
      position: relative;
      cursor: pointer;
      transition: all 0.5s ease 0s;

      &:hover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.43);
        transform: translate(0, -5px);
      }
    }
  }
}

.goods_main_box {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;

  .top {
    height: 78%;
    position: relative;

    .txt {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 9;
      background: rgba(0, 0, 0, 0.43);
      text-align: center;
      line-height: 50px;
      font-size: 20px;
      color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .bottom {
    height: 22%;
    display: flex;
    align-items: center;
    justify-content: center;

    .pay_btn {
      text-decoration: none;
      --pay_btn_height: 36px;
      width: 50%;
      height: var(--pay_btn_height);
      line-height: var(--pay_btn_height);
      border-radius: calc(var(--pay_btn_height) / 2);
      vertical-align: middle;
      text-align: center;
      font-size: 18px;
      color: #fff;
      background: var(--base_color);
    }
  }
}

@media screen and (max-width: 1280px) {
  .goods_wrap {

    &::after {
      width: 33.33%;
    }

    .goods_item {
      width: 33.33%;
    }
  }

  .goods_main_box {
    .top {
      .txt {
        height: 50px;
        line-height: 50px;
        font-size: 18px;
      }
    }

    .bottom {
      .pay_btn {
        font-size: 18px;
      }
    }
  }
}

@media screen and (max-width: 980px) {
  .goods_wrap {
    margin-top: 0;
    margin-bottom: 40px;
  }

  .goods_wrap {
    &::after {
      width: 50%;
    }

    .goods_item {
      width: 50%;
      padding: 0 20px;

      .goods_main {
        margin-top: 40px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .banner {
    max-height: none;
  }
}

@media screen and (max-width: 640px) {

  .goods_wrap {
    margin-bottom: 30px;

    &::after {
      width: 100%;
    }

    .goods_item {
      width: 100%;

      .goods_main {
        margin-top: 20px;
      }
    }
  }

  .goods_main_box {
    .bottom {
      .pay_btn {
        width: 70%;
        --pay_btn_height: 40px;
        font-size: 16px;
      }
    }
  }
}
</style>
