<template>
  <div>
    <!-- 简介 -->
    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step1'">
      <div class="intro-t">{{ $t("account.security.deregister.popup.title") }}</div>
      <div class="intro-head">{{ $t("account.security.deregister.s1.title") }}</div>
      <div class="intro">
        <p class="">{{ $t("account.security.deregister.s1.p1", {XXX: userInfo.username || userInfo.nick_name || userInfo.user_id || userInfo.plaintext_username }) }}</p>
        <p class="">{{ $t("account.security.deregister.s1.p2") }}</p>
        <p class="intro-p-red">{{ $t("account.security.deregister.s1.p3") }}</p>
      </div>
      <div class="intro-foot">{{ $t("account.security.deregister.s1.f1") }}</div>
      <div class="ac_pop_btn_line">
        <a-button class="login_btn"
                  type="primary"
                  @click="clickStep1">{{ $t("account.security.deregister.nextStep") }}
        </a-button>
      </div>
    </div>

    <!-- 选择验证方式 -->
    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step2'">
      <div class="h3">{{ $t("account.securityVerification.title") }}</div>
      <div class="ac_profile_edit_form">
        <p class="ac_profile_edit_desc">
          {{ $t("account.securityVerification.methods") }}
        </p>
        <a-select class="deregister_check_method_select" ref="select"
                  :disabled="checkMethodChoiceList.length <= 1"
                  v-model:value="checkMethodChoiceValue" @change="checkMethodChoice"
                  :options="checkMethodChoiceList">
        </a-select>
        <p class="ac_profile_edit_desc">
          {{
            $t(
                checkMethodChoiceValue.value == "phone"
                    ? "account.securityVerification.phoneInstructions"
                    : "account.securityVerification.emailInstructions"
            )
          }}
        </p>
        <!-- 邮箱验证码 -->
        <div v-if="checkMethodChoiceValue.value == 'email'">
          <div class="ac_profile_edit_desc">{{ bindEmail }}</div>
          <div class="deregister_input_box">
            <a-input v-model:value="oEmailCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxLength="6">
            </a-input>
            <a-button class="captcha_ant_btn" :loading="captchaLoading"
                      :disabled="captchaLoading ||!!countDownEmailCaptchaBtnText" @click="clickCaptcha">{{
                countDownEmailCaptchaBtnText
                    ? `${$t("account.common.sent")}（${countDownEmailCaptchaBtnText}s）`
                    : $t("account.common.get")
              }}
            </a-button>
          </div>
        </div>
        <!-- 手机验证码 -->
        <div v-if="checkMethodChoiceValue.value == 'phone'">
          <div class="ac_profile_edit_desc">{{ bindPhone }}</div>
          <div class="deregister_input_box">
            <a-input v-model:value="oPhoneCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxLength="6">
            </a-input>
            <a-button class="captcha_ant_btn" :loading="captchaLoading"
                      :disabled="captchaLoading || !!countDownPhoneCaptchaBtnText" @click="clickCaptcha">{{
                countDownPhoneCaptchaBtnText
                    ? `${$t("account.common.sent")}（${countDownPhoneCaptchaBtnText}s）`
                    : $t("account.common.get")
              }}
            </a-button>
          </div>
        </div>

        <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
          {{ oErrorHint }}
        </div>
      </div>
      <div class="deregister_btn">
        <a-button class="login_btn"
                  type="primary" @click="checkCaptcha">{{ $t("account.common.nextStep") }}
        </a-button>
      </div>
    </div>

    <!-- 注销须知 -->
    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step3'">
      <div class="intro-t">{{ $t("account.security.deregister") }}</div>
      <div class="intro-head">{{ $t("account.security.deregister.s3.t1") }}</div>
      <div class="intro intro-pre">
        <p style="margin-bottom: 0">{{ $t("account.security.deregister.s3.p1") }}</p>
        <p class="" v-html="$t('account.security.deregister.s3.p2')"></p>
      </div>
      <div class="intro-foot-check">
        <a-checkbox v-model:checked="isStep3Checkbox">{{ $t("account.security.deregister.s3.check") }}</a-checkbox>
      </div>
      <div class="ac_pop_btn_line">
        <a-button class="login_btn login_btn_disabled"
                  type="primary"
                  :disabled="timerStep3Value!==0"
                  @click="clickStep3">{{
            timerStep3Value !== 0 ?
                $t("account.security.deregister.s3.btn1", {XXX: timerStep3Value}) :
                $t("account.security.deregister.nextStep")
          }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepCancel">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
      </div>
    </div>

    <!-- 注销须知 2 -->
    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step4'">
      <div class="intro-t">{{ $t("account.security.deregister") }}</div>
      <div class="intro-head">{{ $t("account.security.deregister.s4.t1") }}</div>
      <div class="intro intro-pre">
        <p class="">{{ $t("account.security.deregister.s4.p1") }}</p>
      </div>
      <div class="intro-foot-check">
        <a-checkbox v-model:checked="isStep4Checkbox">{{ $t("account.security.deregister.s4.check") }}</a-checkbox>
      </div>
      <div class="ac_pop_btn_line">
        <a-button class="login_btn login_btn_disabled"
                  type="primary"
                  :disabled="timerStep4Value!==0"
                  @click="clickStep4">{{
            timerStep4Value !== 0 ?
                $t("account.security.deregister.s3.btn1", {XXX: timerStep4Value}) :
                $t("account.security.deregister.nextStep")
          }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepCancel">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
      </div>
    </div>

    <!-- 选择注销原因 -->
    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step5'">
      <div class="intro-t">{{ $t("account.security.deregister") }}</div>
      <div class="intro-select-head">
        {{ $t("account.security.deregister.s5.t1") }}
        <span>{{ $t("account.security.deregister.s5.t2", {XXX: userInfo.username || userInfo.nick_name || userInfo.user_id || userInfo.plaintext_username }) }}</span>
      </div>
      <div class="intro-select-t">{{ $t("account.security.deregister.s5.t3") }}</div>
      <div class="intro-select">
        <p class="" v-for="item in reasonOptions" :key="item.value">
          {{ item.label }}
          <a-checkbox tabindex="3" :checked="reasonValue.includes(item.value)"
                      @change="changeReason(item)"></a-checkbox>
        </p>
      </div>
      <div class="intro-select-input">
        <a-textarea
            v-model:value="reasonOtherValue"
            :placeholder="$t('account.security.deregister.s5.p7')"
            :auto-size="{ minRows: 3, maxRows: 5 }"
            :disabled="!reasonValue.includes(6)"
            :maxLength="30"
        />
      </div>
      <div class="ac_pop_btn_line">
        <a-button class="login_btn"
                  type="primary"
                  @click="clickStep5">{{ $t("account.security.deregister.s5.b1") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepCancel">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
      </div>
    </div>

    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step6'">
      <div class="intro-t">{{ $t("account.security.deregister") }}</div>
      <div class="intro-head">{{ $t("account.security.deregister.s6.t1") }}</div>
      <div class="intro-no-border">
        <p class="">{{ $t("account.security.deregister.s6.p1") }}</p>
        <p class="intro-p-red ">{{ $t("account.security.deregister.s6.p2") }}</p>
        <p class="">{{ $t("account.security.deregister.s6.p3") }}</p>
      </div>
      <div class="step6-gap"></div>
      <div class="ac_pop_btn_line">
        <a-button class="login_btn"
                  type="primary"
                  :loading="submitLoading" :disabled="submitLoading"
                  @click="clickStep6">{{ $t("account.security.deregister.s6.b1") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepCancel">{{ $t("account.security.deregister.s6.b2") }}
        </a-button>
      </div>
    </div>

    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step7'">
      <div class="intro-t">{{ $t("account.security.deregister") }}</div>
      <div class="intro-step7-head">
        <div class="intro-step7-img">
          <img src="~/assets/images/account/deregister_4.png">
        </div>
        {{ $t("account.security.deregister.s7.t1") }}
        <span>{{ $t("account.security.deregister.s7.t1-1") }}</span>
      </div>
      <div class="intro-step7">
        <p class=""
           v-html="$t('account.security.deregister.s5.t2', {XXX: `<span>${userInfo.username || userInfo.nick_name || userInfo.user_id || userInfo.plaintext_username || result.snda_id}</span>`})"></p>
        <p class=""
           v-html="$t('account.security.deregister.s7.p2', {XXX: `<span>${result.logout_apply_time}</span>`})"></p>
        <p class="">{{ $t("account.security.deregister.s7.p3") }}</p>
      </div>
      <div class="intro_step7_d2">{{ $t("account.security.deregister.s7.d2") }}</div>
      <div class="intro_step7_d3" v-if="!isSdkEnv">{{ $t("account.security.deregister.s7.d3") }}</div>
      <div class="intro_step7_d3 p-red" v-if="isSdkEnv">{{ $t("pages.security.deregister.s7.app") }}</div>
      <div class="ac_pop_btn_line" v-if="!isSdkEnv">
        <a-button class="login_btn"
                  type="primary"
                  @click="clickStep7">{{ $t("account.security.deregister.s7.b1") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  :loading="submitLoading" :disabled="submitLoading"
                  @click="clickStep7_2">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
      </div>
    </div>

    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step8'">
      <div class="h3">
        <div class="step8_logo c_logo_blue"></div>
      </div>
      <div class="intro-step7-head intro-step7-head-step8">
        <div class="intro-step7-img">
          <img src="~/assets/images/account/deregister_1.png">
        </div>
        {{ $t("account.security.deregister.s8.t1") }}
        <span style="padding-top: 20px;padding-bottom: 7px">{{ $t("account.security.deregister.s8.t2") }}</span>
      </div>
      <div class="intro-step8 intro-step8-bg">
        <p class="" style="margin-bottom: 0"
           v-html="$t('account.security.deregister.s5.t2', {XXX: `<span class='p-red'>${newStepLoginData.username}</span>`})"></p>
        <p class=""
           v-html="$t('account.security.deregister.s7.p2', {XXX: `<span class='p-red'>${newStepLoginData.logout_apply_time}</span>`})"></p>
        <p class="p-red" style="margin-top: 6px;max-height: 20vh;overflow: auto;">{{
            newStepLoginData.reject_reason
          }}</p>
        <p class="" style="margin-top: 6px">{{ $t("account.security.deregister.s8.p4") }}</p>
      </div>
      <div class="ac_pop_btn_line" style="margin-top: 33px;padding: 0">
        <a-button class="login_btn"
                  type="primary"
                  :loading="submitLoading" :disabled="submitLoading"
                  @click="cancelDeregister">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepChangeAccount">{{ $t("account.security.deregister.s8.b1") }}
        </a-button>
      </div>
    </div>

    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step9'">
      <div class="h3">
        <div class="step8_logo c_logo_blue"></div>
      </div>
      <div class="intro-step7-head intro-step7-head-step8">
        <div class="intro-step7-img">
          <img src="~/assets/images/account/deregister_2.png">
        </div>
        {{ $t("account.security.deregister.s9.t1") }}
      </div>
      <div class="intro-step8" style="padding: 0 20px;">
        <p style="margin-bottom: 0"
           v-html="$t('account.security.deregister.s5.t2', {XXX: `<span class='p-red'>${newStepLoginData.username}</span>`})"></p>
        <p class=""
           v-html="$t('account.security.deregister.s7.p2', {XXX: `<span class='p-red'>${newStepLoginData.logout_apply_time}</span>`})"></p>
        <p class="">{{ $t("account.security.deregister.s9.p3") }}</p>
        <p class="">{{ $t("account.security.deregister.s9.p4") }}</p>
        <p class="">{{ $t("account.security.deregister.s8.p4") }}</p>
      </div>
      <div class="ac_pop_btn_line" style="margin-top: 33px">
        <a-button class="login_btn"
                  type="primary"
                  @click="cancelDeregister">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepChangeAccount">{{ $t("account.security.deregister.s8.b1") }}
        </a-button>
      </div>
    </div>

    <div class="ac_c_mb20 ac_c_r20 deregister-module ac_common_box noBoxShadow" v-if="newStep == 'step10'">
      <div class="h3">
        <div class="step8_logo c_logo_blue"></div>
      </div>
      <div class="intro-step7-head intro-step7-head-step8">
        <div class="intro-step7-img">
          <img src="~/assets/images/account/deregister_3.png">
        </div>
        {{ $t("account.security.deregister.s10.t1") }}
        <span style="color: #111111; font-size: 24px;">{{ $t("account.security.deregister.s10.t2") }}</span>
      </div>
      <div class="intro-step7 intro-step8-bg" style="margin: 6px 0 0">
        <p class=""
           v-html="$t('account.security.deregister.s5.t2', {XXX: `<span>${newStepLoginData.username}</span>`})"></p>
        <p class=""
           v-html="$t('account.security.deregister.s7.p2', {XXX: `<span>${newStepLoginData.logout_apply_time}</span>`})"></p>
        <p class="" style="margin-top: 19px">{{ $t("account.security.deregister.s7.p3") }}</p>
        <p class="" style="margin-top: 19px">{{ $t("account.security.deregister.s8.p4") }}</p>
      </div>
      <div class="ac_pop_btn_line" style="margin-top: 33px;padding: 0">
        <a-button class="login_btn"
                  type="primary"
                  @click="cancelDeregister">{{ $t("account.security.deregister.s3.btn2") }}
        </a-button>
        <div style="width: 20px;"></div>
        <a-button class="login_btn login_btn_cancel"
                  type="default"
                  @click="clickStepChangeAccount">{{ $t("account.security.deregister.s8.b1") }}
        </a-button>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
// 引入验证码
import {useYcCaptcha} from "~/utils/ycCaptcha";


const {initYcCaptcha} = useYcCaptcha();
// 登录态管理
import {useAuthStore} from "~/stores/auth";
import {useAccountStore} from "~/stores/account";
import {noVerifyStep1, noVerifyStep2, cancelLogout, verifyStep1, verifyStep2} from "~/api/deregister";

const route = useRoute();
const {$jsbridge} = useNuxtApp() as any;
const authStore = useAuthStore();
const accountStore = useAccountStore();
const {locale, t} = useI18n();
// 用户信息
const userInfo: any = computed(() => accountStore.userInfo);
const {$validator} = useNuxtApp();
const $v: any = $validator;
const $props = defineProps({
  newStepLoginData: {
    type: Object,
    default: {},
  },
});

// 步骤的id
// const step = ref<string>("step1");
const isSdkEnv = ref<boolean>(route.query.from !== undefined && route.query.from === 'app');
const loginData = ref<any>({});
const newStep = ref<string>("step1"); // intro step1 step8 申请被驳回 step9 申请审核中 step10 注销中
const isVerify = ref<boolean>(false);
const isStep3Checkbox = ref<boolean>(false);
const isStep4Checkbox = ref<boolean>(false);
const timerStep3Value = ref<number>(10);
const timerStep4Value = ref<number>(10);
const result = ref<any>({});
const reasonOptions = ref<any>([
  {label: t('account.security.deregister.s5.p1'), value: 1},
  {label: t('account.security.deregister.s5.p2'), value: 2},
  {label: t('account.security.deregister.s5.p3'), value: 3},
  {label: t('account.security.deregister.s5.p4'), value: 4},
  {label: t('account.security.deregister.s5.p5'), value: 5},
  {label: t('account.security.deregister.s5.p6'), value: 6},
]);
const reasonValue = ref<any>([]);
const reasonOtherValue = ref<string>("");
const changeReason = (item: any) => {
  const arr = JSON.parse(JSON.stringify(reasonValue.value));
  const idx = arr.findIndex((element: any) => element === item.value);
  if (idx > -1) {
    arr.splice(idx, 1)
    reasonValue.value = arr
  } else {
    if (reasonValue.value.length < 3) {
      reasonValue.value.push(item.value)
    }
  }
};
const submitLoading = ref<boolean>(false);
const captchaLoading = ref<boolean>(false);
const bindEmail = ref<string>("");
const bindPhone = ref<string>("");
const oEmailCaptcha = ref<string>("");
const oPhoneCaptcha = ref<string>("");
const oErrorHint = ref<string>(""); // 错误提示
const oErrorHintSuccess = ref<boolean>(false); // 成功提示
const countDownEmailCaptchaBtnText = ref<number | boolean>(false);
const countDownPhoneCaptchaBtnText = ref<number | boolean>(false);

// 选择验证方式
const checkMethodChoiceValue = ref<{ label: string; value: string }>({
  label: "",
  value: "",
});
const checkMethodChoiceList = ref<any[]>([]);

const success = (msg: string) => {
  message.destroy()
  message.success({
    content: msg,
    class: 'c_message_big',
    style: {
      marginTop: '20vh',
    },
    duration: 3,
  });
};
const error = (msg: string) => {
  message.destroy()
  message.error({
    content: msg,
    class: 'c_message_big',
    style: {
      marginTop: '20vh',
    },
    duration: 3,
  });
};

const step3Timer = () => {
  const sn = setInterval(() => {
    timerStep3Value.value--
    if (timerStep3Value.value === 0) {
      clearInterval(sn);
    }
  }, 1000)
};
const step4Timer = () => {
  const sn = setInterval(() => {
    timerStep4Value.value--
    if (timerStep4Value.value === 0) {
      clearInterval(sn);
    }
  }, 1000)
};
// 查询账号的安全验证方式
const querySecurityVerifyMethod = async () => {
  const userData = userInfo.value;
  // userData.is_bind_phone = true;
  // userData.is_bind_email = true;
  // userData.email = '<EMAIL>';
  // userData.phone = '***********';
  if (userData.is_bind_email) {
    bindEmail.value = userData.email;
    checkMethodChoiceList.value.push({
      label: t("account.securityVerification.emailVerification"),
      value: "email",
    });
  }
  if (userData.is_bind_phone) {
    bindPhone.value = userData.phone;
    checkMethodChoiceList.value.push({
      label: t("account.securityVerification.phoneVerification"),
      value: "phone",
    });
  }
  if (checkMethodChoiceList.value.length > 0) {
    checkMethodChoiceValue.value = checkMethodChoiceList.value[0];
    isVerify.value = true;
  } else {
    // 没有任何安全验证方式的用户 跳过验证，直接拉注销须知弹窗
  }
};
const checkMethodChoice = (value: any) => {
  checkMethodChoiceValue.value = checkMethodChoiceList.value.find((item) => item.value == value);
};
const timerCaptcha = async () => {
  oErrorHint.value = t("account.verification.codeSent");
  oErrorHintSuccess.value = true;
  // 开启倒计时
  if (checkMethodChoiceValue.value.value == "email") {
    countDownEmailCaptchaBtnText.value = 60;
    const timer = setInterval(() => {
      (countDownEmailCaptchaBtnText.value as number)--;
      if (countDownEmailCaptchaBtnText.value as number <= 0) {
        clearInterval(timer);
        countDownEmailCaptchaBtnText.value = false;
      }
    }, 1000);
  } else if (checkMethodChoiceValue.value.value == "phone") {
    countDownPhoneCaptchaBtnText.value = 60;
    const timer = setInterval(() => {
      (countDownPhoneCaptchaBtnText.value as number)--;
      if (countDownPhoneCaptchaBtnText.value as number <= 0) {
        clearInterval(timer);
        countDownPhoneCaptchaBtnText.value = false;
      }
    }, 1000);
  }
};

const getCaptcha = async (token: string = '', captcha_verification: string = '') => {
  // 判断邮箱验证码是否在倒计时
  if (
      checkMethodChoiceValue.value.value == "email" &&
      !!countDownEmailCaptchaBtnText.value
  ) {
    return;
  }
  // 判断手机验证码是否在倒计时
  if (
      checkMethodChoiceValue.value.value == "phone" &&
      !!countDownPhoneCaptchaBtnText.value
  ) {
    return;
  }
  try {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    captchaLoading.value = true;
    const authType = checkMethodChoiceValue.value.value == "phone" ? 3 : 4;
    let res:any;
    if(isSdkEnv.value){
      res = await verifyStep1(authType, token, captcha_verification, true);
    }else{
      res = await verifyStep1(authType, token, captcha_verification);
    }
    if (res.code == 200 || res.code == 0) {
      // success('发送成功');
      timerCaptcha();  // 验证码开始计时
    } else {
      oErrorHint.value = res.message;
      if (res.code == 2044) {
        initYcCaptcha((token: string = '', captcha_verification: string = '') => {
          console.log(2044, token, captcha_verification);
          getCaptcha(token, captcha_verification)
        });
      }
    }
    captchaLoading.value = false;
  } catch (error) {
    captchaLoading.value = false;
    console.log(error);
  }
};
const checkCaptcha = async () => {
  oErrorHint.value = "";
  oErrorHintSuccess.value = false;
  if (
      checkMethodChoiceValue.value.value == "email" &&
      oEmailCaptcha.value.length != 6
  ) {
    oErrorHint.value = t("account.verification.codeInvalid");
    return;
  }
  if (
      checkMethodChoiceValue.value.value == "phone" &&
      oPhoneCaptcha.value.length != 6
  ) {
    oErrorHint.value = t("account.verification.codeInvalid");
    return;
  }
  try {
    submitLoading.value = true;
    const authType = checkMethodChoiceValue.value.value == "phone" ? 3 : 4;
    const code = checkMethodChoiceValue.value.value == "email" ? oEmailCaptcha.value : oPhoneCaptcha.value;
    let res:any;
    if(isSdkEnv.value){
      res = await verifyStep2(authType, code, true);
    }else{
      res = await verifyStep2(authType, code);
    }

    submitLoading.value = false;
    if (res.code == 200 || res.code == 0) {
      newStep.value = 'step3';
      step3Timer()
    } else {
      oErrorHint.value = res.message;
    }
  } catch (error) {
    submitLoading.value = false;
    console.log(error);
  }
};
const clickCaptcha = () => {
  getCaptcha()
};

const $emit = defineEmits(["close", 'changeAccount', 'closeModelClose']);
const clickStep1 = async () => {
  if (isVerify.value) {
    newStep.value = 'step2';
  } else {
    newStep.value = 'step3';
    step3Timer()
  }
};
const clickStep3 = async () => {
  if (isStep3Checkbox.value && timerStep3Value.value <= 0) {
    newStep.value = 'step4';
    step4Timer()
  } else {
    error(t("account.security.deregister.s3.t1"))
  }
};
const clickStep4 = async () => {
  if (isStep4Checkbox.value && timerStep4Value.value <= 0) {
    newStep.value = 'step5';
  } else {
    error(t("account.security.deregister.s4.t1"))
  }
};
const clickStep5 = async () => {
  if (reasonValue.value.length > 0) {
    newStep.value = 'step6';
  } else {
    error(t('account.security.deregister.msg1'))
  }
};
const clickStep6 = async () => {
  submitLoading.value = true;
  let r1:any;
  if(isSdkEnv.value){
    r1 = await noVerifyStep1(true)
  }else{
    r1 = await noVerifyStep1()
  }

  console.log(r1)
  if (r1.code == 200 || r1.code == 0) {
    let reason = '';
    for (const v of reasonValue.value) {
      if (v !== 6) {
        const af = reasonOptions.value.filter((e: any) => e.value === v)
        if (af.length !== 0) {
          reason += af[0].label + ';'
        } else {
          reason += v + ';'
        }
      } else {
        reason += reasonOtherValue.value + ';'
      }
    }
    let r2:any;
    if(isSdkEnv.value){
      r2 = await noVerifyStep2(reason, true)

    }else{
      r2 = await noVerifyStep2(reason)
    }
    console.log(r2)
    if (r2.code == 200 || r2.code == 0) {
      submitLoading.value = false;
      result.value = r2.data
      newStep.value = 'step7';
      $emit("closeModelClose");
      // 原生sdk需要的注销功能
      if (isSdkEnv.value) {
        $jsbridge.logout()
      }
    } else {
      submitLoading.value = false;
      error(r2.message)
    }
  } else {
    submitLoading.value = false;
    error(r1.message)
  }
};
const clickStep7 = async () => {
  // 关闭页面
  clickStepCancel()
  navigateTo(`/${locale.value}/account/login`);
};
const clickStep7_2 = async () => {
  submitLoading.value = true;
  const res:any = await cancelLogout();
  if (res.code == 200 || res.code == 0) {
    submitLoading.value = false;
    success(t('account.security.deregister.msg2'))

    authStore.login(userInfo.value.username, res.data.token);
    clickStepCancel()
  } else {
    submitLoading.value = false;
    error(res.message)
  }
};
const clickStepCancel = async () => {
  // 原生sdk需要的
  if (isSdkEnv.value) {
    $jsbridge.close()
  } else {
    $emit("close");
  }
};
const clickStepChangeAccount = async () => {
  // tempPaymentLogin 是老登录未统一的兼容方式 以后可以删除此逻辑 有两处 第1处
  if (route.query.from !== undefined && route.query.from === 'tempPaymentLogin') {
    window.history.back();
  } else {
    $emit("changeAccount");
    loginData.value = {}
  }
};
const cancelDeregister = async () => {
  submitLoading.value = true;
  const res:any = await cancelLogout(loginData.value.token);
  if (res.code == 200 || res.code == 0) {
    submitLoading.value = false;

    success(t('account.security.deregister.msg2'))
    authStore.login(loginData.value.username, res.data.token);
    // tempPaymentLogin 是老登录未统一的兼容方式 以后可以删除此逻辑 有两处 第2处
    if (route.query.from !== undefined && route.query.from === 'tempPaymentLogin') {
      window.history.back();
    } else {
      navigateTo(`/${locale.value}/account/profile`);
    }
  } else {
    submitLoading.value = false;
    error(res.message)
  }
};
const checkLoginResult = () => {
  loginData.value = $props.newStepLoginData
  if (Object.keys(loginData.value).length !== 0) {
    // 注销状态：1待审核(注销中) 2延期未审核 3驳回
    // loginData.value.logout_status = 2
    if (loginData.value.logout_status === 1 || loginData.value.logout_status === 4) {
      newStep.value = 'step10';
    } else if (loginData.value.logout_status === 2) {
      newStep.value = 'step9';
    } else if (loginData.value.logout_status === 3) {
      newStep.value = 'step8';
    }
  }
}
watch(() => $props.newStepLoginData, (newValue) => {
  checkLoginResult()
});
onBeforeMount(() => {
  checkLoginResult()
});
onMounted(() => {
  querySecurityVerifyMethod()
});
</script>
<style lang="scss">
@import url(~/assets/styles/account/checkEmailAndPhoneAll.scss);

.r_password_step1 {
  .input_wrap .input_box .input {
    margin-left: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
@import url("~/assets/styles/account/login.scss");
@import url("~/assets/styles/account/common.scss");
@import url("~/assets/styles/account/checkEmailAndPhone.scss");

.password_hint {
  padding-top: 40px;
  color: #5f5f5f;
  text-align: center;
  font-size: 14px;

  a {
    color: #466df6;
  }
}

.c_logo_blue {
  margin: 0 auto;
}

.intro-t {
  color: #414141;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 16px;
  line-height: 56px;
}

.intro {
  background: #F4F6FC;
  margin: 0 48px;
  padding: 12px 18px 18px;
  font-size: 14px;
  border-radius: 12px;

  > p:last-child {
    margin-bottom: 0;
  }
}

.intro-pre {
  white-space: pre-wrap; /* pre标签功能 */
  word-wrap: break-word;
  height: 30vh;
  overflow: auto;
}

.intro-no-border {
  margin: 0 48px;
  padding: 12px 18px 18px;
  font-size: 14px;
  border-radius: 12px;
  color: #5F5F5F;

  > p:last-child {
    margin-bottom: 0;
  }

  > p:nth-child(2) {
    color: #FF442B;
  }
}

.intro-head {
  text-align: center;
  font-size: 18px;
  color: #111111;
  font-weight: 400;
  padding: 37px 48px 22px;
  border-top: 1px solid #ECECF0;
}

.intro-foot {
  text-align: center;
  color: #5F5F5F;
  padding: 13px 0 28px;
  margin: 0 48px;
}

.intro-foot-check {
  color: #5F5F5F;
  padding: 13px 0 28px;
  margin: 0 48px;
}

.intro_step7_d2 {
  color: #FF442B;
  margin: 0 48px;
  padding-top: 4px;
}

.intro_step7_d3 {
  color: #5F5F5F;
  padding: 16px 0 32px;
  margin: 0 48px;
}

.login_btn_cancel {
  background: transparent;
  border: 1px solid #ADADAD;
  color: #ADADAD;

  &:hover {
    background: transparent;
    color: #ADADAD;
  }
}

.intro-select-head {
  font-size: 20px;
  color: #111111;
  font-weight: 400;
  padding: 37px 48px 36px;
  border-top: 1px solid #ECECF0;
  display: flex;
  justify-content: space-between;

  > span {
    font-size: 12px;
    font-weight: normal;
  }
}

.intro-select {
  margin: 0 48px;

  > p {
    display: flex;
    justify-content: space-between;
  }
}

.intro-select-t {
  font-size: 12px;
  color: #5F5F5F;
  margin: 0 48px 14px;
}

.intro-select-input {
  font-size: 12px;
  margin: 0 48px 29px;

  textarea {
    border-radius: 4px;
  }
}

.login_btn_disabled[disabled] {
  color: #FFFFFF;
  background: #8D8D8D;
  text-shadow: none;
  box-shadow: none;
}

.intro-step7-img {
  display: flex;
  justify-content: center;
  padding-bottom: 24px;

  > img {
    width: 74px;
    height: 74px;
  }
}

.intro-step7-head {
  text-align: center;
  font-size: 20px;
  color: #111111;
  font-weight: 400;
  padding: 31px 48px 14px;
  border-top: 1px solid #ECECF0;

  > span {
    display: flex;
    justify-content: center;
    font-size: 16px;
    color: #5F5F5F;
    padding-top: 6px;
  }
}

.intro-step7 {
  background: #F4F6FC;
  margin: 0 48px;
  padding: 12px 18px 18px;
  font-size: 14px;
  border-radius: 12px;

  > p {
    margin-bottom: 0;

    :deep(span) {
      color: #FF442B;
    }
  }
}

:deep(.intro-p-red) {
  color: #FF442B;
}

:deep(.p-red) {
  color: #FF442B;
}

.deregister-module {
  background: #fff;
  width: 100%;
}

.deregister_check_method_select {
  width: 100%;
  height: 48px;
  margin-bottom: 20px;

  :deep(.ant-select-selector) {
    width: 100%;
    height: 100%;
    border-radius: 12px;
  }

  :deep(.ant-select-selection-item) {
    display: flex;
    align-items: center;
  }
}

.deregister_input_box {
  display: flex;
  position: relative;
  height: 48px;

  > input {
    border-radius: 12px;
  }

  > button {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
  }
}

.deregister_btn {
  padding: 11px 32px 32px;
}

.step8_logo {
  margin-bottom: 25px;
}

.intro-step7-head-step8 {
  border: 0;
  font-size: 24px;
  padding-left: 0;
  padding-right: 0;
  color: #111111;
}

.p-red {
  color: #FF442B;
}

.intro-step8 {
  color: #5F5F5F;

  > p {
    margin-bottom: 10px;
  }
}

.intro-step8-bg {
  background: #F4F6FC;
  padding: 10px 18px;
  border-radius: 12px;
  color: #5F5F5F;
}

.step6-gap {
  height: 180px;
}

@media screen and (max-width: 768px) {
  .intro-head,
  .intro-select-head,
  .intro-step7-head {
    line-height: 1.2;
    text-align: left;
    padding-left: 20px;
    padding-right: 20px;
  }
  .intro-select-head {
    display: flex;
    flex-flow: column;
  }
  .intro,
  .intro-foot,
  .intro-foot-check,
  .intro-select-t,
  .intro-select,
  .intro-select-input,
  .intro-no-border,
  .intro-step7,
  .intro_step7_d2,
  .intro_step7_d3 {
    text-align: left;
    margin-left: 20px;
    margin-right: 20px;
  }
  .intro-no-border {
    padding-left: 0px;
    padding-right: 0px;
  }
  .step6-gap {
    height: 0px;
  }

  .intro-select-head {
    > span {
      margin-top: 10px;
    }
  }
  .intro-step7-head > span {
    justify-content: flex-start;
  }
}
</style>
