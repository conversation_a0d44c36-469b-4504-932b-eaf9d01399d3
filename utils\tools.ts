export const isMobile = ()=> {
    // 使用 UA 字符串判断
    const ua = navigator.userAgent;
    return (
        ua.match(/Android/i) ||
        ua.match(/webOS/i) ||
        ua.match(/iPhone/i) ||
        ua.match(/iPad/i) ||
        ua.match(/iPod/i) ||
        ua.match(/BlackBerry/i) ||
        ua.match(/harmonyos/i) ||
        ua.match(/Windows Phone/i)
    );
}

export const isAppleDevice = () => {
    const ua = navigator.userAgent.toLowerCase();
    return /iPad|iPhone|iPod|Macintosh/ig.test(ua);
}
// 显示倒计时 按照秒来基数
export const formatTimeSecond = (second: number) => {
    // console.log('Formatting time for:', second);  // 检查函数是否被调用
    const pad = (num: number) => (num < 10 ? '0' + num : num);
    const hours = Math.floor(second / 3600);
    const minutes = Math.floor((second % 3600) / 60);
    const seconds = second % 60;
    return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
};


// 用来区分在 account 和 payment 域名下跳转
export const goToPageSwitch = (pathVal: string, host: string, locale: any) => {
    // 判断是否在 account 域名下
    const accountHostName = process.env.VITE_ACCOUNT_HOST_NAME;
    const paymentHostName = process.env.VITE_PAYMENT_HOST_NAME;
    const wwwHostName = process.env.VITE_WWW_HOST_NAME;
    if(host == 'account') {
        if(accountHostName && window.location.hostname.indexOf(accountHostName) == -1) {
            window.location.href = `https://${accountHostName}/${locale}/${pathVal}`;
        }else {
            navigateTo(`/${locale}/${pathVal}`)
        }
    }
    if(host == 'payment') {
        if(paymentHostName && window.location.hostname.indexOf(paymentHostName) == -1) {
            window.location.href = `https://${paymentHostName}/${locale}/${pathVal}`;
        }else {
            navigateTo(`/${locale}/${pathVal}`)
        }
    }
    if(host == 'www') {
        if(wwwHostName && window.location.hostname.indexOf(wwwHostName) == -1) {
            window.location.href = `https://${wwwHostName}/${locale}/${pathVal}`;
        }else {
            navigateTo(`/${locale}/${pathVal}`)
        }
    }
}

// 获取图片路径
export const getImageUrl = (path: string) => {
    if (!path) return '';

    try {
        // 使用 import.meta.glob 动态导入资源
        const images:any = import.meta.glob('~/assets/images/**/*', { eager: true });
        const imagePath = path.replace('~', '');
        
        // 检查路径是否在导入的资源中
        if (images[imagePath]) {
            // console.log("============",images[imagePath].default)
            return images[imagePath].default;
        }

        // 如果是完整的 URL，直接返回
        if (path.startsWith('http') || path.startsWith('https')) {
            return path;
        }
        // 使用动态导入来获取资源
        return new URL(`${path}`, import.meta.url).href;
    } catch (error) {
        console.error('Error getting image URL:', error);
        return path;
    }
}
