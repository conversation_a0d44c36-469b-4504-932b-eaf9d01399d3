
// 登录态管理器
import { defineStore } from "pinia";

/**
 * 定义一个名为`account`的Pinia store，用于管理用户支付状态。
 */
export const useAccountStore = defineStore("account", {
    // 初始化状态
    state: () => ({
        userInfo: {},
        actionType: "",
    }),
    // 登录操作，更新状态并设置cookie
    actions: {
        setUserInfo(userInfo: any) {
            this.userInfo = userInfo;
        },
        setActionType(actionType: string) {
            this.actionType = actionType;
        },
    },
});
