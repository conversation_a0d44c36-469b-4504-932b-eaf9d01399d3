<template>

    <div class="concat-us-box" :style="`--channel-count:${props.data?.length}`">
        <main-title-box :name='$t("pages.index.main.concat.title")' align="left"></main-title-box>
        <div class="concat-channels">
            <div class="concat-channel" v-for="(channel, index) of props.data" :key="channel.title">
                <div class="concat-icon-box">
                    <img :src="channel.icon" alt="" class="concat-icon">
                </div>
                <div class="concat-detail-box">
                    <div class="concat-title">{{ channel.title }}</div>
                    <div class="concat-desc">{{ channel.desc }}</div>
                </div>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">

interface Channel {
    title: string;
    icon: string;
    desc: string;
}

const props = defineProps<{
    data: Channel[]
}>();

</script>

<style lang="scss" scoped>
.concat-us-box {
    --channel-count: 3;
    padding: 20px 40px;
    max-width: 1440px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    flex-wrap: wrap;

    :deep(.title-box-wrapper) {
        margin-right: 20px;
        margin-block-end: 20px;
    }

    :deep(.title-box-name) {
        font-size: 38px;
        color: #fff;
    }

    :deep(.title-box-divider) {
        margin: 9px 0 0;
    }

}

.concat-channels,
.concat-channel {
    display: flex;
    align-items: center;
    flex: 1;
}


.concat-channels {
    max-width: 1023px;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: space-between;
}

.concat-channel {
    justify-content: start;
    max-width: calc(100% / var(--channel-count) - 10px);
}

.concat-icon-box {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    margin-right: 15px;
}

.concat-icon {
    width: 100%;
    height: 100%;
}

.concat-detail-box {
    display: flex;
    flex-direction: column;
    color: #fff;
    min-width: calc(100% - 70px);
}

.concat-title {
    color: #FFF;
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    /* 33px */
}

.concat-desc {
    color: #D9D9D9;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    /* 27px */
}

.concat-title,
.concat-desc {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}


@media screen and (max-width:768px) {


    .concat-title,
    .concat-desc {
        word-break: break-word;
        // white-space: unset;
    }

    .concat-us-box {
        padding: 20px 12% 20px;

        :deep(.title-box-wrapper) {
            margin-right: auto;
        }
    }

    .concat-us-box,
    .concat-channels {
        flex-direction: column;
        margin-right: auto;
    }

    .concat-channels{
        align-items: start;
        width: 100%;
    }

    .concat-channel {
        width: 100%;
        max-width: 100%;
    }

    .concat-detail-box{
        max-width: calc(76% - 70px);
    }
}
</style>