import URLS from "~/api/uri";
import { $axios } from "~/utils/axiosCustiom";
import {pbAppId} from "~/api/login";

/*
*
* proxy_app_id web view场景传对应账号中心web自己的app_id，期望使用账号中心的邮箱或短信模板配置时传该字段  错误 必须账户中心appid
*
* */
//=============================================================================非密保注销
// 创建注销
const noVerifyStep1 = (isWebview: boolean = false) => {
    let url = URLS.Login.postAuthStep1;
    return $axios({
        url,
        method: "post",
        data: {
            "auth_type": 1, // 1无需校验
            "operation": 31,
            proxy_app_id: isWebview ? pbAppId : 0,
        },
    });
};

// 注销账号
const noVerifyStep2 = (reason: string, isWebview: boolean = false) => {
    let url = URLS.Login.postAuthStep3;
    return $axios({
        url,
        method: "post",
        data: {
            "reason": reason, // 注销理由 "xxxx"
            "operation": 31,
            "auth_type": 1,
            proxy_app_id: isWebview ? pbAppId : 0,
        },
    });
};
//=============================================================================end

const cancelLogout = (token?: string) => {
    let url = URLS.Login.cancelLogout;
    if(token !== undefined){
        return $axios({
            url,
            method: "get",
            headers: {
                'Authorization': `Bearer ${token}`
            },
        });
    }
    return $axios({
        url,
        method: "get",
    });
};

// 验证码发送
const verifyStep1 = (authType: number, token: string, captcha_verification: string, isWebview: boolean = false) => {
    let url = URLS.Login.postAuthStep1;
    return $axios({
        url,
        method: "post",
        data: {
            /** 操作类型，2 修改密码、找回密码；3 绑定手机；4 绑定邮箱；13 解绑手机；解绑邮箱 14；更换密保手机 23；更换密保邮箱 24；账号注销 31 */
            operation: 31,
            /** 选择验证方式（修改密码时、注销账号可选 2 3 4，找回密码时可选 3 4，其他情况忽略该字段）	 */
            auth_type: authType,
            /** 图形验证码token */
            token: token,
            /** 图形验证码验证 */
            captcha_verification: captcha_verification,
            proxy_app_id: isWebview ? pbAppId : 0,
        },
    });
};

// 验证码验证
const verifyStep2 = (authType: number, code: string, isWebview: boolean = false) => {
    let url = URLS.Login.postAuthStep2;
    return $axios({
        url,
        method: "post",
        data: {
            "auth_type": authType, // 2密码校验 3手机校验 4邮箱校验
            "operation": 31,
            "password": code, // 若为手机或邮箱验证，则为验证码；修改密码则为验证码或原密码
            proxy_app_id: isWebview ? pbAppId : 0,
        },
    });
};

export {
    noVerifyStep1,
    noVerifyStep2,
    cancelLogout,
    verifyStep1,
    verifyStep2,
};
