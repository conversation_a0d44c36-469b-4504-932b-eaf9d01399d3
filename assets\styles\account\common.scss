.item.active,
.item:hover {

    .ac_icon {
        &.icon_1 {
            background-position: 0 0px;
        }

        &.icon_2 {
            background-position: 0 -25px;
        }

        &.icon_3 {
            background-position: 0 -50px;
        }

        &.icon_4 {
            background-position: 0 -75px;
        }
    }
}

.ac_icon {
    background-image: url(~/assets/images/account/icon_sprites.png);
    background-size: 60px 200px;
    background-repeat: no-repeat;

    &.edit {
        background-position: 0 -110px;
        height: 28px;
        width: 28px;
    }

    &.icon_1 {
        background-position: -30px 0px;
    }

    &.icon_2 {
        background-position: -30px -25px;
    }

    &.icon_3 {
        background-position: -30px -50px;
    }

    &.icon_4 {
        background-position: -30px -75px;
    }
}

.icon_bell {
    width: 22px;
    height: 24px;
    background: url(~/assets/images/account/bell.png) top center/100% 100% no-repeat;
}

.ac_common_box {
    border: 1px solid #E8E8E8;
    &.noBoxShadow {
        box-shadow: none;
        border: none;
    }
    .h3 {
        line-height: 50px;
        font-size: 16px;
        font-weight: 500;
        border-bottom: 1px solid #ececf0;
        text-align: center;
    }
    
}

.ac_pop_btn_line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px 20px;
}

.ac_c_mb20 {
    margin-bottom: 20px;
}

.ac_c_r20 {
    overflow: hidden;
    border-radius: 16px;
}

.ac_common_btn {
    min-width: 90px;
    padding: 0 10px;
    line-height: 32px;
    flex-shrink: 0;
    border-radius: 10px;
    background: #E6ECFC;
    text-align: center;
    color: #3A58A9;
    font-size: 14px;
    cursor: pointer;

    &:hover {
        opacity: 0.8;
    }
}

.ac_header {
    background: #E8ECFA;
    line-height: 64px;
    padding: 0 40px;
    color: #111;
    font-size: 18px;
}

.login_error_hint {
    min-height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #ff4d4f;
    &.success {
        color: #007406;
    }
}


.account_wrap {
    display: flex;
    justify-content: space-between;
    max-width: 1460px;
    width: 100%;
    margin: 0 auto;
    padding: 120px 30px 50px;
}

.account_right {
    position: relative;
    z-index: 1;
    flex: 1;
    padding-left: 40px;
    display: flex;
    flex-wrap: wrap;
    min-width: 0; // 添加这一行

    .h2 {
        width: 100%;
        color: #000;
        font-size: 24px;
        line-height: 40px;
        padding-bottom: 20px;
    }
}

.ac_common_pop_box {
    position: relative;
    z-index: 9;
    margin: 100px auto;
    max-width: 530px;
    width: 100%;
    background: #fff;
    padding: 40px 30px;
    border-radius: 20px;
}



.ac_profile_edit_form {
    min-height: 230px;
    padding: 20px 40px 0 40px;
}



.input_box {
    height: 50px;
    border: 1px solid var(--gray_color);
    border-radius: 10px;
    margin-bottom: 16px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    &:last-child {
        margin-bottom: 4px;
    }
    .input {
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        color: #111;
        border: none;
        flex: 1;

        &.ant-input-status-error {
            border-color: #ff4d4f;
        }

        &::placeholder {
            color: #999;
        }

        &:focus {
            border: none;
            background: #f0f0f0;
        }
    }
}

@media screen and (max-width: 1368px) {
    
    .account_right {
        padding-left: 30px;
    }
}

@media screen and (max-width: 1280px) {}

@media screen and (max-width: 980px) {

    .account_wrap {
        flex-direction: column;
        padding: 80px 20px 10px;
    }
    .account_right {
        padding-left: 0;
        .h2 {
            display: none;
        }
    }
}

@media screen and (max-width: 768px) {
    .account_wrap {
        padding: 60px 10px 10px;
    }
    .ac_header {
        font-size: 16px;
        line-height: 40px;
        padding: 0 20px;
    }
    .ac_pop_btn_line {
        flex-wrap: wrap;
        justify-content: center;
        .c_small_btn {
            margin-top: 10px;
            width: 100%;
        }
    }
    
    .ac_profile_edit_form {
        padding: 20px;
    }
    .ac_pop_btn_line {
        padding: 0 20px 20px;
    }
    .input_box {
        padding: 0 10px;
    }
}

@media screen and (max-width: 640px) {
}