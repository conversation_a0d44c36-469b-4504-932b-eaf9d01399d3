// import antd from 'ant-design-vue/es/locale-provider/zh_TW'
// import momentCN from 'moment/locale/zh-tw'
import global from './zh-Hant/global'

import menu from './zh-Hant/menu'
import setting from './zh-Hant/setting'
import user from './zh-Hant/user'
import account from './zh-Hant/account'
import pages from './zh-Hant/pages'

const components = {
    // antLocale: antd,
    momentName: 'zh-tw',
    // momentLocale: momentCN
}

export default {
    message: '-',
    'login.hint.txt':"本網站僅支持官方遊戲帳號進行登入。",
    'login.hint.txt2':"其他三方登入帳號（如Google登入、Facebook、蘋果登入等）請先前往遊戲內進行帳號綁定。",
    'login.error.email':'帳號或密碼錯誤，請重新輸入',
    'login.email.name':'帳號登入儲值',
    'layouts.usermenu.dialog.title': '信息',
    'layouts.usermenu.dialog.content': '您确定要注销吗？',
    'layouts.userLayout.title': 'Ant Design 是西湖区最具影响力的 Web 设计规范',
    'pay.credit.card':'信用卡',
    'pay.role.comfirm':'確認',
    'pay.how.get.roleId':'如何獲取角色ID？',
    'pay.comfirm.account':'確認訂單',
    'pay.comfirm.account.roleId':'請確認要儲值的角色ID或是帳號',
    'page.fetch.fail':'頁面不存在，請點擊回到{ XX }首頁{ YY }',
    'login.current.name':'最後上線時間',
    'login.role.name':'角色',
    'footer.policy.cookies':'Cookies政策',
    'footer.policy.refund':'退款政策',
    'footer.policy.website':'網站使用條款',
    'footer.policy.privacy':'隱私權政策',
    'footer.policy.website2':'遊戲服務合約',
    'footer.policy.privacy2':'隱私保護政策',
    'ux.txt.agree':'接受所有',
    'api.login.lose':'登录失效，请重新登录',
    'api.login.request.fail':'请求数据失败，请稍后再试',
    'common.policy.cookies':'我們使用cookies為您提供更佳的使用體驗。有些cookies對本網站的運行至關重要；有些則是幫助我們瞭解您是如何使用網站的，以便我們可以對其進行改進。按一下“接受所有”以同意我們的Cookies政策和隱私政策。',
    'common.comming.soon':'敬請期待',
    'pay.payment.mycard.select':'Mycard支付方式請選擇下方商品',
    'pay.payment.mycard.hint':'請選擇Mycard的商品',
    'pay.order.not.paid':'訂單未完成支付',
    'pay.order.comfirming.loading':'訂單確認中',
    'pay.order.comfirming.txt':'訂單確認中，請稍後在訂單列表查看訂單狀態。如遇儲值問題請"聯繫客服"',
    'pay.role.login.hint':'當前遊戲僅支持使用角色ID儲值',
    'cookies.policy.agree.all':'接受所有可選Cookie',
    'cookies.policy.disagree.all':'拒絕所有可選Cookie',
    'cookies.policy.manage':'確認我的選擇',
    'cookies.policy.confirm.my':'確認我的選擇',
    'cookies.policy.function.txt':'這些Cookie可幫助我們分析您對網站的使用情況，以評估和改善我們的性能。它們也可用於提供更好的客戶體驗。例如:記住您的登入詳細資訊，或向我們提供有關我們網站使用情況的資訊。',
    'cookies.policy.function.title':'功能Cookie',
    'cookies.policy.analysis.title':'分析Cookie',
    'cookies.policy.analysis.txt':'這些Cookie幫助我們了解訪客與網站的互動情況。我們可以從中得知訪問次數、流量來源以及訪客在網站上花費的時間，並以此來衡量網站的使用情況並進行優化。',
    'cookies.policy.base.title':'必要的Cookie',
    'cookies.policy.base.txt':'這些Cookie是網站正常運行所必須的，讓您能夠正常使用網站的功能，且不會從我們的系統中關閉',
    'cookies.policy.base.status':'始終處於活動狀態',
    'cookies.policy.center':'Cookie管理中心',
    'common.get.more':"了解更多",
    'pay.order.status.alipay.hint':'打開支付寶掃一掃付款',
    'pay.maintenance.hint': '頁面維護中，暫無法支付。',
    'pay.apple.only.hint': '當前支付方式僅支援 Apple 設備，請選擇其他支付方式。',
    'pay.order.status.processing': '訂單處理中，請稍後再試',
    'login.search.noData': '搜尋無結果',
    'login.choose.games':'選擇遊戲',
    ...components,
    ...global,
    ...menu,
    ...setting,
    ...user,
    ...account,
    ...pages
}
