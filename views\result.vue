<template>
    <div>
        <div class="order_banner">
            <img class="img banner_pc" :src="banner.pc" alt="" />
            <img class="img banner_mobile" :src="banner.mb" alt="" />
        </div>
    </div>
    <div class="order_result_wrap">
        <order-status :json="orderDetail" @setClose="orderStatusClose" :other-page="true"></order-status>
    </div>

</template>
<script lang="ts" setup>
import { useAuthStore } from '~/stores/auth.js';
const authStore = useAuthStore();
import { useUserStore } from "~/stores/user";
const { banner_pc, banner_mb } = useUserStore();
const banner = ref({
    pc: banner_pc,
    mb: banner_mb,
})

// 语言
const { locale } = useI18n();
const route = useRoute();

const orderDetail = useState(() => ({}))
onMounted(() => {
    // authStore.hideLoading();
    const { roleId, uid, orderId } = route.params;
    orderDetail.value = {
        roleId, uid, orderId
    }
})
const orderStatusClose = () => {

}

</script>

<style lang="scss" scoped>
.order_banner {
    width: 100%;
    min-height: 15.6vw;

    .img {
        width: 100%;
    }

}
.order_result_wrap {
    background: #fff;
}
</style>