<template>
    <!-- 忘记密码第一步验证 -->
    <div class="ac_c_r202 ac_common_box noBoxShadow r_password_step1"
        v-if="$props.type == 'forgetPassword' && step == 'step1'">
        <div class="c_logo_blue"></div>
        <div class="tab_name">{{ $t("account.forgotPassword.title") }}</div>
        <div class="input_wrap">
            <div class="input_box">
                <a-input class="input" tabindex="0" v-model:value="usernameEmail"
                    :placeholder="$t('account.forgotPassword.enterEmail')" :maxlength="64" allow-clear>
                </a-input>
            </div>
        </div>
        <div class="login_error_hint">{{ oErrorHint }}</div>
        <a-button class="login_btn" type="primary" :loading="submitLoading" :disabled="submitLoading"
            @click="checkResetPasswordUsername">{{ $t("account.forgotPassword.nextStep") }}</a-button>
        <div class="password_hint">
            {{ $t("account.forgotPassword.contactSupport") }}
            <a @click="goToPage('support', 'account', locale)">{{
                $t("account.forgotPassword.contactSupport2")
                }}</a>
        </div>
        <div class="login_hint_a" @click="goToLogin">
            {{ $t("account.login.accountLogin") }}
        </div>
    </div>

    <!-- 修改 + 设置新密码 -->
    <div class="ac_c_mb20 ac_c_r20 ac_check_email_phone ac_common_box noBoxShadow"
        v-if="step == 'step1' && $props.type == 'editPasswordAgain'">
        <div class="h3">{{ $t("account.security.changePassword") }}</div>
        <div class="ac_profile_edit_form">
            <div class="input_wrap">
                <div class="input_box">
                    <a-input-password class="input" tabindex="1" v-model:value="oOldPassword" :placeholder="$t('account.resetPassword.enterOriginalPassword')
                        " :maxlength="64" allow-clear>
                    </a-input-password>
                </div>
            </div>
            <p class="ac_profile_edit_desc" style="padding-top: 10px; margin-bottom: 0px">
                {{ $t("account.resetPassword.setNewPassword") }}
            </p>
            <div class="input_wrap">
                <div class="input_box">
                    <a-input-password class="input" tabindex="2" v-model:value="oPassword" :placeholder="$t('account.register.passwordRequirements')
                        " :maxlength="64" allow-clear>
                    </a-input-password>
                </div>
            </div>
            <div class="input_wrap">
                <div class="input_box">
                    <a-input-password class="input" tabindex="3" v-model:value="oPasswordConfirm"
                        :placeholder="$t('account.register.confirmPassword')" :maxlength="64" allow-clear>
                    </a-input-password>
                </div>
            </div>
            <div class="login_error_hint">{{ oErrorHint }}</div>
        </div>
        <div class="ac_pop_btn_line">
            <a-button class="login_btn" type="primary" :loading="submitLoading" :disabled="submitLoading"
                @click="editPassword">{{ $t("account.common.confirm") }}</a-button>
        </div>
    </div>

    <!-- 选择验证方式 -->
    <div class="ac_c_mb20 ac_c_r20 ac_check_email_phone ac_common_box noBoxShadow" v-if="step == 'step2'">
        <div class="h3">{{ $t("account.securityVerification.title") }}</div>
        <div class="ac_profile_edit_form">
            <p class="ac_profile_edit_desc">
                {{ $t("account.securityVerification.methods") }}
            </p>
            <div class="input_box">
                <a-select class="check_method_select" ref="select" :disabled="checkMethodChoiceList.length <= 1"
                    v-model:value="checkMethodChoiceValue" @change="checkMethodChoice" :options="checkMethodChoiceList">
                </a-select>
            </div>
            <p class="ac_profile_edit_desc">
                {{
                    $t(
                        checkMethodChoiceValue.value == "phone"
                            ? "account.securityVerification.phoneInstructions"
                            : "account.securityVerification.emailInstructions"
                    )
                }}
            </p>
            <!-- 邮箱验证码 -->
            <div v-if="checkMethodChoiceValue.value == 'email'">
                <div class="ac_profile_edit_desc">{{ bindEmail }}</div>
                <div class="input_box">
                    <a-input v-model:value="oEmailCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxlength="6">
                    </a-input>
                    <a-button class="captcha_ant_btn" :loading="captchaLoading" :disabled="captchaLoading ||!!countDownEmailCaptchaBtnText" @click="getCaptcha">{{
                        countDownEmailCaptchaBtnText
                            ? `${$t(
                                "account.common.sent"
                            )}（${countDownEmailCaptchaBtnText}s）`
                            : $t("account.common.get")
                    }}</a-button>
                </div>
            </div>
            <!-- 手机验证码 -->
            <div v-if="checkMethodChoiceValue.value == 'phone'">
                <div class="ac_profile_edit_desc">{{ bindPhone }}</div>
                <div class="input_box">
                    <a-input v-model:value="oPhoneCaptcha" :placeholder="$t(
                        'account.securityVerification.enterVerificationCode'
                    )
                        " size="large" :maxlength="6">
                    </a-input>
                    <a-button class="captcha_ant_btn" :loading="captchaLoading" :disabled="captchaLoading || !!countDownPhoneCaptchaBtnText" @click="getCaptcha">{{
                        countDownPhoneCaptchaBtnText
                            ? `${$t(
                                "account.common.sent"
                            )}（${countDownPhoneCaptchaBtnText}s）`
                            : $t("account.common.get")
                    }}</a-button>
                </div>
            </div>

            <div class="login_error_hint" :class="{ success: oErrorHintSuccess }">
                {{ oErrorHint }}
            </div>
        </div>
        <div class="ac_pop_btn_line">
            <a-button class="c_small_btn blue save_btn" :loading="submitLoading" :disabled="submitLoading"
                type="primary" @click="checkCaptcha">{{ $t("account.common.nextStep") }}</a-button>
        </div>
    </div>

    <!-- 设置新密码 -->
    <div class="ac_c_mb20 ac_c_r20 ac_check_email_phone ac_common_box noBoxShadow" v-if="step == 'step3'">
        <div class="h3">
            {{ $t("account.resetPassword.setNewPassword") }}
        </div>
        <div class="ac_profile_edit_form">
            <p class="ac_profile_edit_desc">
                {{ $t("account.profile.accountName") }}：{{
                    userInfo.username || usernameEmail
                }}
            </p>
            <div class="input_wrap">
                <div class="input_box">
                    <a-input-password class="input" tabindex="4" v-model:value="oPassword" :placeholder="$t('account.register.passwordRequirements')
                        " :maxlength="64" allow-clear>
                    </a-input-password>
                </div>
            </div>
            <div class="input_wrap">
                <div class="input_box">
                    <a-input-password class="input" tabindex="5" v-model:value="oPasswordConfirm"
                        :placeholder="$t('account.register.confirmPassword')" :maxlength="64" allow-clear>
                    </a-input-password>
                </div>
            </div>
            <div class="login_error_hint">{{ oErrorHint }}</div>
            <div class="ac_pop_btn_line">
                <a-button class="login_btn" type="primary" :loading="submitLoading" :disabled="submitLoading"
                    @click="checkResetPasswordPassword">{{ $t("account.common.confirm") }}</a-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {
    postResetPasswordMethod,
    postNoLoginStep1,
    postNoLoginStep2,
    postNoLoginStep3,
    postAuthStep1,
    postAuthStep2,
    postAuthStep3,
} from "~/api/login";
// 引入验证码
import { useYcCaptcha } from "~/utils/ycCaptcha";
const { initYcCaptcha } = useYcCaptcha();
// 登录态管理
import { useAuthStore } from "~/stores/auth";
import { useAccountStore } from "~/stores/account";
import { goToPageSwitch } from "~/utils/tools";
const authStore = useAuthStore();
const accountStore = useAccountStore();

// 用户信息
const userInfo: any = computed(() => accountStore.userInfo);

// 步骤的id
const step = ref<string>("step1");
const guid = ref<string>("");
const snda_id = ref<string>("");

// 登录态  loading
const submitLoading = ref<boolean>(false);
const captchaLoading = ref<boolean>(false);

const { locale, t } = useI18n();
const { $validator } = useNuxtApp();
const $v: any = $validator;

// 跳转页面
const goToPage = (pathVal: string, host: string, locale: any) => {
    goToPageSwitch(pathVal, host, locale);
    authStore.setLoginModal(false);
};

// 选择验证方式
const checkMethodChoiceValue = ref<{ label: string; value: string }>({
    label: "",
    value: "",
});
/**
 * @example
 * [
    {
        label: "手机号码验证",
        value: "phone",
    },
    {
        label: "邮箱验证",
        value: "email",
    },
]
 */
const checkMethodChoiceList = ref<any[]>([]);
// 换绑老邮箱验证码
const oEmailCaptcha = ref<string>("");
// 换绑老手机验证码
const oPhoneCaptcha = ref<string>("");
// 获取验证码倒计时
const countDownEmailCaptchaBtnText = ref<number | boolean>(false);
// 手机验证码倒计时
const countDownPhoneCaptchaBtnText = ref<number | boolean>(false);
// 绑定邮箱
const bindEmail = ref<string>("");
// 绑定手机
const bindPhone = ref<string>("");

const showModal = (status: boolean) => {
    authStore.setLoginModal(status);
};

// const oEmail = ref<string>('<EMAIL>');
const usernameEmail = ref<string>("");
const oPassword = ref<string>("");
const oOldPassword = ref<string>("");
const oPasswordStatus = ref<"error" | "warning" | "">("");
const oPasswordConfirm = ref<string>("");
const oPasswordConfirmStatus = ref<"error" | "warning" | "">("");
const oErrorHint = ref<string>(""); // 错误提示
const oErrorHintSuccess = ref<boolean>(false); // 成功提示

// 操作类型
const operation = ref<number>(0);
// 验证方式
const auth_type = ref<number>(0);

const $props = defineProps({
    needRedirect: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String,
        default: "forgetPassword",
    },
});
// 刷新页面
const $emit = defineEmits(["refreshUserInfo", "closeEdit"]);

// 返回登录
const goToLogin = () => {
    if ($props.needRedirect) {
        navigateTo(`/${locale.value}/account/login`);
    } else {
        $emit("closeEdit", "login");
    }
};
// =======================================================修改密码步骤

/**
 * step1 验证配置
 * @param operation 操作类型
 */
const postAuthStep1Fun = async (
    operation_num: number,
    auth_type_num: number = 0,
    token:string = '',
    captcha_verification:string = ''
) => {
    operation.value = operation_num;
    auth_type.value = auth_type_num;
    try {
        const res: any = await postAuthStep1({
            operation: operation.value,
            auth_type: auth_type.value,
            token,
            captcha_verification
        });
        console.log(res);
        if (res.code == 200 || res.code == 0) {
            guid.value = res.data.guid;
        }
        return res;
    } catch (error) {
        console.log(error);
    }
};

// step2
const postAuthStep2Fun = async (password: string) => {
    try {
        const res: any = await postAuthStep2({
            operation: operation.value || 2,
            auth_type:
                auth_type.value ||
                (checkMethodChoiceValue.value.value == "phone" ? 3 : 4),
            guid: guid.value,
            password,
        });
        console.log(res);
        if (res.code == 200 || res.code == 0) {
            return res;
        } else {
            oErrorHint.value = res.message;
        }
    } catch (error) {
        console.log(error);
    }
};

// step3
const postAuthStep3Fun = async (data: any) => {
    console.log("postAuthStep3Fun", data);
    try {
        const res: any = await postAuthStep3({
            guid: guid.value,
            operation: operation.value,
            auth_type: auth_type.value,
            ...data,
        });
        console.log(res);
        oErrorHintSuccess.value = false;
        if (res.code == 200 || res.code == 0) {
            guid.value = res.data.guid;
            return res;
        } else {
            oErrorHint.value = res.message;
        }
    } catch (error) {
        console.log(error);
    }
};
// 通过账密 修改密码 直接完成
const editPassword = async () => {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    if (!oOldPassword.value) {
        oErrorHint.value = t("account.resetPassword.enterOriginalPassword");
        return;
    }
    if (!oPassword.value) {
        oErrorHint.value = t("account.resetPassword.setNewPassword");
        return;
    }
    if (oOldPassword.value.length < 8 || oOldPassword.value.length > 20) {
        oErrorHint.value = t("account.password.originalIncorrect");
        return;
    }
    if (oPassword.value.length < 8 || oPassword.value.length > 20) {
        oErrorHint.value = t("account.register.passwordLengthInvalid");
        return;
    }
    if (oPassword.value != oPasswordConfirm.value) {
        oErrorHint.value = t("account.password.mismatch");
        return;
    }
    try {
        submitLoading.value = true;
        const res: any = await postAuthStep1Fun(2, 2);
        if (res.code == 200 || res.code == 0) {
            const res2: any = await postAuthStep2Fun(oOldPassword.value);
            if (res2.code == 200 || res2.code == 0) {
                const res3: any = await postAuthStep3Fun({
                    password: oPassword.value,
                    confirm_password: oPasswordConfirm.value,
                    guid: guid.value,
                });
                submitLoading.value = false;
                if (res3.code == 200 || res3.code == 0) {
                    authStore.logout(() => {
                        // 密码修改成功
                        $emit("closeEdit", "login");
                        message.success(t("account.password.changeSuccess"));
                        navigateTo(`/${locale.value}/account/login`);
                    });
                } else {
                    oErrorHint.value = res3.message;
                }
            } else {
                submitLoading.value = false;
                oErrorHint.value = res2.message;
            }
        } else {
            submitLoading.value = false;
            oErrorHint.value = res.message;
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};

// =================================================== 忘记密码步骤
// 第一步 获取用户邮箱账号是否可以找回密码
const checkResetPasswordUsername = async () => {
    oErrorHint.value = "";
    // 验证邮箱
    if (!usernameEmail.value) {
        oErrorHint.value = t('account.register.emailEmpty');
        return;
    }
    // 邮箱格式
    if (!$v.isEmail(usernameEmail.value)) {
        oErrorHint.value = t("account.login.emailInvalid");
        return;
    }
    try {
        submitLoading.value = true;
        const res: any = await postResetPasswordMethod(usernameEmail.value);
        submitLoading.value = false;
        console.log(res);
        if (res.code == 200 || res.code == 0) {
            let { is_bind_email, is_bind_phone, email, phone } = res.data;
            if (is_bind_email) {
                bindEmail.value = email;
                checkMethodChoiceList.value.push({
                    label: t("account.securityVerification.emailVerification"),
                    value: "email",
                });
            }
            if (is_bind_phone) {
                bindPhone.value = phone;
                checkMethodChoiceList.value.push({
                    label: t("account.securityVerification.phoneVerification"),
                    value: "phone",
                });
            }
            if (checkMethodChoiceList.value.length > 0) {
                checkMethodChoiceValue.value = checkMethodChoiceList.value[0];
            }
            step.value = "step2";
        } else {
            oErrorHint.value = res.message;
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};

// 第二步  切换验证方式
const checkMethodChoice = (value: any) => {
    checkMethodChoiceValue.value = checkMethodChoiceList.value.find(
        (item) => item.value == value
    );
};

const sendCaptcha = async () => {
    oErrorHint.value = t("account.verification.codeSent");
    oErrorHintSuccess.value = true;

    // 开启倒计时
    if (checkMethodChoiceValue.value.value == "email") {
        countDownEmailCaptchaBtnText.value = 60;
        const timer = setInterval(() => {
            (countDownEmailCaptchaBtnText.value as number)--;
            if (countDownEmailCaptchaBtnText.value as number <= 0) {
                clearInterval(timer);
                countDownEmailCaptchaBtnText.value = false;
            }
        }, 1000);
    } else if (checkMethodChoiceValue.value.value == "phone") {
        countDownPhoneCaptchaBtnText.value = 60;
        const timer = setInterval(() => {
            (countDownPhoneCaptchaBtnText.value as number)--;
            if (countDownPhoneCaptchaBtnText.value as number <= 0) {
                clearInterval(timer);
                countDownPhoneCaptchaBtnText.value = false;
            }
        }, 1000);
    }
};

// 第二步 发送验证码
const postStep1Fun = async (token:string = '',captcha_verification:string = '') => {
    // 判断邮箱验证码是否在倒计时
    if (
        checkMethodChoiceValue.value.value == "email" &&
        !!countDownEmailCaptchaBtnText.value
    ) {
        return;
    }
    // 判断手机验证码是否在倒计时
    if (
        checkMethodChoiceValue.value.value == "phone" &&
        !!countDownPhoneCaptchaBtnText.value
    ) {
        return;
    }
    try {
        oErrorHint.value = "";
        oErrorHintSuccess.value = false;
        captchaLoading.value = true;
        let res: any;
        if ($props.type == "forgetPassword") {
            operation.value = 2;
            auth_type.value =
                checkMethodChoiceValue.value.value == "phone" ? 3 : 4;
            res = await postNoLoginStep1({
                username: usernameEmail.value,
                operation: operation.value,
                auth_type: auth_type.value,
                token,
                captcha_verification
            });
        }
        if ($props.type == "editPasswordAgain") {
            res = await postAuthStep1Fun(
                2,
                checkMethodChoiceValue.value.value == "phone" ? 3 : 4,
                token,
                captcha_verification
            );
        }
        if (res.code == 200 || res.code == 0) {
            guid.value = res.data.guid;
            snda_id.value = res.data.snda_id;
            // 发送验证码
            sendCaptcha();
        } else {
            oErrorHint.value = res.message;
            if(res.code == 2044){
                initYcCaptcha(postStep1Fun);
            }
        }
        captchaLoading.value = false;
    } catch (error) {
        captchaLoading.value = false;
        console.log(error);
    }
};
// 第二步 发送验证码
const getCaptcha = () => {
    // 忘记密码的步骤
    postStep1Fun();
};

// 第二步 验证码验证
const postStep2Fun = async () => {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    if (
        checkMethodChoiceValue.value.value == "email" &&
        oEmailCaptcha.value.length != 6
    ) {
        oErrorHint.value = t("account.verification.codeInvalid");
        return;
    }
    if (
        checkMethodChoiceValue.value.value == "phone" &&
        oPhoneCaptcha.value.length != 6
    ) {
        oErrorHint.value = t("account.verification.codeInvalid");
        return;
    }
    try {
        submitLoading.value = true;
        let res: any;
        if ($props.type == "forgetPassword") {
            console.log(
                "===============================",
                checkMethodChoiceValue.value.value
            );
            console.log("=================", auth_type.value);
            res = await postNoLoginStep2({
                username: usernameEmail.value,
                guid: guid.value,
                password:
                    checkMethodChoiceValue.value.value == "email"
                        ? oEmailCaptcha.value
                        : oPhoneCaptcha.value,
                snda_id: snda_id.value,
                operation: operation.value || 2,
                auth_type:
                    auth_type.value ||
                    (checkMethodChoiceValue.value.value == "phone" ? 3 : 4),
            });
        }
        if ($props.type == "editPasswordAgain") {
            res = await postAuthStep2Fun(
                checkMethodChoiceValue.value.value == "email"
                    ? oEmailCaptcha.value
                    : oPhoneCaptcha.value
            );
        }
        submitLoading.value = false;
        if (res.code == 200 || res.code == 0) {
            step.value = "step3";
            return res;
        } else {
            oErrorHint.value = res.message;
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};
const checkCaptcha = async () => {
    // 忘记密码的步骤
    postStep2Fun();
};

// 第三步 确认新密码
const postStep3Fun = async () => {
    oErrorHint.value = "";
    oErrorHintSuccess.value = false;
    if (!oPassword.value) {
        oErrorHint.value = t("account.resetPassword.setNewPassword");
        return;
    }
    if (oPassword.value.length < 8 || oPassword.value.length > 20) {
        oErrorHint.value = t("account.register.passwordLengthInvalid");
        return;
    }
    if (oPassword.value != oPasswordConfirm.value) {
        oErrorHint.value = t("account.password.mismatch");
        return;
    }
    try {
        submitLoading.value = true;
        let res: any;
        if ($props.type == "forgetPassword") {
            res = await postNoLoginStep3({
                username: usernameEmail.value,
                guid: guid.value,
                password: oPassword.value,
                confirm_password: oPasswordConfirm.value,
                snda_id: snda_id.value,
                operation: operation.value || 2,
                auth_type:
                    auth_type.value ||
                    (checkMethodChoiceValue.value.value == "phone" ? 3 : 4),
            });
        }

        if ($props.type == "editPasswordAgain") {
            res = await postAuthStep3Fun({
                password: oPassword.value,
                confirm_password: oPasswordConfirm.value,
                guid: guid.value,
            });
        }
        submitLoading.value = false;
        if (res.code == 200 || res.code == 0) {
            authStore.logout(() => {
                // 密码修改成功
                if ($props.needRedirect && $props.type == "forgetPassword") {
                    // 跳转到登录页面
                    navigateTo(`/${locale.value}/account/login`);
                } else {
                    message.success(t("account.password.changeSuccess"));
                    $emit("closeEdit", "login");
                    if ($props.type == "editPasswordAgain") {
                        navigateTo(`/${locale.value}/account/login`);
                    }
                }
            });
        } else {
            oErrorHint.value = res.message;
        }
    } catch (error) {
        submitLoading.value = false;
        console.log(error);
    }
};
const checkResetPasswordPassword = async () => {
    // 忘记密码的步骤
    postStep3Fun();
};
onMounted(() => {
    // 判断是否是修改密码
    if ($props.type == "editPasswordAgain") {
        let { email, phone } = userInfo.value;
        if (email || phone) {
            if (email) {
                bindEmail.value = email;
                checkMethodChoiceList.value.push({
                    label: t("account.securityVerification.emailVerification"),
                    value: "email",
                });
            }
            if (phone) {
                bindPhone.value = phone;
                checkMethodChoiceList.value.push({
                    label: t("account.securityVerification.phoneVerification"),
                    value: "phone",
                });
            }
            if (checkMethodChoiceList.value.length > 0) {
                checkMethodChoiceValue.value = checkMethodChoiceList.value[0];
            }
            step.value = "step2";
        } else {
            step.value = "step1";
        }
    }
});
</script>
<style lang="scss">
@import url(~/assets/styles/account/checkEmailAndPhoneAll.scss);

.r_password_step1 {
    .input_wrap .input_box .input {
        margin-left: 0 !important;
    }
}
</style>
<style lang="scss" scoped>
@import url("~/assets/styles/account/login.scss");
@import url("~/assets/styles/account/common.scss");
@import url("~/assets/styles/account/checkEmailAndPhone.scss");

.password_hint {
    padding-top: 40px;
    color: #5f5f5f;
    text-align: center;
    font-size: 14px;

    a {
        color: #466df6;
    }
}

.c_logo_blue {
    margin: 0 auto;
}
</style>
