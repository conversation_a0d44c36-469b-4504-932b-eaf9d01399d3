// import antdEnUS from 'ant-design-vue/es/locale-provider/en_US'
// import momentEU from 'moment/locale/eu'
import global from './en/global'

import menu from './en/menu'
import setting from './en/setting'
import user from './en/user'
import account from './en/account'

import pages from './en/pages'

const components = {
  //antLocale: antdEnUS,
  momentName: 'eu',
  //momentLocale: momentEU
}

export default {
  message: '-',
  'login.hint.txt':"This website only supports logging in with official game email accounts.",
  'login.hint.txt2':"For other third-party login accounts (such as Google login, Facebook, Apple login, etc.), please first go to the game and bind your email.",
  'login.error.email':'Inccorect Email or password. Please enter again.',
  'login.email.name':'Login with Email',
  'layouts.usermenu.dialog.title': 'Message',
  'layouts.usermenu.dialog.content': 'Are you sure you would like to logout?',
  'layouts.userLayout.title': 'Ant Design is the most influential web design specification in Xihu district',
  'pay.credit.card':'Credit Card',
  'pay.role.comfirm':'Confirm',
  'pay.how.get.roleId':'How to find the character ID?',
  'pay.comfirm.account':'Confirm Order',
  'pay.comfirm.account.roleId':"Please confirm the character ID or account you'd like to purchase for",
  'page.fetch.fail':'The page is failed to load. Please return to the { XX }homepage{ YY }.',
  'login.current.name':'Last online',
  'login.role.name':'Character',
  'footer.policy.cookies':'Cookies Policy',
  'footer.policy.refund':'Refund Policy',
  'footer.policy.website':'Website Terms of Use',
  'footer.policy.privacy':'Privacy Policy',
  'footer.policy.website2':'Game Service Agreement',
  'footer.policy.privacy2':'Privacy Policy',
  'ux.txt.agree':'Accept All',
  'api.login.lose':'Login failed. Please try again.',
  'api.login.request.fail':'Data request failed. Please try again later.',
  'common.policy.cookies':'We use Cookies to provide you with a better browsing experience. Some Cookies are essential for the operation of this site; others help us understand how you use the site so that we can improve it. Click "Accept All" to agree our Cookies Policy and Privacy Policy.',
  'common.comming.soon':'Coming soon',
  'pay.payment.mycard.select':'For Mycard payment, please select the product below',
  'pay.payment.mycard.hint':'Please select a Mycard product',
  'pay.order.not.paid':'Order not yet paid',
  'pay.order.comfirming.loading':'Order to be confirmed',
  'pay.order.comfirming.txt':'The order is in the process of confirmation. Please check the order status in the Order List later.For payment issues, please contact customer service.',
  'pay.role.login.hint':'The game currently only supports paying with character ID.',
  'cookies.policy.agree.all':'Accept all',
  'cookies.policy.disagree.all':'Reject all',
  'cookies.policy.manage':'Manage Cookie',
  'cookies.policy.confirm.my':'Confirm',
  'cookies.policy.function.txt':'These cookies help us analyze your use of the website to evaluate and improve our performance. They can also be used to provide a better customer experience. For example: remembering your login details or providing us with information about how you use our website.',
  'cookies.policy.function.title':'Functional Cookie',
  'cookies.policy.analysis.title':'Analytic Cookie',
  'cookies.policy.analysis.txt':'These cookies help us understand how visitors interact with the website. We can learn about the number of visits, traffic sources, and the time spent on the website to measure and optimize website usage.',
  'cookies.policy.base.title':'Necessary Cookies',
  'cookies.policy.base.txt':'These cookies are essential for the website to function properly and allow you to use its features normally, and cannot be turned off from our system.',
  'cookies.policy.base.status':'Always Active',
  'cookies.policy.center':'Cookie Management Center',
  'common.get.more':"More details",
  'pay.order.status.alipay.hint':'Alipay Scan-to-Pay',
  'pay.maintenance.hint': 'The page is currently under maintenance. Payments are temporarily unavailable.',
  'pay.apple.only.hint': 'The current payment method is only supported on Apple devices. Please select another payment method.',
  'pay.order.status.processing': 'Order being processed. Please try again later.',
  'login.search.noData': 'No results found',
  'login.choose.games':'Select a Game',
  ...components,
  ...global,
  ...menu,
  ...setting,
  ...user,
  ...account,
  ...pages
}
