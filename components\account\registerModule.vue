<template>
    <div class="login_pop_main">
        <div class="c_logo_blue"></div>
        <div class="tab_name">{{ $t('account.register.accountRegistration') }}</div>
        <div class="input_wrap">
            <div class="input_box">
                <div class="c_icon_email"></div>
                <a-input
                    class="input"  tabindex="0"
                    v-model:value="oEmail"
                    :placeholder="$t('user.email.required')"
                    :maxlength="64"
                    allow-clear
                >
                </a-input>
            </div>
            <div class="input_box">
                <div class="c_icon_password"></div>
                <a-input-password
                    class="input"  tabindex="1"
                    :status="oPasswordStatus"
                    v-model:value="oPassword"
                    :placeholder="$t('account.register.passwordRequirements')"
                    :maxlength="64"
                    allow-clear
                />
            </div>
            <div class="input_box">
                <div class="c_icon_password"></div>
                <a-input-password  tabindex="2"
                    class="input"
                    :status="oPasswordConfirmStatus"
                    v-model:value="oPasswordConfirm"
                    :placeholder="$t('account.register.confirmPassword')"
                    :maxlength="64"
                    allow-clear
                />
            </div>
        </div>
        <div class="login_error_hint">{{ oErrorHint }}</div>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'zh-Hans'"
            >我已阅读并同意<a href="javascript:;" @click="goToPage('/policy/website')">游戏服务合约</a>和<a href="javascript:;" @click="goToPage('/policy/privacy')">隐私保护政策</a>
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'zh-Hant'"
            >我已閱讀並同意<a href="javascript:;" @click="goToPage('/policy/website')">遊戲服務合約</a>和<a href="javascript:;" @click="goToPage('/policy/privacy')">隱私保護政策</a>
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'ko'"
            >저는 <a href="javascript:;" @click="goToPage('/policy/website')">게임 서비스 계약</a> 및 <a href="javascript:;" @click="goToPage('/policy/privacy')">개인정보 보호정책</a>을 읽고 동의했습니다
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'ja'"
            ><a href="javascript:;" @click="goToPage('/policy/website')">ゲーム利用規約</a>と<a href="javascript:;" @click="goToPage('/policy/privacy')">プライバシーポリシ</a>ーに同意する
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'en'"
            >I have read and agreed to<a href="javascript:;" @click="goToPage('/policy/website')">the Game Service Agreement</a> and <a href="javascript:;" @click="goToPage('/policy/privacy')">Privacy Policy</a>
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'id'"
            >Saya telah membaca dan menyetujui <a href="javascript:;" @click="goToPage('/policy/website')">Perjanjian Layanan Game</a> dan <a href="javascript:;" @click="goToPage('/policy/privacy')">Kebijakan Privasi</a>
        </a-checkbox>
        <a-checkbox tabindex="3" v-model:checked="registerPolicy" v-if="locale == 'th'"
            >ฉันได้อ่านและยอมรับ<a href="javascript:;" @click="goToPage('/policy/website')">ข้อตกลงการให้บริการเกม</a>และ<a href="javascript:;" @click="goToPage('/policy/privacy')">นโยบายความเป็นส่วนตัว</a>
        </a-checkbox>
        <a-checkbox tabindex="4" v-model:checked="registerPolicy14YearOld" v-if="locale === 'ko'" >{{ $t('account.register.ageConfirmation') }}</a-checkbox>
        <a-button
            class="login_btn"
            style="margin-top: 20px;"
            type="primary"
            :loading="loginLoading"
            :disabled="loginLoading"
            @click="registerSubmit"
            >{{ $t('account.register.register') }}</a-button
        >
        <div class="login_hint_a" @click="goToLogin">{{ $t('account.register.alreadyHaveAccount') }}</div>

        <!-- 协议二次弹窗确认 -->
        <div class="pop_doubleHint" v-if="showPolicyConfirm">
            <p>{{ $t('account.register.termsConfirmation') }}</p>
            <div class="pop_btn_line">
                <div class="c_small_btn empty" @click="cancelPolicyConfirm">{{$t('account.common.return')}}</div>
                <a-button
                    class="c_small_btn blue"
                    type="primary"
                    :loading="loginLoading"
                    :disabled="loginLoading"
                    @click="registerPolicyConfirm"
                    >{{ $t('account.common.confirm') }}</a-button
                >
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { login, postRegister } from "~/api/login";
// 引入验证码
import { useYcCaptcha } from "~/utils/ycCaptcha";
const { initYcCaptcha } = useYcCaptcha();
// 登录态管理
import { useAuthStore } from "~/stores/auth";
const authStore = useAuthStore();

const $props = defineProps({
    needRedirect: {
        type: Boolean,
        default: false,
    },
});

// 登录态  loading
const loginLoading = ref<boolean>(false);

const { locale, t } = useI18n();
const { $validator } = useNuxtApp();
const $v: any = $validator;

const showModal = (status: boolean) => {
    authStore.setLoginModal(status);
};

// const oEmail = ref<string>('<EMAIL>');
const oEmail = ref<string>("");
const oPassword = ref<string>("");
const oPasswordStatus = ref<"error" | "warning" | "">("");
const oPasswordConfirm = ref<string>("");
const oPasswordConfirmStatus = ref<"error" | "warning" | "">("");
const oErrorHint = ref<string>(""); // 错误提示

// 协议状态开关
const registerPolicy = ref<boolean>(false);
const registerPolicy14YearOld = ref<boolean>(false);
// 协议二次弹窗确认
const showPolicyConfirm = ref<boolean>(false);

// 跳转页面
const goToPage = (path: string) => {
    $emit('closeEdit','close');
    navigateTo(`/${locale.value}${path}`);
}

// 注册
const registerSubmit = async () => {
    oEmail.value = oEmail.value?.trim();
    oPassword.value = oPassword.value?.trim();
    oPasswordConfirm.value = oPasswordConfirm.value?.trim();

    // 重置错误状态
    oPasswordStatus.value = "";
    oPasswordConfirmStatus.value = "";
    oErrorHint.value = "";

    // 验证邮箱
    if(!oEmail.value){
        oErrorHint.value = t('account.register.emailEmpty');
        return;
    }
    if (!$v.isEmail(oEmail.value)) {
        oErrorHint.value = t('account.register.emailInvalid');
        return;
    }

    // 验证密码
    const isValidPassword = (password: string): boolean => {
        // 至少包含一个字母、数字，可以容许包含特殊字符
        const passwordRegex =  /^[a-zA-Z0-9\-=,./;'\[\]\\*`+_<>!@#$%^&*()]{0,20}$/;
        return passwordRegex.test(password);
    };

    // 在 registerSubmit 函数中使用
    if (!oPassword.value) {
        oErrorHint.value = t('account.register.passwordEmpty');
        return;
    }
    if (oPassword.value.length < 8 || oPassword.value.length > 20) {
        oPasswordStatus.value = "error";
        oErrorHint.value = t('account.register.passwordLengthInvalid');
        return;
    }
    if (!isValidPassword(oPassword.value)) {
        oPasswordStatus.value = "error";
        oErrorHint.value = t('account.register.passwordInvalidChars');
        return;
    }

    // 验证确认密码
    if (oPassword.value !== oPasswordConfirm.value) {
        oPasswordConfirmStatus.value = "error";
        oErrorHint.value = t('account.password.mismatch');
        return;
    }

    // 验证协议
    if (!registerPolicy.value) {
        showPolicyConfirm.value = true;
        //oErrorHint.value = "请先阅读并同意游戏服务合约和隐私保护政策";
        return;
    }

    // 如果是韩国 需要验证 14岁
    if (locale.value === "ko" && !registerPolicy14YearOld.value) {
        showPolicyConfirm.value = true;
        // oErrorHint.value = t('account.register.termsAndAgeConfirmation');
        return;
    }

    // 注册
    oErrorHint.value = "";
    register();
};

/**
 * 协议二次弹窗确认
 */
const registerPolicyConfirm = () => {
    showPolicyConfirm.value = false;
    registerPolicy.value = true;
    registerPolicy14YearOld.value = true;
    registerSubmit();
};

/**
 * 协议二次弹窗取消
 */
const cancelPolicyConfirm = () => {
    showPolicyConfirm.value = false;
};

const $emit = defineEmits(['closeEdit']);

// 返回登录
const goToLogin = () => {
    if($props.needRedirect){
        navigateTo(`/${locale.value}/account/login`);
    }else{
        $emit('closeEdit','login');
    }
};

/**
 * 注册
 */
const register = async (token:string = '',captcha_verification:string = '') => {
    // 开始注册
    loginLoading.value = true;
    try {
        const result: any = await postRegister(
            oEmail.value,
            oPassword.value,
            oPasswordConfirm.value,
            token,
            captcha_verification
        );
        loginLoading.value = false;
        if (result.code === 200 || result.code === 0) {
            // 注册成功,自动登录
            const {username,token} = result.data;
            authStore.login(username,token);
            // 是否需要条状
            if($props.needRedirect){
                navigateTo(`/${locale.value}/account/profile`);
            }
        } else {
            oErrorHint.value = result.message;
            // 弹窗图形验证码
            if(result.code == 2044){
                initYcCaptcha(register);
            }
        }
    } catch (error) {
        console.error(error);
        oErrorHint.value = t('account.error.network');
        loginLoading.value = false;
    }
};
</script>
<style lang="scss">
.login_pop_main .ant-checkbox-wrapper {
    margin-left: 0;
    display: flex;
}
</style>
<style lang="scss" scoped>
@import url("~/assets/styles/account/login.scss");
</style>
