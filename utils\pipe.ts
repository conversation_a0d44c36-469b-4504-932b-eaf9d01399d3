/**
 * 管理cms数据格式化转换的一些工具方法
 */

import type { _AsyncData } from "nuxt/dist/app/composables/asyncData";

/**
 * 从useAsyncDataCustome中获取data数据
 */

export function formatResponseData(response: Ref<any>) {
    return response.value.data;
}

interface CMSResponse<T> {
    code: number;
    data: { list: T }
    message: string;
}

interface FormatCmsOptions {
    mode?: "simple" | 'all';
    richText?: boolean,
}
/**
 * 转换cms格式数据映射
 * @params cmsData cms响应元数据
 * @params dict 映射字典
 * @params options 映射配置  mode:simple 仅仅保留转换数据，all 保留全部元数据
 */
export function formatCmsData<T extends { [key: string]: any; sort_id: number }>(response: _AsyncData<CMSResponse<Array<T>>, any>, dict: Record<string, keyof T>, options?: FormatCmsOptions) {
    const data = response.data.value.data.list;
    return data.map(item => {
        const nItem: Record<string, any> = {};

        if (options?.mode === 'all') {
            Object.assign(nItem, item)
        }

        Object.keys(dict).forEach(itemKey => {

            const originKey = dict[itemKey];
            let originData = item[originKey];
            if (!options?.richText && originKey === 'ad_desc') {
                originData = (originData as string).replace(/(\<\/?[^>]+\>)/g, '') as unknown as any;
            }

            nItem[itemKey] = originData;
        });

        let groupKeys: Record<string, string> = {};

        item['meta']?.forEach((extraData: { key: string; value: string }) => {
            // 针对key为group开头的格式数据，将会格式化为数组，
            // 规则为[type]_[attrName]_[index]:[keyName] 如group_channels_[index]:icon
            // 会在顶层对象中生成一个属性值类型为数组，键名为attrName的属性，其中属性值数组的每一项对象键名为keyName，值为value
            const { key, value } = extraData;
            if (/^group_([a-z]+)_\d*\:[a-z0-9]+/ig.test(key)) {
                const [, attrName, indexWithkeyName] = key.split('_');
                const [index, keyName] = indexWithkeyName.split(':');
                groupKeys[attrName] = attrName;
                nItem[attrName] = nItem[attrName] || {};
                nItem[attrName][index] = nItem[attrName][index] || {};
                nItem[attrName][index][keyName] = value
            } else {
                nItem[key] = value
            }

        });

        // 将对象格式的group数据转换回数组
        Object.keys(groupKeys).forEach(key => {
            nItem[key] = Object.values(nItem[key]);
        })

        nItem['__sort'] = item['sort_id'] as number;

        return nItem;
    }).sort((a: any, b: any) => a.__sort - b.__sort);
}