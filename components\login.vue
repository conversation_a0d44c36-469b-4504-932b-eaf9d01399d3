<template>
    <div class="loginin_bar" @click="showModal(true)">
        <div class="c_icon_user"></div>
        <div class="txt">{{ $t('pages.pay.login') }}</div>
    </div>
    <a-modal v-model:visible="open" :width="530" :footer="null" :afterClose="() => { showModal(false) }"
        :destroyOnClose="true" :keyboard="true" class="login_pop_wrap">
        <account-login-module @changeAction="changeAction" v-if="isShow == 'login'"
            @closeAndOpenDeregister="closeAndOpenDeregister"></account-login-module>
        <account-register-module v-if="isShow == 'register'" @closeEdit="closeEdit"></account-register-module>
        <account-reset-password-module v-if="isShow == 'resetpassword'"
            @closeEdit="closeEdit"></account-reset-password-module>
        <accountDeregisterModule v-if="isShow == 'deregister'" :new-step-login-data="newStepLoginData"
            @changeAccount="changeAccount"></accountDeregisterModule>
    </a-modal>
</template>


<script lang="ts" setup>
// 登录态管理
import { useAuthStore } from '~/stores/auth';
import { getThirdConfigList } from '~/api/login';
const authStore = useAuthStore()

const { locale, t } = useI18n();
const { $validator } = useNuxtApp();
const $v: any = $validator;

const newStepLoginData = ref<any>({});
const closeAndOpenDeregister = (data: any) => {
    isShow.value = 'deregister';
    newStepLoginData.value = data;
}
const changeAccount = (data: any) => {
    isShow.value = 'login';
    newStepLoginData.value = {};
}

const showModal = (status: boolean) => {
    authStore.setLoginModal(status);
};

// 显示弹窗内容  login register
const isShow = ref<string>('login')

// 设置 cookie
const { token, user } = authStore.getPBAuthToken();

// 登录弹窗控制器
const open = ref<boolean>(false);

const changeAction = (action: string) => {
    console.log("action================", action)
    isShow.value = action;
}

// 关闭当前场景
const closeEdit = (switchName: string) => {
    if (switchName == "login") {
        isShow.value = "login";
    } else if (switchName == "close") {
        authStore.$patch({ loginModal: false });
    }
}

watchEffect(() => {
    // console.log("open================", authStore.loginModal)
    open.value = authStore.loginModal;
    isShow.value = authStore.loginModalStatus;
});


onBeforeMount(() => {
    if (import.meta.client) {
        authStore.$patch({ appLang: locale.value })
        // 判断登录态
        if (token && user) {
            // console.log("5555555", user.value, token.value)
            authStore.login(user, token)
            // console.log(authStore.isLogin)
        } else {
            getThirdConfigList();
        }
    }
    isShow.value = 'login'
});

</script>
<style lang="scss">
.login_pop_wrap {
    width: 530px !important;

    // .login_pop_main {
    //     width: 490px !important;
    // }

    .ant-modal-content {
        width: 530px;
        border-radius: 20px;
        background: #fff;
        padding: 40px 30px !important;

        .ant-input-affix-wrapper-status-error {
            border: 1px solid #ff4d4f !important;
        }
    }

    .ant-modal-body {
        padding: 0;
    }
}

@media screen and (max-width: 768px) {
    .login_pop_wrap {
        width: 350px !important;

        .login_pop_main {
            width: 310px !important;
        }

        .ant-modal-content {
            width: 350px;
            padding: 20px !important;
        }
    }
}
</style>
<style lang="scss" scoped>
.loginin_bar {
    display: flex;
    align-items: center;

    img {
        width: 20px;
        height: 22px;
    }

    .txt {
        margin-left: 10px;
    }
}

.login_pop_wrap {

    .c_logo_blue {
        margin: 0 auto;
    }
}
</style>